import React from 'react';
import {StyleProp, StyleSheet, Text, TextStyle} from 'react-native';
import theme from '../../styles-v2/theme';

const DEFAULT_FONT_SIZE = 16;
const DEFAULT_LINE_HEIGHT_SIZE = 18;

const getFontSize = (defaultSize: number, selectedSize: string) => {
  return {
    xs: defaultSize * 0.75, //12
    sm: defaultSize * 0.875, //14
    md: defaultSize, //16
    lg: defaultSize * 1.125, //18
    xl: defaultSize * 1.25, //20
    '2xl': defaultSize * 1.5, //24
    '3xl': defaultSize * 1.75, //28
    '4xl': defaultSize * 2, //32
    '5xl': defaultSize * 2.25, //36
    '6xl': defaultSize * 2.5, //40
    '7xl': defaultSize * 2.75, //44
    '8xl': defaultSize * 2.875, //46
    '9xl': defaultSize * 3, //48
  }[selectedSize];
};

const getLineHeightSize = (defaultSize: number, selectedSize: string) => {
  const fontsize = getFontSize(defaultSize, selectedSize);
  return fontsize ? fontsize + 2 : defaultSize + 2;
};

export type COLORS =
  | 'PRIMARY'
  | 'SECONDARY'
  | 'DISABLED'
  | 'SUCCESS'
  | 'WARNING'
  | 'ERROR'
  | 'TERTIARY'
  | 'EDITOR_LIGHT_BLACK';
export type SIZES = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl';
export type FONT_WEIGHTS = 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';

type TextProps = {
  color?: COLORS;
  fontSize?: SIZES;
  lineHeight?: SIZES;
  fontWeight?: FONT_WEIGHTS;
  style?: StyleProp<TextStyle>;
  numberOfLines?: number; // Added numberOfLines
};
type ComputedStyles = {
  text: StyleProp<TextStyle>;
};

const styles = StyleSheet.create({
  text: {
    fontFamily: theme.FONT_FAMILY_BODY,
  },
});

const TextElement = React.forwardRef<Text, TextProps & {children?: any}>((props, ref) => {
  let {color = 'PRIMARY', fontSize = 'md', lineHeight, fontWeight = '400', style, numberOfLines} = props; // Added numberOfLines

  const derivedfontSize = getFontSize(DEFAULT_FONT_SIZE, fontSize);
  const derivedLineHeight = getLineHeightSize(DEFAULT_LINE_HEIGHT_SIZE, lineHeight ? lineHeight : fontSize);
  const textColor = theme[`${color}_COLOR`];

  let computedStyles: ComputedStyles = {
    text: {color: textColor, fontSize: derivedfontSize, lineHeight: derivedLineHeight, fontWeight: fontWeight},
  };

  return <Text style={[styles.text, computedStyles.text, style]} numberOfLines={numberOfLines}>{props.children}</Text>; // Added numberOfLines prop
});

export default TextElement;
