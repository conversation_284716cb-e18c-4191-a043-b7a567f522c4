import React, {useState} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity, Linking} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import {useSelector, useDispatch} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {makeToast} from '@/root/web/actions/toastActions';
import TextElement from '@/root/web/components-v2/base/TextElement';
import packageJson from '@/root/package.json';
import Analytics from '@/root/web/lib/segment';
import CheckBoxWidget from './checkBoxWidget';
import RadioGroup from './radioGroup';
import Tooltip from './tooltip';
import ConfirmationModal from './confirmationModal';
import Icon from 'react-native-vector-icons/AntDesign';

interface PlaystoreDetailsProps {
  onPublish: () => void;
  isIOSBuildRequested: boolean;
  helpDocs: any[];
  setMainDocRef: React.MutableRefObject<(doc: any) => void>;
  tooltipVisible: number | null;
  setTooltipVisible: (value: number | null) => void;
}

const PlaystoreDetails: React.FC<PlaystoreDetailsProps> = ({
  onPublish,
  isIOSBuildRequested,
  helpDocs,
  setMainDocRef,
  tooltipVisible,
  setTooltipVisible,
}) => {
  const {buildDetails, appDetails, setBuildDetails, alreadyExistingMetaData, setAlreadyExistingMetaData} =
    usePublishContext();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const {user} = useSelector((state: EditorRootState) => state.user);

  const {
    hasExistingGoogleDeveloperAccount,
    hasExistingGoogleApp,
    isAndroidBuildRequested,
  } = buildDetails.android;

  const [isGoogleDisabled, setGoogleDisabled] = useState(isAndroidBuildRequested);
  const [requestConfirmationLoader, setRequestConfirmationLoader] = useState(false);
  const [isGoogleBuildModalVisible, setIsGoogleBuildModalVisible] = useState(false);
  const [isGoogleInvitedChecked, setGoogleInviteChecked] = useState(false);

  const isAndroidRequestDisabled =
    buildDetails.android.hasExistingGoogleDeveloperAccount === 'Yes' &&
    buildDetails.android.hasExistingGoogleApp === 'No'
      ? !buildDetails.android.playStoreDevAccountName
      : buildDetails.android.hasExistingGoogleApp === 'Yes'
      ? !(
          buildDetails.android.androidBundleId &&
          buildDetails.android.androidPreviousVersion &&
          buildDetails.android.androidPreviousSemver &&
          buildDetails.android.playStoreDevAccountName
        )
      : false;

  const updateAndroidDetails = (key: string, value: any) => {
    setBuildDetails((prev: any) => ({
      ...prev,
      android: {
        ...prev.android,
        [key]: value,
      },
    }));
  };

  const openPlayStore = () => {
    Linking.openURL('https://play.google.com/console');
  };

  const incrementVersionAndSemver = async (semVersion: string, version: string) => {
    let semVersionArray = semVersion.split('.').map(Number);

    // Ensure semVersion has three parts (x.y.z)
    while (semVersionArray.length < 3) {
      semVersionArray.push(0);
    }

    // Increment the major version and reset minor & patch versions
    semVersionArray[0] += 1;
    semVersionArray[1] = 0;
    semVersionArray[2] = 0;

    return {
      semVersion: semVersionArray.join('.'),
      version: (Number.parseInt(version ?? '0') + 1).toString(),
    };
  };

  const handleBuild = async (platform: 'android' | 'ios', version: string, semver: string) => {
    if (!user) return;
    
    Analytics.track('editor:buildpublishflow_BuildTriggered', {
      appId,
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    });

    const buildPayload = {
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    };

    const response = await BuildManagerApi.createBuild(appId as string, buildPayload);
    let responseData;

    // Parse response data, if it's a string
    if (typeof response.data === 'string') {
      try {
        responseData = JSON.parse(response.data);
      } catch (error) {
        console.error('Failed to parse response data as JSON:', error);
        return;
      }
    } else {
      responseData = response.data;
    }

    const {errors, info} = responseData;

    // Handle info messages and check for pending build
    info?.forEach((message: string) => {
      dispatch(
        makeToast({
          content: message,
          appearances: 'info',
          duration: 30000,
        }),
      );
    });

    // Handle error messages
    errors?.forEach((message: string) => {
      dispatch(
        makeToast({
          content: message,
          appearances: 'error',
          duration: 30000,
        }),
      );
    });
  };

  const handleAndroidConfirmBuild = async () => {
    try {
      Analytics.track('editor:buildpublishflow_buildConfirmationAndroid');
      setRequestConfirmationLoader(true);

      const updatedAndroidDetails = {...buildDetails.android, isAndroidBuildRequested: true};
      updateAndroidDetails('isAndroidBuildRequested', true);

      if (buildDetails.android.hasExistingGoogleApp === 'Yes') {
        await BuildManagerApi.patchAppSettings(appId as string, {
          androidBundleIdentifier: updatedAndroidDetails.androidBundleId,
        });
      }

      const newMetaData = {
        hasExistingGoogleApp: updatedAndroidDetails.hasExistingGoogleApp === 'Yes' ? true : false,
        androidPreviousSemver: updatedAndroidDetails.androidPreviousSemver,
        androidPreviousVersion: updatedAndroidDetails.androidPreviousVersion,
        isAndroidBuildRequested: updatedAndroidDetails.isAndroidBuildRequested,
        hasExistingGoogleDeveloperAccount:
          updatedAndroidDetails.hasExistingGoogleDeveloperAccount === 'Yes' ? true : false,
        playStoreDevAccountName: updatedAndroidDetails.playStoreDevAccountName
          ? updatedAndroidDetails.playStoreDevAccountName
          : 'Apptile',
      };

      await BuildManagerApi.patchAppMetadata(appId as string, {
        metadata: {
          ...alreadyExistingMetaData,
          android: newMetaData,
        },
      });

      setAlreadyExistingMetaData({
        ...alreadyExistingMetaData,
        android: newMetaData,
      });

      BuildManagerApi.sendBuildAlert(appId as string, {appId, appName: appDetails.appName, android: newMetaData});

      if (!isIOSBuildRequested) {
        onPublish();
      }

      if (updatedAndroidDetails.hasExistingGoogleApp === 'Yes') {
        const {version, semVersion} = await incrementVersionAndSemver(
          updatedAndroidDetails.androidPreviousSemver,
          updatedAndroidDetails.androidPreviousVersion,
        );
        await handleBuild('android', version, semVersion);
      } else {
        await handleBuild('android', '1', '1.0.0');
      }

      setRequestConfirmationLoader(false);
      setIsGoogleBuildModalVisible(false);
    } catch (error) {
      setRequestConfirmationLoader(false);
      updateAndroidDetails('isAndroidBuildRequested', false);
      console.error('Error during build confirmation:', error);
      setIsGoogleBuildModalVisible(false);
      dispatch(
        makeToast({
          content: 'Unknown Error Occured! Please try after some time.',
          appearances: 'error',
          duration: 5000,
        }),
      );
    }
  };

  return (
    <>
      {/* Google Developer Section - Direct Display */}
      <View style={styles.innerCard}>
        {isAndroidBuildRequested ? (
          <>
            {/* Success Message */}
            <View style={styles.successCard}>
              <Icon name="checkcircle" size={30} color={'#6AC370'} />
              <View style={{gap: 5, alignItems: 'center'}}>
                <TextElement style={styles.successMessage} color="SECONDARY">
                  Android build request has been submitted successfully.
                </TextElement>
                <TextElement style={styles.successMessage} color="SECONDARY">
                  Our team will get back to you shortly
                </TextElement>
              </View>
            </View>

            {(hasExistingGoogleDeveloperAccount === true || hasExistingGoogleDeveloperAccount === 'Yes') && (
              <View style={{position: 'relative'}}>
                <TextElement style={[styles.subHeading, {marginBottom: 10}]}>
                  Play Store Developer Account Name
                </TextElement>
                <TextInput
                  style={[styles.input, styles.disabledInput]}
                  value={buildDetails?.android?.playStoreDevAccountName || ''}
                  editable={false}
                  pointerEvents="none"
                />
              </View>
            )}

            {/* Only Show Disabled Inputs if Existing App Details were Entered */}
            {(hasExistingGoogleApp === true || hasExistingGoogleApp === 'Yes') && (
              <>
                <TextElement style={[styles.heading, {fontSize: 16}]}>Account Details</TextElement>
                {['Package Name', 'Version Number', 'Version Semver'].map((placeholder, index) => {
                  const stateKey = {
                    0: 'androidBundleId',
                    1: 'androidPreviousVersion',
                    2: 'androidPreviousSemver',
                  }[index] as keyof typeof buildDetails.android;

                  return (
                    <View
                      key={`google-disabled-${index}`}
                      style={{position: 'relative'}}
                      onMouseEnter={() => setTooltipVisible(index)}
                      onMouseLeave={() => setTooltipVisible(null)}>
                      <TextElement style={styles.subHeading}>{placeholder}</TextElement>
                      <TextInput
                        style={[styles.input, styles.disabledInput]}
                        placeholder={`Add ${placeholder} here`}
                        value={buildDetails.android[stateKey] as string}
                        editable={false}
                        pointerEvents="none"
                      />
                      {tooltipVisible === index && (
                        <Tooltip message="Account details can't be changed after build is requested." visible />
                      )}
                    </View>
                  );
                })}
              </>
            )}
          </>
        ) : (
          <>
            {/* Developer Account Question */}
            <TextElement style={[styles.subHeading, {fontSize: 16}]} fontSize="sm">
              Do you have a Google Developer Account?
            </TextElement>

            <View
              style={{
                marginVertical: 10,
                flexDirection: 'row',
                position: 'relative',
              }}>
              <RadioGroup
                options={[
                  {label: 'Yes', value: 'Yes'},
                  {label: 'No', value: 'No'},
                ]}
                selectedValue={hasExistingGoogleDeveloperAccount}
                customStyles={{}}
                onSelect={(value: string | boolean) => {
                  setMainDocRef.current(helpDocs[5]);
                  updateAndroidDetails('hasExistingGoogleDeveloperAccount', value);
                }}
              />

              <View onMouseEnter={() => setTooltipVisible(24)} onMouseLeave={() => setTooltipVisible(null)}>
                {tooltipVisible === 24 && (
                  <Tooltip
                    message="Choosing No will use Apptile developer account for publishing your app"
                    visible
                  />
                )}
                <MaterialCommunityIcons name="information-outline" color="#000000" size={18} />
              </View>
            </View>

            {hasExistingGoogleDeveloperAccount === 'No' ? (
              <TouchableOpacity
                style={isAndroidRequestDisabled ? styles.requestButtonDisabled : styles.requestButton}
                disabled={isAndroidRequestDisabled}
                onPress={() => setIsGoogleBuildModalVisible(true)}>
                <TextElement style={styles.buttonText} fontSize="sm">
                  Request Android Build
                </TextElement>
              </TouchableOpacity>
            ) : (
              <>
                <View style={styles.infoBox}>
                  <MaterialCommunityIcons name="information-outline" color="#000000" size={18} />
                  <TextElement style={styles.infoText}>
                    Please share Owner Access to your Google Developer Account with{' '}
                    <TextElement style={styles.boldText}><EMAIL></TextElement> before you go forward.
                  </TextElement>
                </View>
                <View
                  style={[
                    styles.linkContainer,
                    {borderBottomColor: '#F5F5F5', borderBottomWidth: 2, paddingBottom: 15},
                  ]}>
                  <MaterialCommunityIcons name="link-variant" color="#1060E0" size={20} />
                  <TouchableOpacity>
                    <Text style={{fontWeight: '600'}}>
                      Go to{' '}
                      <Text style={styles.link} onPress={openPlayStore}>
                        Google Play Console
                      </Text>{' '}
                    </Text>
                  </TouchableOpacity>
                </View>

                {!isAndroidBuildRequested && (
                  <CheckBoxWidget
                    label="I have invited and shared <NAME_EMAIL>"
                    checked={isGoogleInvitedChecked}
                    isDisabled={false}
                    onChange={setGoogleInviteChecked}
                  />
                )}
                {isGoogleInvitedChecked && (
                  <>
                    {/* Existing App Section */}
                    <View style={{borderBottomColor: '#F5F5F5', borderBottomWidth: 2, marginBottom: 10}}>
                      <TextElement style={[styles.subHeading, {fontSize: 16}]}>
                        Do you have an Existing App?
                      </TextElement>

                      <View style={{marginVertical: 10}}>
                        <RadioGroup
                          options={[
                            {label: 'Yes', value: 'Yes'},
                            {label: 'No', value: 'No'},
                          ]}
                          selectedValue={hasExistingGoogleApp}
                          customStyles={{}}
                          onSelect={(value: string | boolean) => updateAndroidDetails('hasExistingGoogleApp', value)}
                        />
                      </View>
                    </View>

                    {hasExistingGoogleDeveloperAccount === 'Yes' && (
                      <View style={{borderBottomColor: '#F5F5F5', borderBottomWidth: 2, marginBottom: 10}}>
                        <TextElement style={[styles.subHeading, {fontSize: 15}, {marginBottom: 10}]}>
                          Account Details
                        </TextElement>
                        <View>
                          <TextElement style={[styles.subHeading, {marginBottom: 10}]}>
                            Play Store Developer Account Name
                          </TextElement>
                          <TextInput
                            value={buildDetails.android.playStoreDevAccountName}
                            onChangeText={(value: string) => {
                              updateAndroidDetails('playStoreDevAccountName', value);
                            }}
                            style={[styles.input, isGoogleDisabled && styles.disabledInput]}
                            placeholder="Add Play Store Developer Account Name here"
                            editable={!isGoogleDisabled}
                            placeholderTextColor="#939393"
                            onFocus={() => {
                              setMainDocRef.current(helpDocs[5]);
                            }}
                          />
                        </View>
                      </View>
                    )}

                    {hasExistingGoogleApp === 'Yes' && (
                      <>
                        <TextElement style={[styles.subHeading, {fontSize: 15}, {marginBottom: 10}]}>
                          Previous App Details
                        </TextElement>

                        {[
                          {label: 'Package Name', placeholder: 'com.yourappname.app'},
                          {label: 'Version Number', placeholder: '10'},
                          {label: 'Version Semver', placeholder: '2.0.0'},
                        ].map((input, index) => {
                          const stateKey = {
                            0: 'androidBundleId',
                            1: 'androidPreviousVersion',
                            2: 'androidPreviousSemver',
                          }[index] as keyof typeof buildDetails.android;

                          return (
                            <View key={'google' + input.label}>
                              <TextElement style={styles.subHeading}>{input.label}</TextElement>
                              <TextInput
                                key={`google-${index}`}
                                value={(buildDetails.android[stateKey] as string) ?? ''}
                                onChangeText={(value: string) => {
                                  updateAndroidDetails(stateKey, value);
                                }}
                                style={[styles.input, isGoogleDisabled && styles.disabledInput]}
                                placeholder={`${input.placeholder}`}
                                editable={!isGoogleDisabled}
                                placeholderTextColor={'#939393'}
                                onFocus={() => {
                                  setMainDocRef.current(helpDocs[3]);
                                }}
                              />
                            </View>
                          );
                        })}
                      </>
                    )}

                    <TouchableOpacity
                      style={isAndroidRequestDisabled ? styles.requestButtonDisabled : styles.requestButton}
                      disabled={isAndroidRequestDisabled}
                      onPress={() => setIsGoogleBuildModalVisible(true)}>
                      <TextElement style={styles.buttonText} fontSize="sm">
                        Request Android Build
                      </TextElement>
                    </TouchableOpacity>
                  </>
                )}
              </>
            )}
          </>
        )}
      </View>

      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={isGoogleBuildModalVisible}
        message="App & Account details can't be edited after the build is requested. Do you wish to Continue?"
        onCancel={() => setIsGoogleBuildModalVisible(false)}
        onConfirm={handleAndroidConfirmBuild}
        isLoaderActive={requestConfirmationLoader}
      />
    </>
  );
};

const styles = StyleSheet.create({
  innerCard: {
    borderRadius: 12,
    marginVertical: 10,
  },
  successCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    height: 140,
    marginBottom: 20,
    gap: 10,
  },
  successMessage: {
    fontSize: 16,
    fontWeight: '400',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginVertical: 15,
  },
  subHeading: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginVertical: 10,
  },
  input: {
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    marginBottom: 15,
    backgroundColor: '#F3F3F3',
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
    color: '#A0A0A0',
  },
  infoBox: {
    backgroundColor: '#F3F3F3',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#D9D9D9',
    marginBottom: 20,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    fontWeight: '200',
  },
  infoText: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 20,
    fontWeight: '400',
  },
  boldText: {
    fontWeight: '600',
    color: '#333',
  },
  linkContainer: {
    flexDirection: 'row',
    gap: 10,
    margin: 5,
  },
  link: {
    color: '#1060E0',
    fontSize: 14,
    fontWeight: 500,
    textDecorationLine: 'underline',
    marginBottom: 15,
  },
  requestButton: {
    backgroundColor: '#1060E0',
    fontWeight: 200,
    width: 'max-content',
    minWidth: 150,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  requestButtonDisabled: {
    backgroundColor: '#A9A9A9',
    fontWeight: '200',
    width: 'max-content',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginVertical: 20,
    opacity: 0.6,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

export default PlaystoreDetails;
