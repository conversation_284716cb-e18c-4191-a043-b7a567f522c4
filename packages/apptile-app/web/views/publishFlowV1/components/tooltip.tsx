import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {darkTheme} from '../styles';

interface TooltipProps {
  message: string;
  visible: boolean;
}

const Tooltip: React.FC<TooltipProps> = ({message, visible}) => {
  if (!visible) return null;

  return (
    <View style={styles.tooltipContainer}>
      <View style={styles.tooltip}>
        <Text style={styles.tooltipText}>{message}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tooltipContainer: {
    zIndex: 9999,
    // position: 'absolute',
    bottom: 30,
    // Move it above the icon instead of below
    left: '50%',

    alignItems: 'center',
    position: 'absolute',
    // transform:
    transform: 'translateX(-50%)',
  },
  tooltipArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6, // Flip the arrow to point downwards
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#000', // Arrow color
    marginTop: 2,
  },
  tooltip: {
    backgroundColor: darkTheme.tooltipBackground,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: darkTheme.tooltipBorder,
    width: 250,
  },
  tooltipText: {
    color: darkTheme.tooltipText,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default Tooltip;
