import React, {useState, useEffect} from 'react';
import Analytics from '@/root/web/lib/segment';
import {View, Text, StyleSheet, TextInput, Image, ScrollView} from 'react-native';
import FileUploadButton from './fileUploadButton';
import {MaterialCommunityIcons} from 'apptile-core';
import {BuildManagerApi} from '../../../api/BuildApi';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {usePublishContext} from '../context';
import Footer from './footer';
import {ActivityIndicator} from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';

const AppDetails: React.FC = () => {
  const {
    appDetails,
    setAppDetails,
    initialAppDetails,
    setInitalAppDetails,
    formLoading,
    doesMetaDataExist,
    setMetaDataExist,
    isLegacyApp,
  } = usePublishContext();
  const {appName, subtitle, description, privacyPolicy, iconUri, splashUri, supportURL} = appDetails;

  const appId = useSelector((state: EditorRootState) => state.apptile.appId) as string;
  const updateAppDetails = (updates: Partial<typeof appDetails>) => {
    // Track each field update
    if (updates.appName !== undefined) {
      Analytics.track('editor:buildpublishflow_appNameChanged', {value: updates.appName});
    }
    if (updates.subtitle !== undefined) {
      Analytics.track('editor:buildpublishflow_subtitleChanged', {value: updates.subtitle});
    }
    if (updates.description !== undefined) {
      Analytics.track('editor:buildpublishflow_descriptionChanged', {value: updates.description});
    }
    if (updates.privacyPolicy !== undefined) {
      Analytics.track('editor:buildpublishflow_privacyPolicyChanged', {value: updates.privacyPolicy});
    }
    if (updates.supportURL !== undefined) {
      Analytics.track('editor:buildpublishflow_supportUrlChanged', {value: updates.supportURL});
    }
    if (updates.iconUri !== undefined) {
      Analytics.track('editor:buildpublishflow_iconUploaded', {});
    }
    if (updates.splashUri !== undefined) {
      Analytics.track('editor:buildpublishflow_splashUploaded', {});
    }
    setAppDetails(prev => ({...prev, ...updates}));
  };

  const [errors, setErrors] = useState({
    appName: false,
    subtitle: false,
    description: false,
    privacyPolicy: false,
    supportURL: false,
  });
  const [isFileUploading, setIsFileUploading] = useState(false);
  const handleBlur = (field: 'appName' | 'subtitle |description| privacyPolicy' | 'supportURL', value: string) => {
    setErrors(prev => ({
      ...prev,
      [field]: value.trim() === '',
    }));
  };

  function requestAssetChange() {
    if (iconUri && iconUri !== initialAppDetails.iconUri) {
      BuildManagerApi.sendAssetChangeAlert(appId, {appId, appName: appDetails.appName, changeType: 'icon'});
      setInitalAppDetails(prev => {
        return {
          ...prev,
          iconUri,
        };
      });
    }

    if (splashUri && splashUri !== initialAppDetails.splashUri) {
      BuildManagerApi.sendAssetChangeAlert(appId, {appId, appName: appDetails.appName, changeType: 'splash'});
      setInitalAppDetails(prev => {
        return {
          ...prev,
          splashUri,
        };
      });
    }
  }

  useEffect(() => {
    requestAssetChange();
  }, [iconUri, splashUri]);

  const handleSubmit = async () => {
    Analytics.track('editor:buildpublishflow_submitClicked', {});
    if (!appId) return;
    const changedAppDetails = {};
    if (initialAppDetails.appName !== appDetails.appName) {
      changedAppDetails.appName = appDetails.appName;
    }
    if (initialAppDetails.subtitle !== appDetails.subtitle) {
      changedAppDetails.appSubtitle = appDetails.subtitle;
    }
    if (initialAppDetails.description !== appDetails.description) {
      changedAppDetails.appDescription = appDetails.description;
    }

    if (initialAppDetails.privacyPolicy !== appDetails.privacyPolicy) {
      changedAppDetails.privacyPolicy = appDetails.privacyPolicy;
    }

    if (initialAppDetails.supportURL !== appDetails.supportURL) {
      changedAppDetails.supportURL = appDetails.supportURL;
    }

    if (Object.keys(changedAppDetails).length === 0) return;

    if (changedAppDetails.appName) {
      await BuildManagerApi.patchAppSettings(appId, {
        displayName: changedAppDetails.appName,
      });
    }
    if (!doesMetaDataExist) {
      await BuildManagerApi.postAppMetadata(appId, {...changedAppDetails, appName: appDetails.appName});
      setMetaDataExist(true);
      setInitalAppDetails(appDetails);
    } else {
      await BuildManagerApi.patchAppMetadata(appId, changedAppDetails);
      setInitalAppDetails(appDetails);
    }
  };

  return (
    <>
      <View style={styles.wrapper}>
        {/* Left Panel */}

        <ScrollView
          style={styles.leftPanel}
          contentContainerStyle={styles.leftPanelContent}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}>
          <View style={!formLoading ? styles.card : [styles.card, {alignItems: 'center', justifyContent: 'center'}]}>
            {formLoading && <ActivityIndicator size={'large'} />}
            {/* App Name */}
            {!formLoading && (
              <>
                <View style={{marginBottom: 10}}>
                  <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                    App Name{' '}
                    <TextElement color="SECONDARY" fontSize="sm">
                      (Max 30 characters)
                    </TextElement>
                  </TextElement>

                  <TextInput
                    style={[styles.input, errors.appName && styles.errorBorder]}
                    placeholder="Enter your app name"
                    placeholderTextColor={'#939393'}
                    maxLength={30}
                    value={appName}
                    onChangeText={text => updateAppDetails({appName: text})}
                    onBlur={() => handleBlur('appName', appName)}
                  />

                  {errors.appName && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={13} color="red" />
                      <Text style={styles.errorText}>App Name is required.</Text>
                    </View>
                  )}
                </View>
                <View style={{marginBottom: 10}}>
                  <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                    Subtitle{' '}
                    <TextElement color="SECONDARY" fontSize="sm">
                      (Max 30 characters)
                    </TextElement>
                  </TextElement>
                  <TextInput
                    style={[styles.input, errors.subtitle && styles.errorBorder]}
                    placeholder="Add a catchy subtitle"
                    placeholderTextColor={'#939393'}
                    maxLength={30}
                    value={subtitle}
                    onChangeText={text => updateAppDetails({subtitle: text})}
                    onBlur={() => handleBlur('subtitle', subtitle)}
                  />

                  {errors.subtitle && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={14} color="red" />
                      <Text style={styles.errorText}>Subtitle is required.</Text>
                    </View>
                  )}
                </View>

                {/* App Icon */}
                <View style={{marginBottom: 10}}>
                  <FileUploadButton
                    label="App Icon"
                    fileType="image"
                    fileClass="Icon"
                    subLabel="(1024 × 1024 px, PNG)"
                    accept=".png"
                    onFileSelect={file => {
                      Analytics.track('editor:buildpublishflow_iconUploaded', {});
                      updateAppDetails({
                        iconUri: file,
                      });
                    }}
                    setLoadingState={setIsFileUploading}
                    existingFileURL={iconUri}
                    expectedWidth={1024}
                    expectedHeight={1024}
                    iconName="image"
                    style={{backgroundColor: '#F5F5F5'}}
                    assetClass="icon"
                  />

                  {/* {iconUri && <Image source={{uri: iconUri}} style={styles.uploadedImagePreview} />} */}
                </View>
                <View style={{marginBottom: 10}}>
                  {/* Description */}
                  <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                    Description{' '}
                    <TextElement color="SECONDARY" fontSize="sm">
                      (Max 4000 characters)
                    </TextElement>
                  </TextElement>
                  <TextInput
                    style={[styles.input, styles.textArea, errors.description && styles.errorBorder]}
                    placeholder="Write a brief overview of your app"
                    placeholderTextColor={'#939393'}
                    multiline
                    maxLength={4000}
                    value={description}
                    onBlur={() => handleBlur('description', description)}
                    onChangeText={text => updateAppDetails({description: text})}
                  />
                  {errors.description && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={13} color="red" />
                      <Text style={styles.errorText}>App Description is required.</Text>
                    </View>
                  )}
                </View>
                <View style={{marginBottom: 10}}>
                  {/* Splash Screen */}

                  <FileUploadButton
                    label="Splash Screen"
                    fileType="image"
                    fileClass="Splash"
                    subLabel="(1242 × 2208 px, PNG/GIF)"
                    expectedWidth={1242}
                    expectedHeight={2208}
                    accept=".png,.gif"
                    onFileSelect={file => {
                      Analytics.track('editor:buildpublishflow_splashUploaded', {});
                      updateAppDetails({
                        splashUri: file,
                      });
                    }}
                    setLoadingState={setIsFileUploading}
                    iconName="image"
                    existingFileURL={splashUri}
                    style={{backgroundColor: '#F5F5F5'}}
                    assetClass="splash"
                  />
                </View>

                <View style={{marginBottom: 10}}>
                  {/* Privacy Policy */}
                  <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                    Privacy Policy URL
                  </TextElement>
                  <TextInput
                    style={[styles.input, errors.privacyPolicy && styles.errorBorder]}
                    placeholder="Enter privacy policy URL"
                    placeholderTextColor={'#939393'}
                    value={privacyPolicy}
                    onBlur={() => handleBlur('privacyPolicy', privacyPolicy)}
                    onChangeText={text => updateAppDetails({privacyPolicy: text})}
                  />
                  {errors.privacyPolicy && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={14} color="red" />
                      <Text style={styles.errorText}>Privacy Policy is required.</Text>
                    </View>
                  )}
                </View>
                <View style={{marginBottom: 10}}>
                  {/* Support URL*/}
                  <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                    Support URL / Contact Us Page
                  </TextElement>
                  <TextInput
                    style={[styles.input, errors.privacyPolicy && styles.errorBorder]}
                    placeholder="Enter Support / Contact Us Website"
                    placeholderTextColor={'#939393'}
                    value={supportURL}
                    onBlur={() => handleBlur('supportURL', supportURL)}
                    onChangeText={text => updateAppDetails({supportURL: text})}
                  />
                  {errors.supportURL && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={14} color="red" />
                      <Text style={styles.errorText}>Support URL is required.</Text>
                    </View>
                  )}
                </View>
              </>
            )}
          </View>
        </ScrollView>

        {/* Right Panel */}
        <View style={[styles.rightPanel]}>
          <div
            style={{
              width: '300px',
              height: '300px',
              overflow: 'scroll',
              position: 'absolute',
              bottom: '-40px',
              paddingBottom: '40px',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              backgroundColor: '#000000',
              borderRadius: '47.7px',
              borderWidth: '12px',
              borderColor: '#000000',
              borderStyle: 'solid',
            }}
            className="publishFlow-scrollbar">
            <ScrollView
              contentContainerStyle={[styles.scrollableContent]}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}>
              {/* Status Bar */}
              <View style={styles.statusBar}>
                <TextElement style={styles.timeText}>5:05</TextElement>
                <View style={styles.statusIcons}>
                  <MaterialCommunityIcons name="wifi" size={15} color="#fff" />
                  <MaterialCommunityIcons name="battery" size={15} color="#fff" />
                </View>
              </View>

              {/* App Header */}
              {/* <View style={styles.headerSection}>
                <MaterialCommunityIcons name="chevron-left" size={20} color="#007BFF" />
                <TextElement style={styles.backText}>Search</TextElement>
              </View> */}

              {/* App Content */}
              <View style={styles.contentSection}>
                <View style={styles.appIconContainer}>
                  {iconUri ? (
                    <Image source={{uri: iconUri}} style={styles.appIcon} />
                  ) : (
                    <View style={styles.placeholderIcon}>
                      <TextElement style={styles.placeholderText}>App Icon</TextElement>
                    </View>
                  )}
                </View>
                <View style={styles.textContainer}>
                  <TextElement style={styles.appName}>{appName || 'App Name'}</TextElement>
                  <TextElement style={styles.subtitle}>{subtitle || 'Subtitle'}</TextElement>
                  <View style={styles.getButton}>
                    <TextElement style={styles.getButtonText}>Get</TextElement>
                  </View>
                </View>
              </View>

              {/* Ratings Section */}
              <View style={styles.ratingsSection}>
                <View style={styles.ratingItem}>
                  <TextElement style={styles.ratingValue}>1 Rating</TextElement>

                  <TextElement style={styles.ratingDetail}>5.0 </TextElement>
                  <TextElement style={styles.ratingDetail}>★★★★★</TextElement>
                </View>
                <View style={styles.ratingItem}>
                  <TextElement style={styles.ratingValue}>Age</TextElement>
                  <TextElement style={styles.ratingDetail}>4+</TextElement>
                  <TextElement style={styles.ratingDetail}>Years Old</TextElement>
                </View>
                <View style={styles.ratingItem}>
                  <TextElement style={styles.ratingValue}>Category</TextElement>
                  <MaterialCommunityIcons name="shopping" size={14} color={'#AAAAAA'} />
                  <TextElement style={styles.ratingDetail}>Shopping</TextElement>
                </View>
                <View style={styles.ratingItem}>
                  <TextElement style={styles.ratingValue}>Developer</TextElement>
                  <TextElement style={styles.ratingDetail}>Apptile</TextElement>
                </View>
              </View>

              {/* Description */}
              <View style={styles.additionalContent}>
                <TextElement style={styles.additionalText}>
                  {description || 'Your app description will appear here.'}
                </TextElement>
              </View>

              {/* Screenshots */}
              <View style={styles.screenshots}>
                {/* First Gradient */}
                <View style={[styles.screenshot, styles.gradient1]} />
                {/* Second Gradient */}
                <View style={[styles.screenshot, styles.gradient2]} />
              </View>
            </ScrollView>
          </div>
        </View>
      </View>
      <Footer
        canProceed={
          isLegacyApp ||
          (!isFileUploading && appName && subtitle && description && privacyPolicy && iconUri && splashUri && supportURL
            ? true
            : false)
        }
        onNext={() => handleSubmit()}
      />
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    flexDirection: 'row',
    padding: 20,
    // backgroundColor: '#F5F5F5',
  },
  leftPanel: {
    flex: 1,
    paddingRight: 10,
    marginRight: 20,
    height: '100%',
  },
  leftPanelContent: {
    flexGrow: 1,
    // height: '100%',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    height: '100%',
  },
  heading: {
    fontWeight: '600',
    marginBottom: 10,
  },
  input: {
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    marginBottom: 10,
    backgroundColor: '#F3F3F3',
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: '#FF2424',
  },
  errorText: {
    color: '#FF2424',
    fontSize: 12,
    fontWeight: 400,
    marginBottom: 10,
  },
  textArea: {
    height: 120,
  },
  descriptionInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  uploadButton: {
    backgroundColor: '#007BFF',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  uploadLabel: {
    color: '#FFFFFF',
    fontWeight: '600',
    cursor: 'pointer',
  },
  fileInput: {
    display: 'none',
  },
  uploadedImagePreview: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginTop: 10,
  },
  splashPreview: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginTop: 10,
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    paddingTop: 117,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    alignItems: 'center',
    // justifyContent: 'center',
    paddingLeft: 20,
    overflow: 'hidden',
  },
  phoneContainer: {
    width: 330,
    height: 720,
    backgroundColor: '#000000',
    borderRadius: 47.7,
    // overflow: 'scroll',
    // overflow: 'hidden',
    // justifyContent: 'flex-end',
    borderWidth: 12,
    borderColor: '#000000',
  },
  scrollableContent: {
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  statusIcons: {
    flexDirection: 'row',
  },
  statusIcon: {
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: 8,
  },
  headerSection: {
    flexDirection: 'row',

    alignItems: 'center',
    marginBottom: 5,
  },
  backText: {
    color: '#007BFF',
    fontSize: 14,
    fontWeight: '400',
  },
  contentSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 20,
  },
  appIconContainer: {
    marginRight: 10,
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 20,
  },
  placeholderIcon: {
    width: 101,
    height: 101,
    backgroundColor: '#E0E0E0',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 14,
    color: '#333',
  },
  textContainer: {
    flex: 1,
  },
  appName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 12.55,
    color: '#A4A4AB',
    marginBottom: 10,
  },
  getButton: {
    backgroundColor: '#007BFF',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 20,
    alignItems: 'center',
    width: 75,
  },
  getButtonText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  shareIcon: {
    fontSize: 18,
    color: '#007BFF',
    marginLeft: 10,
  },
  ratingsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 10,
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  ratingItem: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 5,
    paddingRight: 15,
    paddingLeft: 15,
    borderRightWidth: 1,
    borderRightColor: '#D6D4D4',
  },
  ratingValue: {
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  ratingDetail: {
    fontSize: 14,
    color: '#AAAAAA',
  },
  additionalContent: {
    marginTop: 10,
  },
  additionalText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginBottom: 10,
  },
  privacyPolicy: {
    fontSize: 12,
    color: '#007BFF',
    marginTop: 10,
  },
  privacyPolicyPlaceholder: {
    fontSize: 12,
    color: '#AAAAAA',
    marginTop: 10,
  },
  splashPreviewInPhone: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginTop: 10,
  },

  fileButton: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  fileLabel: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  fileIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  fileText: {
    color: '#555',
    fontSize: 14,
  },
  hiddenInput: {
    display: 'none',
  },
  screenshots: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
    // padding: 16,
    backgroundColor: '#121212', // Black background
  },
  screenshot: {
    width: 150,
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradient1: {
    backgroundColor: '#086877', // Greyish-blue gradient start (based on uploaded image)
    borderBottomColor: '#086877', // Greyish-blue gradient end
    borderBottomWidth: 250, // Mimicking gradient effect
  },
  gradient2: {
    backgroundColor: '#64a1d9', // Bluish-grey gradient start (based on uploaded image)
    borderBottomColor: '#64a1d9', // Bluish-grey gradient end
    borderBottomWidth: 250, // Mimicking gradient effect
  },
  textInputErrorContainer: {
    flexDirection: 'row',

    gap: 5,
  },
});

export default AppDetails;
