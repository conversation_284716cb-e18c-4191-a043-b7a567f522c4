import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import {darkTheme} from '../styles';

const Header: React.FC = () => {
  return (
    <View style={styles.container}>
      <TextElement style={styles.header}>Publish your app</TextElement>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: darkTheme.headerBackground,
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: darkTheme.primaryText,
    marginBottom: 20,
  },
  header: {
    fontSize: 20,
    fontWeight: '600',
    color: darkTheme.primaryText,
  },
  closeButton: {
    backgroundColor: darkTheme.headerBackground,
    borderRadius: 16,
    padding: 5,
  },
  closeIcon: {
    fontSize: 18,
    fontWeight: 'bold',
    color: darkTheme.primaryText,
  },
});

export default Header;
