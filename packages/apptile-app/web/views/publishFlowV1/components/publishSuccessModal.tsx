import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Modal} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../../components-v2/base/TextElement';
import {darkTheme} from '../styles';
import {usePublishContext} from '../context';

interface PublishSuccessModalProps {
  visible: boolean;
  onClose: () => void;
  appStoreAccountName?: string;
  playStoreAccountName?: string;
}

const PublishSuccessModal: React.FC<PublishSuccessModalProps> = ({
  visible,
  onClose,
  appStoreAccountName = 'Apptile',
  playStoreAccountName = 'Floryo',
}) => {
  const {setStep} = usePublishContext();

  const handleContinue = () => {
    setStep(5); // Navigate to Your Builds page
    onClose();
  };

  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialCommunityIcons name="close" size={24} color={darkTheme.primaryText} />
          </TouchableOpacity>

          {/* Success Icon */}
          <View style={styles.successIconContainer}>
            <MaterialCommunityIcons name="check" size={48} color="#FFFFFF" />
          </View>

          {/* Title */}
          <TextElement style={styles.title}>Your Publish Profile is now complete!</TextElement>

          {/* Description */}
          <TextElement style={styles.description}>
            Your Android and iOS apps are now being built. Once the builds are ready, we'll automatically submit them
            for review to the developer accounts you selected:
          </TextElement>

          {/* Account Details */}
          <View style={styles.accountsContainer}>
            {/* AppStore Account - Only show if appStoreAccountName is provided */}
            {appStoreAccountName && (
              <View style={styles.accountRow}>
                <View style={styles.accountIcon}>
                  <Text style={styles.accountIconText}>🍎</Text>
                </View>
                <View style={styles.accountInfo}>
                  <TextElement style={styles.accountLabel}>AppStore Account Name:</TextElement>
                  <TextElement style={styles.accountName}>{appStoreAccountName}</TextElement>
                </View>
              </View>
            )}

            {/* PlayStore Account - Only show if playStoreAccountName is provided */}
            {playStoreAccountName && (
              <View style={styles.accountRow}>
                <View style={styles.accountIcon}>
                  <Text style={styles.accountIconText}>▶️</Text>
                </View>
                <View style={styles.accountInfo}>
                  <TextElement style={styles.accountLabel}>PlayStore Account Name:</TextElement>
                  <TextElement style={styles.accountName}>{playStoreAccountName}</TextElement>
                </View>
              </View>
            )}
          </View>

          {/* Continue Button */}
          <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
            <TextElement style={styles.continueButtonText}>Continue</TextElement>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: darkTheme.cardBackground,
    borderRadius: 16,
    padding: 32,
    width: '100%',
    maxWidth: 500,
    alignItems: 'center',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 8,
    zIndex: 1,
  },
  successIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: darkTheme.primaryText,
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: darkTheme.secondaryText,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  accountsContainer: {
    width: '100%',
    backgroundColor: darkTheme.secondaryCardBackground,
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
    gap: 16,
  },
  accountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  accountIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: darkTheme.primaryButton,
    justifyContent: 'center',
    alignItems: 'center',
  },
  accountIconText: {
    fontSize: 20,
  },
  accountInfo: {
    flex: 1,
  },
  accountLabel: {
    fontSize: 14,
    color: darkTheme.secondaryText,
    marginBottom: 4,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: darkTheme.primaryText,
  },
  continueButton: {
    backgroundColor: darkTheme.primaryButton,
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 8,
    minWidth: 120,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: darkTheme.primaryButtonText,
    textAlign: 'center',
  },
});

export default PublishSuccessModal;
