import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TextInput, TouchableOpacity, Linking, ActivityIndicator} from 'react-native';
import Tooltip from './tooltip';
import CheckBoxWidget from './checkBoxWidget';
import {MaterialCommunityIcons} from 'apptile-core';
import RadioGroup from './radioGroup';
import FileUploadButton from './fileUploadButton';
import Icon from 'react-native-vector-icons/AntDesign';
import {isEmpty} from 'lodash';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import TextElement from '@/root/web/components-v2/base/TextElement';
import DropDownControl from '@/root/web/components/controls/DropDownControl';
import Analytics from '@/root/web/lib/segment';
import {darkTheme} from '../styles';

interface IosDetailsProps {
  onPublish: () => void;
  isAndroidBuildRequested: boolean;
  helpDocs: any[];
  setMainDocRef: any;
  tooltipVisible: number | null;
  setTooltipVisible: (value: number | null) => void;
  appleDeveloperAccountTypes: any[];
  handleRequestBuild: () => void;
  autoFetchIOSVersion: (appId: string, platform: string) => Promise<any>;
  handleBuild: (platform: string, version: string, semVersion: string) => Promise<void>;
  alreadyExistingMetaData: any;
  setAlreadyExistingMetaData: (data: any) => void;
  appDetails: any;
  isLegacyApp: boolean;
}

const IosDetails: React.FC<IosDetailsProps> = ({
  onPublish,
  isAndroidBuildRequested,
  helpDocs,
  setMainDocRef,
  tooltipVisible,
  setTooltipVisible,
  appleDeveloperAccountTypes,
  handleRequestBuild,
  autoFetchIOSVersion,
  handleBuild,
  alreadyExistingMetaData,
  setAlreadyExistingMetaData,
  appDetails,
  isLegacyApp,
}) => {
  const {buildDetails, setBuildDetails} = usePublishContext();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);

  let {
    isAppleSelected,
    accountType,
    hasExistingIOSApp,
    accountDetails,
    initialAccountDetails,
    isIOSBuildRequested,
    isDeveloperAccountConnected,
    ipadSupport,
    appTrackingPermission,
    appstoreBundleIds,
  } = buildDetails.ios;

  const [IOSConnectAccountError, setIOSConnectAccountError] = useState('');
  const [isRequestBuildVisible, setRequestBuildVisible] = useState(false);
  const [connectAccountLoader, setConnectAccountLoader] = useState(false);
  const [isConnectAccountVisible, setConnectAccountVisible] = useState(false);
  const [isConnectAccountDisabled, setConnectAccountDisabled] = useState(false);
  const [showExistingAppSection, setExistingAppSection] = useState(false);
  const [isIOSRequestDisabled, setIOSRequestDisabled] = useState(false);

  const updateIOSDetails = (key: string, value: any) => {
    setBuildDetails((prev: any) => ({
      ...prev,
      ios: {
        ...prev.ios,
        [key]: value,
      },
    }));
  };

  useEffect(() => {
    const isAnyDetailEmpty = accountDetails.some((detail: string) => isEmpty(detail) || detail === '');
    setConnectAccountDisabled(isAnyDetailEmpty || !buildDetails.assets.appStorePrivateKey);
  }, [accountDetails, buildDetails]);

  useEffect(() => {
    if (appstoreBundleIds?.length > 0) {
      setExistingAppSection(true);
    } else {
      setExistingAppSection(false);
    }
  }, [appstoreBundleIds]);

  useEffect(() => {
    if (buildDetails.ios.isDeveloperAccountConnected) {
      if (buildDetails.ios.hasExistingIOSApp) {
        if (buildDetails.ios.iosBundleId.trim('') !== '') {
          setIOSRequestDisabled(false);
        } else {
          setIOSRequestDisabled(true);
        }
      } else {
        setIOSRequestDisabled(false);
      }
      setRequestBuildVisible(true);
    }
  }, [buildDetails.ios]);

  const openAppstoreLink = () => {
    Linking.openURL('https://appstoreconnect.apple.com/apps');
  };

  const handleConnectAccount = async () => {
    Analytics.track('editor:buildpublishflow_connectAccountClicked', {});
    var isSecretsSuccesfullyUpdated = false;
    try {
      setConnectAccountLoader(true);

      const changedSecrets = [];
      const secretClasses = ['teamId', 'appStoreIssuerId', 'appStoreApiKey'];

      for (let i = 1; i <= 3; i++) {
        if (initialAccountDetails[i] !== accountDetails[i]) {
          changedSecrets.push({
            secret: accountDetails[i].trim(),
            secretClass: secretClasses[i - 1],
          });
        }
      }

      if (changedSecrets.length > 0) {
        await BuildManagerApi.postAppSecret(appId, changedSecrets);
        isSecretsSuccesfullyUpdated = true;
      }

      await BuildManagerApi.verifyCredentials(appId);
      const responseBundleIDS = await BuildManagerApi.getAppStoreBundleIds(appId);
      updateIOSDetails('appstoreBundleIds', responseBundleIDS.data);

      setRequestBuildVisible(true);
      setIOSConnectAccountError('');
      setExistingAppSection(true);
      setConnectAccountLoader(false);

      updateIOSDetails('isDeveloperAccountConnected', true);
    } catch (error: any) {
      setConnectAccountLoader(false);
      console.log(error);
      const errorMessage = error?.response?.data?.message || 'Failed to verify credentials. Please try again.';
      setIOSConnectAccountError(errorMessage);
    } finally {
      if (isSecretsSuccesfullyUpdated) {
        updateIOSDetails('initialAccountDetails', [...accountDetails]);
      }
    }
  };

  const MessageBanner: React.FC<{type: 'success' | 'error'; message: string}> = ({type, message}) => {
    const iconProps = {
      name: type === 'success' ? 'checkcircle' : 'exclamationcircleo',
      size: 20,
      color: type === 'success' ? '#6AC370' : '#DC3545',
    };

    return (
      <View style={[styles.banner, type === 'success' ? styles.successConnectAccount : styles.errorConnectAccount]}>
        <TextElement style={styles.connectAccountMessage}>
          <Icon {...iconProps} />
        </TextElement>
        <TextElement color="SECONDARY">{message}</TextElement>
      </View>
    );
  };

  // Normal flow - no debugging
  const showConnectedState = isDeveloperAccountConnected;

  return (
    <>
      {/* Apple Developer Section */}
      {isAppleSelected && (
        <>
          {showConnectedState ? (
            <>
              <View style={styles.sectionHeader}>
                <TextElement style={styles.sectionTitle}>3.2 Add Existing Appstore App Details</TextElement>
              </View>

              {/* Connected Account Success Box */}
              <View style={styles.connectedAccountBox}>
                <View style={styles.connectedHeader}>
                  <Icon name="checkcircle" size={20} color={'#28A745'} />
                  <TextElement style={styles.connectedMessage}>
                    Apple developer account connected successfully.
                  </TextElement>
                </View>
                <View style={styles.connectedDetails}>
                  <View style={styles.detailRow}>
                    <TextElement style={styles.detailLabel}>Entity Name:</TextElement>
                    <TextElement style={styles.detailValue}>{accountDetails[0] || 'N/A'}</TextElement>
                  </View>
                  <View style={styles.detailRow}>
                    <TextElement style={styles.detailLabel}>Team ID:</TextElement>
                    <TextElement style={styles.detailValue}>{accountDetails[1] || 'N/A'}</TextElement>
                  </View>
                </View>
              </View>

              {/* Build Request Submitted Card */}
              {isIOSBuildRequested && (
                <View style={styles.buildSubmittedCard}>
                  <View style={styles.connectedHeader}>
                    <Icon name="checkcircle" size={20} color={'#28A745'} />
                    <TextElement style={styles.connectedMessage}>iOS Build Request Submitted Successfully!</TextElement>
                  </View>
                </View>
              )}

              {/* Existing App Question - only show if build not requested */}
              {!isIOSBuildRequested && (
                <>
                  <TextElement style={[styles.subHeading, {fontSize: 16, marginTop: 20}]}>
                    Do you have an Existing App?
                  </TextElement>
                  <View style={{marginVertical: 10}}>
                    <RadioGroup
                      options={[
                        {label: 'Yes', value: true},
                        {label: 'No', value: false},
                      ]}
                      selectedValue={hasExistingIOSApp}
                      customStyles={{}}
                      onSelect={(value: string | boolean) => {
                        updateIOSDetails('hasExistingIOSApp', value);
                      }}
                    />
                  </View>
                </>
              )}
            </>
          ) : (
            <>
              <View style={styles.sectionHeader}>
                <TextElement style={styles.sectionTitle}>3.1 Choose Appstore Developer Account</TextElement>
              </View>
            </>
          )}

          {/* Only show these two cards when build is requested */}
          {isIOSBuildRequested ? (
            <View style={styles.innerCard}>
              {/* No content here - the cards are shown above in the connected state section */}
            </View>
          ) : (
            <View style={styles.innerCard}>
              <>
                {/* Developer Account Selection */}
                <View style={{flexDirection: 'row', alignItems: 'center', marginLeft: 5}}>
                  <TextElement style={[styles.subHeading, {fontSize: 16}]} fontSize="md">
                    Do you have an Apple Developer Account?
                  </TextElement>
                  <View onMouseEnter={() => setTooltipVisible(23)} onMouseLeave={() => setTooltipVisible(null)}>
                    {tooltipVisible === 23 && (
                      <Tooltip
                        message="Choosing No will use Apptile developer account for publishing your app"
                        visible
                      />
                    )}
                    <MaterialCommunityIcons name="information-outline" color={darkTheme.primaryText} size={18} />
                  </View>
                </View>

                <RadioGroup
                  options={appleDeveloperAccountTypes}
                  selectedValue={buildDetails.ios.accountType}
                  customStyles={{marginVertical: 10}}
                  onSelect={(value: string | boolean) => {
                    updateIOSDetails('accountType', value);
                    setMainDocRef.current(helpDocs[0]);
                  }}
                />

                {accountType !== 'Apptile Developer Account' && !showConnectedState ? (
                  <>
                    <View style={styles.infoBox}>
                      <MaterialCommunityIcons name="information-outline" color={darkTheme.primaryText} size={18} />
                      <TextElement style={styles.infoText}>
                        Please share Owner Access to your Apple Developer Account with{' '}
                        <TextElement style={styles.boldText}><EMAIL></TextElement> before you go forward.
                      </TextElement>
                    </View>
                    <View
                      style={[
                        styles.linkContainer,
                        {borderBottomColor: '#F5F5F5', borderBottomWidth: 2, paddingBottom: 20},
                      ]}>
                      <MaterialCommunityIcons name="link-variant" color="#1060E0" size={20} />
                      <TouchableOpacity>
                        <Text style={{fontWeight: 'bold'}}>
                          Go to{' '}
                          <Text style={styles.link} onPress={openAppstoreLink}>
                            Apple Appstore Connect
                          </Text>{' '}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{borderBottomColor: '#F5F5F5', borderBottomWidth: 2, marginBottom: 20}}>
                      <TextElement style={[styles.heading, {fontSize: 16}]}>Account Details</TextElement>
                      {/* Inputs */}
                      {['Entity Name', 'Team ID', 'Issuer ID', 'Key ID'].map((placeholder, index) => (
                        <View key={placeholder + index}>
                          <TextElement style={styles.subHeading}>{placeholder}</TextElement>
                          <TextInput
                            key={`apple-connect-${index}`}
                            style={styles.input}
                            placeholder={`Add ${placeholder} here`}
                            placeholderTextColor={darkTheme.inputPlaceholder}
                            value={accountDetails[index]}
                            onChangeText={(value: string) => {
                              const updatedDetails = [...accountDetails];
                              updatedDetails[index] = value;
                              updateIOSDetails('accountDetails', updatedDetails);
                            }}
                            onFocus={() => setMainDocRef.current(helpDocs[2])}
                          />
                        </View>
                      ))}
                      <FileUploadButton
                        label="P8 File"
                        disabled={isDeveloperAccountConnected}
                        accept=".p8"
                        fileType="file"
                        fileClass="appStorePrivateKey"
                        assetClass="appStorePrivateKey"
                        onFileSelect={(file: File) =>
                          setBuildDetails({
                            ...buildDetails,
                            assets: {
                              ...buildDetails.assets,
                              appStorePrivateKey: file,
                            },
                          })
                        }
                        iconName="cloud-upload-outline"
                        style={{backgroundColor: darkTheme.secondaryCardBackground}}
                        existingFileURL={buildDetails?.assets?.appStorePrivateKey}
                        existingFileName={buildDetails?.assets?.appStorePrivateKey ? 'appStorePrivateKey.p8' : null}
                      />

                      <CheckBoxWidget
                        label="I have invited Apptile to my apple developer account & shared owner <NAME_EMAIL>"
                        checked={isDeveloperAccountConnected ? true : false}
                        isDisabled={isDeveloperAccountConnected}
                        onChange={(value: boolean) => {
                          setConnectAccountVisible(value);
                          setMainDocRef.current(helpDocs[1]);
                        }}
                      />

                      {IOSConnectAccountError !== '' && <MessageBanner message={IOSConnectAccountError} type="error" />}

                      {!isDeveloperAccountConnected && isConnectAccountVisible && (
                        <TouchableOpacity
                          style={isConnectAccountDisabled ? styles.requestButtonDisabled : styles.requestButton}
                          disabled={isConnectAccountDisabled}
                          onPress={handleConnectAccount}>
                          {connectAccountLoader && <ActivityIndicator size="small" color="#FFFFFF" />}

                          {!connectAccountLoader && (
                            <TextElement style={styles.buttonText} fontSize="sm">
                              Connect Account
                            </TextElement>
                          )}
                        </TouchableOpacity>
                      )}
                    </View>

                    {/* Existing App Section */}
                    {showExistingAppSection && (
                      <View>
                        <TextElement
                          style={[
                            styles.subHeading,
                            {
                              marginBottom: 5,
                              marginTop: 5,
                              fontSize: 16,
                            },
                          ]}>
                          Do you want to update your existing app on appstore?
                        </TextElement>
                        <View style={{marginVertical: 10}}>
                          <RadioGroup
                            options={[
                              {label: 'Yes', value: true},
                              {label: 'No', value: false},
                            ]}
                            selectedValue={hasExistingIOSApp}
                            customStyles={{}}
                            onSelect={(value: string | boolean) => {
                              updateIOSDetails('hasExistingIOSApp', value);
                            }}
                          />
                        </View>
                      </View>
                    )}

                    {showExistingAppSection && hasExistingIOSApp && (
                      <>
                        <TextElement style={[styles.heading, {fontSize: 16}, {marginBottom: 10}]}>
                          Previous App Details
                        </TextElement>

                        <TextElement style={styles.subHeading}>Choose your bundle id</TextElement>
                        <DropDownControl
                          value={''}
                          defaultValue={''}
                          options={appstoreBundleIds}
                          onChange={(e: string) => {
                            updateIOSDetails('iosBundleId', e);
                          }}
                        />

                        <TextElement style={[styles.heading, {fontSize: 16}, {marginBottom: 10}]}>
                          Do you support ipad device for your previous app?
                        </TextElement>

                        <RadioGroup
                          options={[
                            {label: 'Yes', value: true},
                            {label: 'No', value: false},
                          ]}
                          selectedValue={ipadSupport}
                          customStyles={{}}
                          onSelect={(value: string | boolean) => {
                            updateIOSDetails('ipadSupport', value);
                          }}
                        />
                        <TextElement style={[styles.heading, {fontSize: 16}, {marginBottom: 10}]}>
                          Does your previous app ask for user tracking permission?
                        </TextElement>

                        <RadioGroup
                          options={[
                            {label: 'Yes', value: true},
                            {label: 'No', value: false},
                          ]}
                          selectedValue={appTrackingPermission}
                          customStyles={{}}
                          onSelect={(value: string | boolean) => {
                            updateIOSDetails('appTrackingPermission', value);
                          }}
                        />
                      </>
                    )}

                    {/* Request Build Button */}
                    {isRequestBuildVisible && (
                      <TouchableOpacity
                        style={isIOSRequestDisabled ? styles.requestButtonDisabled : styles.requestButton}
                        onPress={handleRequestBuild}
                        disabled={isIOSRequestDisabled}>
                        <TextElement style={styles.buttonText} fontSize="sm">
                          Request iOS Build
                        </TextElement>
                      </TouchableOpacity>
                    )}
                  </>
                ) : (
                  <>
                    {/* If "No" Developer Account */}
                    <TouchableOpacity style={styles.requestButton} onPress={handleRequestBuild} disabled={false}>
                      <TextElement style={styles.buttonText} fontSize="sm">
                        Request iOS Build
                      </TextElement>
                    </TouchableOpacity>
                  </>
                )}
              </>
            </View>
          )}
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  accordionHeader: {
    paddingVertical: 20,
    paddingHorizontal: 15,
    backgroundColor: darkTheme.cardBackground,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
  },
  accordionContainer: {flexDirection: 'row', justifyContent: 'space-between'},
  accordionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: darkTheme.primaryText,
  },
  innerCard: {
    borderRadius: 12,
    marginVertical: 10,
  },
  successCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    backgroundColor: darkTheme.cardBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    height: 140,
    marginBottom: 20,
    gap: 10,
  },
  successMessage: {
    fontSize: 16,
    fontWeight: '400',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: darkTheme.primaryText,
    marginVertical: 15,
  },
  subHeading: {
    fontSize: 14,
    fontWeight: '600',
    color: darkTheme.primaryText,
    marginVertical: 10,
  },
  input: {
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    marginBottom: 15,
    backgroundColor: darkTheme.inputBackground,
    borderWidth: 1,
    borderColor: darkTheme.inputBorder,
    color: darkTheme.inputText,
    outline: 'none',
  },
  disabledInput: {
    backgroundColor: '#F4F4F4',
    borderColor: '#DEDEDE',
    borderWidth: 1,
  },
  banner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    width: '100%',
  },
  successConnectAccount: {
    backgroundColor: '#F0FFF5',
    borderColor: '#28A745',
    gap: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  errorConnectAccount: {
    backgroundColor: '#FFF5F5',
    borderColor: '#DC3545',
    gap: 10,
    alignItems: 'center',
  },
  connectAccountMessage: {
    fontSize: 16,
    color: '#333',
    flexShrink: 1,
  },
  infoBox: {
    backgroundColor: darkTheme.secondaryCardBackground,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    marginBottom: 15,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    fontWeight: '200',
  },
  infoText: {
    fontSize: 14,
    color: darkTheme.primaryText,
    flex: 1,
  },
  boldText: {
    fontWeight: '600',
    color: darkTheme.primaryText,
  },
  linkContainer: {
    flexDirection: 'row',
    gap: 10,
    margin: 5,
  },
  link: {
    color: '#1060E0',
    fontSize: 14,
    fontWeight: 500,
    textDecorationLine: 'underline',
    marginBottom: 15,
  },
  requestButton: {
    backgroundColor: '#1060E0',
    fontWeight: 200,
    width: 'max-content',
    minWidth: 150,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  requestButtonDisabled: {
    backgroundColor: '#A9A9A9',
    fontWeight: '200',
    width: 'max-content',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginVertical: 20,
    opacity: 0.6,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  connectedAccountBox: {
    backgroundColor: '#F0FFF5',
    borderWidth: 1,
    borderColor: '#28A745',
    borderLeftWidth: 4,
    borderLeftColor: '#28A745',
    borderRadius: 8,
    padding: 16,
    marginVertical: 10,
  },
  connectedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  connectedMessage: {
    fontSize: 16,
    fontWeight: '400',
    color: '#333',
  },
  connectedDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    minWidth: 100,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '400',
    color: '#333',
    marginLeft: 20,
  },
  sectionHeader: {
    width: '100%',
    height: 51,
    gap: 8,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomWidth: 1,
    borderBottomColor: darkTheme.primaryText,
    paddingTop: 12,
    paddingRight: 18,
    paddingBottom: 12,
    paddingLeft: 18,
    backgroundColor: darkTheme.headerBackground,
    marginBottom: 0,
    marginLeft: -20,
    marginRight: -20,
    paddingLeft: 38,
    paddingRight: 38,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: darkTheme.primaryText,
    lineHeight: 24,
  },
  buildSubmittedCard: {
    backgroundColor: '#F0FFF5',
    borderWidth: 1,
    borderColor: '#28A745',
    borderLeftWidth: 4,
    borderLeftColor: '#28A745',
    borderRadius: 8,
    padding: 16,
    marginVertical: 10,
  },
});

export default IosDetails;
