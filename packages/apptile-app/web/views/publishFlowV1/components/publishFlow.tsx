import React from 'react';
import {View, StyleSheet} from 'react-native';
import Header from './header';
import Stepper from './stepper';
import Footer from './footer';
import AppDetails from './appDetails';
import FirebaseDetails from './firebasedetails';
import {usePublishContext} from '../context';
import AppstoreDetails from './appstoreDetails';
import PlaystoreDetailsPage from './playstoreDetailsPage';
import BuildList from './buildsList';
import {darkTheme} from '../styles';

const PublishFlow: React.FC = () => {
  const {step} = usePublishContext();

  const renderStep = () => {
    switch (step) {
      case 1:
        return <AppDetails />;
      case 2:
        return <FirebaseDetails />;
      case 3:
        return <AppstoreDetails />;
      case 4:
        return <PlaystoreDetailsPage />;
      case 5:
        return <BuildList />;
      default:
        return null;
    }
  };

  return (
    <View style={{flexDirection: 'column', flex: 1, backgroundColor: darkTheme.background}}>
      <Header />
      <View style={styles.wrapper}>
        <Stepper />
        <View style={styles.content}>{renderStep()}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    padding: 20,
    backgroundColor: darkTheme.background,
  },
  content: {
    flex: 1,
  },
});

export default PublishFlow;
