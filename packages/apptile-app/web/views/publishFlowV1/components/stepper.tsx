import React, {useEffect} from 'react';
import {View, Text, StyleSheet, ActivityIndicator, TouchableOpacity} from 'react-native';
import {usePublishContext} from '../context';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../../components-v2/base/TextElement';
import {darkTheme} from '../styles';

const steps = [
  {label: 'APP DETAILS', icon: '📱'},
  {label: 'FIREBASE DETAILS', icon: '🔥'},
  {label: 'APP STORE DETAILS', icon: '🍎'},
  {label: 'PLAYSTORE DETAILS', icon: '▶️'},
  {label: 'YOUR BUILDS', icon: '📦'},
];

const Stepper: React.FC = () => {
  const {step, setStep, maxStepReached, isLoading, buildsList} = usePublishContext();

  return (
    <View style={styles.stepper}>
      {steps.map((stepItem, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === step;
        const isCompleted = stepNumber < step;
        const isAccessible = stepNumber <= maxStepReached; // Can only access steps up to the max reached
        const isClickable = isAccessible && stepNumber !== step; // Can click on accessible steps except current

        return (
          <TouchableOpacity
            key={index}
            style={[
              styles.tabContainer,
              isActive && styles.activeTab,
              isCompleted && styles.completedTab,
              !isAccessible && styles.disabledTab,
            ]}
            onPress={() => {
              if (isClickable) {
                setStep(stepNumber);
              }
            }}
            disabled={!isClickable}>
            {/* Tab Content */}
            <View style={styles.tabContent}>
              {/* Icon */}
              <Text style={[styles.tabIcon, !isAccessible && styles.disabledIcon]}>{stepItem.icon}</Text>

              {/* Label */}
              <TextElement
                style={[
                  styles.tabLabel,
                  isActive && styles.activeTabLabel,
                  isCompleted && styles.completedTabLabel,
                  !isAccessible && styles.disabledTabLabel,
                ]}>
                {stepItem.label}
              </TextElement>
            </View>

            {/* Active Tab Indicator */}
            {isActive && <View style={styles.activeIndicator} />}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  stepper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: darkTheme.background,
    paddingHorizontal: 20,
    paddingVertical: 0,
    marginBottom: 0,
    borderBottomWidth: 1,
    borderBottomColor: darkTheme.primaryBorder,
    height: 60,
  },
  tabContainer: {
    flex: 1,
    position: 'relative',
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: darkTheme.stepperActive,
  },
  completedTab: {
    // No special border for completed tabs
  },
  disabledTab: {
    opacity: 0.5,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  tabIcon: {
    fontSize: 16,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: darkTheme.secondaryText,
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  activeTabLabel: {
    color: darkTheme.primaryText,
  },
  completedTabLabel: {
    color: darkTheme.secondaryText,
  },
  disabledTabLabel: {
    color: darkTheme.tertiaryText,
  },
  disabledIcon: {
    opacity: 0.5,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -1,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: darkTheme.stepperActive,
  },
});

export default Stepper;
