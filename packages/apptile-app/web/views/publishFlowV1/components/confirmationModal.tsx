import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Modal} from 'react-native';
import {ActivityIndicator} from 'react-native';
import {darkTheme} from '../styles';

interface ConfirmationModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  isLoaderActive?: boolean;
  message: string | React.ReactNode;
  showConfirmButton?: boolean;
  showCancelButton?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  message,
  visible,
  onCancel,
  onConfirm,
  showConfirmButton = true,
  showCancelButton = true,

  isLoaderActive = false,
}) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Render text if message is a string, else render JSX content */}
          {typeof message === 'string' ? <Text style={styles.modalMessage}>{message}</Text> : message}
          <View style={styles.modalActions}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text>Cancel</Text>
            </TouchableOpacity>
            {showConfirmButton && (
              <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
                {isLoaderActive && <ActivityIndicator size="small" color="#FFFFFF" />}
                {!isLoaderActive && <Text style={styles.confirmText}>Request Build</Text>}
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    maxWidth: 440,
    minHeight: 158,
    backgroundColor: darkTheme.modalBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    padding: 20,
    alignItems: 'center',
  },
  modalMessage: {
    fontSize: 14,
    marginBottom: 20,
    color: darkTheme.primaryText,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: darkTheme.secondaryButton,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    color: darkTheme.secondaryButtonText,
    paddingHorizontal: 16,
    borderRadius: 34,
    alignItems: 'center',
    marginRight: 10,
    width: 'max-content',
  },
  confirmButton: {
    backgroundColor: darkTheme.primaryButton,
    fontWeight: 200,
    width: 'max-content',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginVertical: 10,
  },
  cancelText: {
    color: darkTheme.secondaryButtonText,
    fontSize: 14,
    fontWeight: '100',
  },
  confirmText: {
    color: darkTheme.primaryButtonText,
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ConfirmationModal;
