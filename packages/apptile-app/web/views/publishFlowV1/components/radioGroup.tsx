import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {darkTheme} from '../styles';
import TextElement from '../../../components-v2/base/TextElement';

interface RadioGroupProps {
  options: {label: string; value: string | boolean}[];
  selectedValue: string | boolean;
  onSelect: (value: string | boolean) => void;
  customStyles: Record<string, any>;
}

const RadioGroup: React.FC<RadioGroupProps> = ({options, selectedValue, onSelect, customStyles = {}}) => {
  return (
    <View style={[styles.radioGroup, customStyles]}>
      {options.map((option, index) => (
        <TouchableOpacity key={option.label + index} style={styles.radioButton} onPress={() => onSelect(option.value)}>
          <View style={[styles.radioOuterCircle, selectedValue === option.value && styles.radioOuterCircleActive]}>
            {selectedValue === option.value && <View style={styles.radioInnerCircle} />}
          </View>

          <TextElement color={selectedValue === option.value ? 'PRIMARY' : 'SECONDARY'} fontSize="md">
            {option.label}
          </TextElement>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  radioGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    marginBottom: 10,
  },
  radioOuterCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: darkTheme.radioInactive,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  radioOuterCircleActive: {
    borderColor: darkTheme.radioActive,
  },
  radioInnerCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: darkTheme.radioActive,
  },
  radioButtonText: {
    fontSize: 16,
    color: darkTheme.radioText,
  },
  radioButtonTextActive: {
    color: darkTheme.radioActive,
    fontWeight: '600',
  },
});

export default RadioGroup;
