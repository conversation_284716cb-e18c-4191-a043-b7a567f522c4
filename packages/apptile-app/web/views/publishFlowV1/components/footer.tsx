import React, {useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Text, ActivityIndicator} from 'react-native';
import {usePublishContext} from '../context';
import {darkTheme} from '../styles';

interface FooterProps {
  canProceed?: boolean;
  onNext?: () => Promise<void> | void;
  isLoading?: boolean;
  errorMessage?: string;
}

const Footer: React.FC<FooterProps> = ({canProceed = true, onNext, isLoading: externalLoading, errorMessage}) => {
  const {step, totalSteps, setStep, isLoading: contextLoading} = usePublishContext();

  const [isLoading, setIsLoading] = useState(false);

  // Enhanced nextStep function with API handling
  const nextStep = async () => {
    setStep(prev => Math.min(prev + 1, totalSteps)); // Move to the next step
  };

  const prevStep = () => {
    setStep(prev => Math.max(prev - 1, 1));
  };

  const handleNext = async () => {
    if (!canProceed || isLoading) return;
    setIsLoading(true);
    try {
      // If onNext is provided, call it first
      if (onNext) {
        await onNext();
      }
      // Only proceed to next step if onNext succeeds
      nextStep();
    } catch (error) {
      console.error('Error in next step handler:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.footer}>
        {/* Back Button */}
        <TouchableOpacity
          style={[styles.button, styles.backButton, step === 1 && styles.hiddenButton]}
          onPress={prevStep}
          disabled={step === 1 || isLoading}>
          <Text style={[styles.buttonText, step === 1 && styles.disabledText]}>Back</Text>
        </TouchableOpacity>

        {/* Next Button */}
        {step < totalSteps ? (
          <View>
            <TouchableOpacity
              style={[styles.button, styles.nextButton, !canProceed && styles.disabledButton]}
              onPress={handleNext}
              disabled={!canProceed || isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={[styles.buttonText, {color: '#FFFFFF'}]}>Next</Text>
              )}
            </TouchableOpacity>
            {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}
          </View>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: darkTheme.primaryBorder,
    backgroundColor: darkTheme.background,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    border: 1,
  },
  backButton: {
    backgroundColor: darkTheme.secondaryButton,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    color: darkTheme.secondaryButtonText,
  },
  nextButton: {
    backgroundColor: darkTheme.primaryButton,
  },
  disabledButton: {
    backgroundColor: darkTheme.disabledButton,
    opacity: 0.7,
  },
  hiddenButton: {
    opacity: 0.3,
  },
  disabledText: {
    color: darkTheme.disabledButtonText,
  },
  buttonText: {
    fontSize: 12,
    color: darkTheme.primaryButtonText,
  },
  errorText: {
    color: darkTheme.error,
    fontSize: 12,
    marginTop: 4,
    textAlign: 'right',
  },
});

export default Footer;
