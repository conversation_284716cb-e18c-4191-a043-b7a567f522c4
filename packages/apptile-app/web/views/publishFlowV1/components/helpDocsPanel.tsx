import React, {useState, useEffect, useRef} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Linking, Animated, ScrollView} from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import {darkTheme} from '../styles';

const HelpDocs = ({helpDocs, setMainDocFn}) => {
  const [mainDoc, setMainDoc] = useState({});
  const isItRenderedFirstTime = useRef(true);
  const [highlighted, setHighlighted] = useState(false);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    setMainDoc(helpDocs[0]);
  }, [helpDocs]);

  const moreDocs = helpDocs.filter(doc => doc.url !== mainDoc.url);

  const setMainHelpDoc = doc => {
    if (mainDoc.title === doc.title) return;

    // Fade out/in animation
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();

    setMainDoc(doc);
  };

  useEffect(() => {
    setMainDocFn(setMainHelpDoc);
  }, []);

  const DocCard = ({doc, isMain}) => (
    <View style={[styles.docCard, isMain && styles.mainDocCard]}>
      <View style={styles.docContent}>
        <TextElement
          style={[styles.docTitle, isMain && styles.mainDocTitle]}
          color="SECONDARY"
          fontSize={isMain ? 'sm' : 'sm'}>
          {doc.title}
        </TextElement>
        {isMain && (
          <TouchableOpacity style={styles.linkButton} onPress={() => Linking.openURL(doc.url)}>
            <TextElement color="PRIMARY" fontSize="sm" fontWeight={isMain ? '600' : '400'}>
              View Help Doc
            </TextElement>
            <TextElement style={styles.arrow}>→</TextElement>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <View style={[styles.container]} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
      <View style={[styles.contentWrapper]}>
        <Animated.View style={[styles.mainDocWrapper, {opacity: fadeAnim}]}>
          <DocCard doc={mainDoc} isMain={true} />
        </Animated.View>

        <ScrollView style={[styles.moreGuidesSection]}>
          <TextElement style={styles.moreGuidesTitle}>MORE GUIDES</TextElement>

          <View style={[styles.moreGuidesList]}>
            {moreDocs.map(doc => (
              <TouchableOpacity key={doc.url} onPress={() => Linking.openURL(doc.url)} style={styles.moreGuidesItem}>
                <DocCard doc={doc} isMain={false} />
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '40%',
    borderRadius: 16,
    height: '100%',
    marginRight: 20,

    flexGrow: 1,
  },
  contentWrapper: {
    backgroundColor: darkTheme.helpPanelBackground,
    padding: 20,
    height: '100%',
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: darkTheme.helpPanelBorder,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  mainDocWrapper: {
    marginBottom: 24,
  },
  docCard: {
    backgroundColor: darkTheme.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  mainDocCard: {
    padding: 20,
    marginBottom: 0,
  },
  docContent: {
    flex: 1,
  },
  docTitle: {
    color: darkTheme.helpPanelText,
    marginBottom: 8,
  },
  mainDocTitle: {
    fontWeight: 600,
    marginBottom: 12,
  },
  linkButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  arrow: {
    color: '#2563EB',
    fontSize: 14,
    marginLeft: 4,
  },
  moreGuidesSection: {
    marginTop: 8,
  },
  moreGuidesTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    letterSpacing: 0.5,
    marginBottom: 16,
  },
  moreGuidesList: {
    gap: 8,
    height: '100%',
  },
  moreGuidesItem: {
    marginBottom: 8,
  },
});

export default HelpDocs;
