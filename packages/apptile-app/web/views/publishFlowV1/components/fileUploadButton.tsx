import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image, ActivityIndicator} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import Uploader<PERSON>pi from '@/root/web/api/UploaderApi';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import TextElement from '../../../components-v2/base/TextElement';
import {Icon} from 'apptile-core';
import {darkTheme} from '../styles';

interface FileUploadButtonProps {
  label: string;
  fileClass: string;
  subLabel?: string;
  accept?: string;
  onFileSelect: (file: File) => void;
  iconName?: string;
  style?: object;
  iconColor?: string;
  textColor?: string;
  assetClass: string;
  existingFileURL: string | null;
  existingFileName: string | null;
  disabled: boolean;
  expectedWidth?: number;
  expectedHeight?: number;
  fileType: string;
  setLoadingState?: (loading: boolean) => void;
}

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  accept = '*/*',
  fileClass = 'File',
  fileType,
  iconName = 'upload',
  style,
  iconColor = '#000000',
  textColor = '#000000',
  assetClass,
  label,
  subLabel,
  existingFileURL,
  existingFileName = '',
  onFileSelect,
  disabled,
  expectedWidth,
  expectedHeight,
  setLoadingState,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);

  useEffect(() => {
    if (existingFileURL) {
      setFilePreview(existingFileURL);
      setFileName(existingFileName);
    }
  }, [existingFileURL, existingFileName]);

  const validateImageDimensions = (file: File): Promise<boolean> => {
    return new Promise(resolve => {
      if (!file.type.startsWith('image/')) {
        resolve(true);
        return;
      }

      const img = document.createElement('img');
      img.src = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(img.src);
        if (expectedWidth && expectedHeight) {
          if (img.width !== expectedWidth || img.height !== expectedHeight) {
            setError(
              `Image dimensions must be ${expectedWidth}x${expectedHeight} pixels. Uploaded image is ${img.width}x${img.height} pixels.`,
            );
            resolve(false);
            return;
          }
        }
        resolve(true);
      };

      img.onerror = () => {
        URL.revokeObjectURL(img.src);
        setError('Failed to load image for dimension validation');
        resolve(false);
      };
    });
  };

  const handleSubmit = async (file: File) => {
    if (disabled) return;
    setIsLoading(true);
    setError(null);

    try {
      const isValidDimensions = await validateImageDimensions(file);
      setLoadingState && setLoadingState(true);
      if (!isValidDimensions) {
        setIsLoading(false);
        return;
      }

      const response = await UploaderApi.UploadAssetApi({file}, () => {});
      await BuildManagerApi.postAppAsset(appId, {
        assetClass,
        uploaderKey: response.data.fileKey,
      });

      setUploadedFile(file);
      setFileName(file.name);
      if (file.type.startsWith('image/')) {
        setFilePreview(URL.createObjectURL(file));
      }
      setFilePreview(URL.createObjectURL(file));
      onFileSelect(URL.createObjectURL(file));
    } catch (err) {
      setError('Failed to upload the file. Please try again.');

      if (err?.response?.status == 400) {
        setError(err?.response?.data?.message ?? 'Failed to upload the file. Please try again.');
      } else {
        setError('Failed to upload the file. Please try again.');
      }
      setUploadedFile(null);
      setFilePreview(null);
    } finally {
      setIsLoading(false);
      setLoadingState && setLoadingState(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    const fileInput = event.target;
    const file = fileInput.files?.[0];

    if (file) {
      const isValidDimensions = await validateImageDimensions(file);
      if (isValidDimensions) {
        handleSubmit(file);
      }
    }
  };

  const handleDelete = () => {
    setUploadedFile(null);
    setFilePreview(null);
    setError(null);
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator size="small" color="#0000ff" />
      ) : filePreview ? (
        <View>
          {fileType == 'image' && (
            <View style={styles.previewContainer}>
              {filePreview && (
                <>
                  <TextElement style={styles.label} color="SECONDARY" fontSize="sm">
                    {label}{' '}
                    {subLabel && (
                      <TextElement color="SECONDARY" fontSize="sm" fontWeight="400">
                        {subLabel}
                      </TextElement>
                    )}
                  </TextElement>
                  <View style={styles.imagePreviewWrapper}>
                    <Image source={{uri: filePreview}} style={styles.imagePreview} />
                    {!disabled && (
                      <TouchableOpacity onPress={handleDelete} style={styles.deleteButton}>
                        <MaterialCommunityIcons name="delete" size={20} color="#fff" />
                      </TouchableOpacity>
                    )}
                  </View>
                </>
              )}
            </View>
          )}
          {fileType !== 'image' && (
            <>
              <TextElement style={styles.label} color="SECONDARY" fontSize="sm">
                {label}{' '}
                {subLabel && (
                  <TextElement color="SECONDARY" fontSize="sm" fontWeight="400">
                    {subLabel}
                  </TextElement>
                )}
              </TextElement>
              <TouchableOpacity style={[styles.fileButton, style]} disabled={disabled}>
                <label style={styles.fileLabel}>
                  <MaterialCommunityIcons name="reload" size={24} style={styles.fileIcon} color={iconColor} />
                  <TextElement style={[styles.fileText, {color: textColor}]}>{fileName}</TextElement>
                  <input
                    type="file"
                    accept={accept}
                    onChange={handleFileUpload}
                    style={styles.hiddenInput}
                    disabled={disabled}
                  />
                </label>
              </TouchableOpacity>
            </>
          )}
        </View>
      ) : (
        <>
          <TextElement style={styles.label} color="SECONDARY" fontSize="sm">
            {label}{' '}
            {subLabel && (
              <TextElement color="SECONDARY" fontSize="sm" fontWeight="400">
                {subLabel}
              </TextElement>
            )}
          </TextElement>
          <TouchableOpacity style={[styles.fileButton, style]} disabled={disabled}>
            <label style={styles.fileLabel}>
              <Icon iconType="Feather" name={'upload'} style={styles.fileIcon} size={20} color={iconColor} />

              <TextElement style={[styles.fileText, {color: textColor}]}>{`Upload ${fileClass}`}</TextElement>
              <input
                type="file"
                accept={accept}
                onChange={handleFileUpload}
                style={styles.hiddenInput}
                disabled={disabled}
              />
            </label>
          </TouchableOpacity>
        </>
      )}
      {error && <TextElement style={styles.errorText}>{error}</TextElement>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  label: {
    fontWeight: '600',
    marginBottom: 10,
  },
  fileButton: {
    height: 40,
    backgroundColor: darkTheme.uploadButtonBackground,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: darkTheme.uploadButtonBorder,
    paddingHorizontal: 14,
    paddingVertical: 8,
    display: 'flex',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  fileLabel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    cursor: 'pointer',
  },
  fileIcon: {
    marginRight: 8,
  },
  fileText: {
    fontSize: 12,
    fontWeight: '600',
    paddingVertical: 10,
  },
  hiddenInput: {
    display: 'none',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    opacity: 0,
    cursor: 'pointer',
  },
  previewContainer: {
    // marginTop: 16,
  },
  imagePreviewWrapper: {
    position: 'relative',
    width: 120,
    height: 120,
    borderWidth: 1,
    borderColor: darkTheme.uploadButtonBorder,
    borderRadius: 10,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  deleteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#FF2424',
    borderRadius: 20,
    padding: 4,
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: 'red',
  },
});

export default FileUploadButton;
