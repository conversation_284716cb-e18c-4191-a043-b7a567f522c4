import React, {useState, useEffect, useRef} from 'react';
import {useCallbackRef} from 'apptile-core';
import Footer from './footer';
import {View, StyleSheet, ScrollView} from 'react-native';
import ConfirmationModal from './confirmationModal'; // Import the reusable modal component
import PublishSuccessModal from './publishSuccessModal';
import CheckBoxWidget from './checkBoxWidget';
import Icon from 'react-native-vector-icons/AntDesign';
import {isEmpty} from 'lodash';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {usePublishContext} from '../context';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import {saveAppState} from '../../../actions/editorActions';
import Helpdocs from './helpDocsPanel';
import {makeToast} from '@/root/web/actions/toastActions';
import TextElement from '@/root/web/components-v2/base/TextElement';
import packageJson from '@/root/package.json';
import {selectCurrentPlanWithDetails} from '@/root/web/selectors/BillingSelector';
import Analytics from '@/root/web/lib/segment';
import PlaystoreDetails from './playstoreDetails';
import IosDetails from './iosDetails';

type LastBuildVersion_ApiResponse = {
  semVersion: string;
  version: string;
};

const BuildDetails: React.FC = () => {
  const {buildDetails, appDetails, setBuildDetails, alreadyExistingMetaData, setAlreadyExistingMetaData, isLegacyApp} =
    usePublishContext();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const dispatch = useDispatch();
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
  });

  const {user} = useSelector((state: EditorRootState) => state.user);
  const currentPlan = useSelector(selectCurrentPlanWithDetails)?.name;

  const isEnterpriseOrPlus = currentPlan === 'PLUS' || currentPlan === 'ENTERPRISE';

  let {
    isAppleSelected,
    isAppleAccordionOpen,
    accountType,
    hasExistingIOSApp,
    accountDetails,
    initialAccountDetails,
    isIOSBuildRequested,
    isDeveloperAccountConnected,
    ipadSupport,
    appTrackingPermission,
    appstoreBundleIds,
  } = buildDetails.ios;

  const {isAndroidBuildRequested} = buildDetails.android;
  const [requestConfirmationLoader, setRequestConfirmationLoader] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState<number | null>(null); // Track tooltip for specific input
  const [IOSConnectAccountError, setIOSConnectAccountError] = useState(''); // API error message placeholder
  const [isModalVisible, setModalVisible] = useState(false); // State for modal visibility
  const [disabledInputs, setDisabledInputs] = useState(false); // Disable inputs once a build is requested
  const [isRequestBuildVisible, setRequestBuildVisible] = useState(false);
  const [connectAccountLoader, setConnectAccountLoader] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const appleDeveloperAccountTypes = [
    {label: 'Yes. Individual Account', value: 'Customer Developer Account,Individual Type'},
    {label: 'Yes. Organization Account', value: 'Customer Developer Account,Organization Type'},
    {label: 'No', value: 'Apptile Developer Account'},
  ];
  const helpDocs = useRef([
    {
      title: 'How to check my Apple Developer Account type',
      url: 'https://help.apptile.com/en/articles/9490585-how-to-check-my-apple-developer-account-type',
    },
    {
      title: 'Invite Apptile to your Apple Developer Account',
      url: 'https://help.apptile.com/en/articles/7192278-invite-apptile-to-your-apple-developer-account',
    },
    {
      title: 'Find Entity Name, Team ID, Issuer ID, Key ID, P8 File',
      url: 'https://help.apptile.com/en/articles/9479809-how-to-find-entity-name-team-id-issuer-id-key-id-p8-file-on-app-store-connect',
    },

    {
      title: 'How to find the Bundle ID, Version Semver & Version No for Google Developer account',
      url: 'https://help.apptile.com/en/articles/9482240-how-to-find-bundle-id-version-semver-version-no-for-google-play-console',
    },
    {
      title: 'How to invite Apptile to your Google Developer Account',
      url: 'https://help.apptile.com/en/articles/7193835-invite-apptile-to-your-google-play-developer-account',
    },
    {
      title: 'How to find your Google Developer Name',
      url: 'https://help.apptile.com/en/articles/9490635-how-to-find-your-google-developer-account-name',
    },
  ]);

  const setMainDocRef = useRef(() => {});

  const [isConnectAccountVisible, setConnectAccountVisible] = useState(false);
  const [isConnectAccountDisabled, setConnectAccountDisabled] = useState(false);
  const [showExistingAppSection, setExistingAppSection] = useState(false);
  const [isIOSRequestDisabled, setIOSRequestDisabled] = useState(false);

  useEffect(() => {
    // Check if any accountDetails entry is empty

    const isAnyDetailEmpty = accountDetails.some(
      (detail: string) => isEmpty(detail) || detail === '', // Handle empty strings explicitly
    );

    // Set disabled to true if any detail is empty, otherwise false
    setConnectAccountDisabled(isAnyDetailEmpty || !buildDetails.assets.appStorePrivateKey);
  }, [accountDetails, buildDetails]);

  useEffect(() => {
    if (appstoreBundleIds?.length > 0) {
      setExistingAppSection(true);
    } else {
      setExistingAppSection(false);
    }
  }, [appstoreBundleIds]);

  useEffect(() => {
    if (buildDetails.ios.isDeveloperAccountConnected) {
      if (buildDetails.ios.hasExistingIOSApp) {
        if (buildDetails.ios.iosBundleId.trim('') !== '') {
          setIOSRequestDisabled(false);
        } else {
          setIOSRequestDisabled(true);
        }
      } else {
        setIOSRequestDisabled(false);
      }
      setRequestBuildVisible(true);
    }
  }, [buildDetails.ios]);

  // In PublishContext
  const updateIOSDetails = (key: string, value: any) => {
    setBuildDetails(prev => ({
      ...prev,
      ios: {
        ...prev.ios,
        [key]: value,
      },
    }));
  };

  const openAppstoreLink = () => {
    Linking.openURL('https://appstoreconnect.apple.com/apps');
  };

  const handleConnectAccount = async () => {
    Analytics.track('editor:buildpublishflow_connectAccountClicked', {});
    var isSecretsSuccesfullyUpdated = false;
    try {
      setConnectAccountLoader(true);

      // Prepare an array to hold only the changed credentials
      const changedSecrets = [];
      const secretClasses = ['teamId', 'appStoreIssuerId', 'appStoreApiKey'];
      // Iterate through the credentials and add only changed ones
      for (let i = 1; i <= 3; i++) {
        if (initialAccountDetails[i] !== accountDetails[i]) {
          changedSecrets.push({
            secret: accountDetails[i].trim(),
            secretClass: secretClasses[i - 1],
          });
        }
      }

      // Post only if there are changed credentials
      if (changedSecrets.length > 0) {
        await BuildManagerApi.postAppSecret(appId, changedSecrets);
        isSecretsSuccesfullyUpdated = true;
      }

      // Call the API to verify credentials
      await BuildManagerApi.verifyCredentials(appId);
      const responseBundleIDS = await BuildManagerApi.getAppStoreBundleIds(appId);
      updateIOSDetails('appstoreBundleIds', responseBundleIDS.data);

      // Assuming the API returns a successful response if credentials are valid
      setRequestBuildVisible(true); // Show Request Build button
      setIOSConnectAccountError(''); // Clear any previous errors
      setExistingAppSection(true); // Show the existing app section
      setConnectAccountLoader(false);

      updateIOSDetails('isDeveloperAccountConnected', true);
    } catch (error) {
      setConnectAccountLoader(false);
      // Handle errors thrown by the API
      console.log(error);
      const errorMessage = error?.response?.data?.message || 'Failed to verify credentials. Please try again.';
      setIOSConnectAccountError(errorMessage);
    } finally {
      if (isSecretsSuccesfullyUpdated) {
        updateIOSDetails('initialAccountDetails', [...accountDetails]);
      }
    }
  };

  const handleRequestBuild = () => {
    Analytics.track('editor:buildpublishflow_requestBuildClicked', {});
    setModalVisible(true); // Show the confirmation modal
  };

  const handleBuild = async (platform: 'android' | 'ios', version: string, semver: string) => {
    Analytics.track('editor:buildpublishflow_BuildTriggered', {
      appId,
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    });
    const buildPayload = {
      version,
      semVersion: semver,
      frameworkVersion: packageJson.version,
      platforms: [platform],
      triggeredBy: `${user.firstname} ${user.lastname}`,
      triggeredByEmail: user.email,
    };
    if (platform === 'ios') {
      buildPayload.enableIpadSupport = ipadSupport;
      buildPayload.enableAppTrackingTransparency = appTrackingPermission;

      buildPayload.isIndividualAccount = buildDetails?.ios?.accountType?.includes('Individual') ? true : false;
    }

    //These entire things should be removed when integrations are self serve
    buildPayload.enableApptileAnalytics = isEnterpriseOrPlus;
    buildPayload.enableLivelyPIP = isEnterpriseOrPlus;

    const response = await BuildManagerApi.createBuild(appId, buildPayload);
    let responseData;

    // Parse response data, if it's a string
    if (typeof response.data === 'string') {
      try {
        responseData = JSON.parse(response.data);
      } catch (error) {
        console.error('Failed to parse response data as JSON:', error);
        return;
      }
    } else {
      responseData = response.data;
    }

    const {errors, warnings, info, success} = responseData;

    let hasPendingBuildMessage = false;

    // Handle success messages

    // Handle info messages and check for pending build
    info?.forEach((message: string) => {
      dispatch(
        makeToast({
          content: message,
          appearances: 'info',
          duration: 30000,
        }),
      );

      if (message.includes('is already in progress')) {
        hasPendingBuildMessage = true;
      }
    });

    // Handle error messages
    errors?.forEach((message: string) => {
      dispatch(
        makeToast({
          content: message,
          appearances: 'error',
          duration: 30000,
        }),
      );
    });
  };
  const autoFetchIOSVersion = async (appId: string, platform: 'android' | 'ios') => {
    try {
      if (isEmpty(appId)) return;

      // Call the API and retrieve the response
      const response = await BuildManagerApi.getLatestBuildVersion<LastBuildVersion_ApiResponse>(appId, platform);
      const {semVersion: apiSemVersion, version, ...messages} = response.data ?? {}; // assuming `messages` contains warnings, info, success, errors

      let semVersion = apiSemVersion;

      const {semVersion: incrementedSemver, version: incrementedVersion} = await incrementVersionAndSemver(
        semVersion,
        version,
      );

      // Dispatch toast notifications based on messages from the API response

      return {
        semVersion: incrementedSemver,

        version: incrementedVersion,
      };
    } catch (err) {
      console.log(err, 'error fetching version');
      dispatch(
        makeToast({
          content: 'An error occurred while fetching the build version.',
          appearances: 'error',
          duration: 3000,
        }),
      );
      throw new Error('Error Fetching Version...');
    }
  };

  const incrementVersionAndSemver = async (semVersion: string, version: string) => {
    let semVersionArray = semVersion.split('.').map(Number);

    // Ensure semVersion has three parts (x.y.z)
    while (semVersionArray.length < 3) {
      semVersionArray.push(0);
    }

    // Increment the major version and reset minor & patch versions
    semVersionArray[0] += 1;
    semVersionArray[1] = 0;
    semVersionArray[2] = 0;

    return {
      semVersion: semVersionArray.join('.'),
      version: (Number.parseInt(version ?? '0') + 1).toString(),
    };
  };

  const handleConfirmIOSBuild = async () => {
    try {
      Analytics.track('editor:buildpublishflow_buildConfirmationIos');
      setRequestConfirmationLoader(true);

      // Ensure the latest 'isIOSBuildRequested' is updated in state before proceeding
      const updatedIOSDetails = {...buildDetails.ios, isIOSBuildRequested: true, showExistingAppSection};
      updateIOSDetails('isIOSBuildRequested', true);

      // Call `patchAppMetadata` with the latest details

      if (buildDetails.ios.accountType !== 'Apptile Developer Account' && buildDetails.ios.hasExistingIOSApp) {
        await BuildManagerApi.patchAppSettings(appId, {
          iosBundleIdentifier: updatedIOSDetails.iosBundleId,
        });
      }

      const newMetaData = {
        ...alreadyExistingMetaData,
        ios: {
          accountType: updatedIOSDetails.accountType,
          hasExistingIOSApp: updatedIOSDetails.hasExistingIOSApp,
          isIOSBuildRequested: updatedIOSDetails.isIOSBuildRequested,
          isDeveloperAccountConnected: updatedIOSDetails.isDeveloperAccountConnected,
          appStoreDevAccountName:
            buildDetails.ios.accountType === 'Apptile Developer Account'
              ? 'Clearsight Technologies Private Limited'
              : accountDetails[0],
          ipadSupport,
          appTrackingPermission,
        }, // Include the updated iOS details
      };
      BuildManagerApi.sendBuildAlert(appId, {appId, appName: appDetails.appName, ios: newMetaData.ios});

      if (hasExistingIOSApp) {
        const {version, semVersion} = await autoFetchIOSVersion(appId, 'ios');
        await handleBuild('ios', version, semVersion);
      } else {
        await handleBuild('ios', '1', '1.0.0');
      }

      await BuildManagerApi.patchAppMetadata(appId, {
        metadata: newMetaData,
      });

      setAlreadyExistingMetaData(newMetaData);

      if (!isAndroidBuildRequested) {
        onPublish();
      }

      setRequestConfirmationLoader(false);
      setModalVisible(false); // Close modal
      setDisabledInputs(true); // Disable inputs
      updateIOSDetails('isAppleAccordionOpen', false);

      // Show success modal if this is the final build request
      const isGoogleSelected = buildDetails.android.isGoogleSelected;
      const shouldShowModal = !isGoogleSelected || isAndroidBuildRequested;
      if (shouldShowModal) {
        setShowSuccessModal(true);
      }
    } catch (error) {
      setRequestConfirmationLoader(false);
      updateIOSDetails('isIOSBuildRequested', false);
      console.error('Error during build confirmation:', error);
      setModalVisible(false);

      // Handle error (e.g., show a notification or revert UI changes)
      setDisabledInputs(false); // Re-enable inputs if needed
      dispatch(
        makeToast({
          content: 'Unknown Error Occured! Please try after some time.',
          appearances: 'error',
          duration: 5000,
        }),
      );
    }
  };

  const handleCancelIOSBuild = () => {
    Analytics.track('editor:buildpublishflow_buildCancelIos');
    setModalVisible(false); // Close modal without action
  };

  return (
    <>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          {/* Left Panel */}
          <View style={styles.leftPanel}>
            <ScrollView style={styles.card} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
              <TextElement style={styles.heading}>Where do you wish to publish your app?</TextElement>
              <View style={styles.storeCheckBoxStyles}>
                <CheckBoxWidget
                  label="Apple Appstore"
                  checked={isIOSBuildRequested ? true : isAppleSelected}
                  isDisabled={isIOSBuildRequested}
                  onChange={value => updateIOSDetails('isAppleSelected', value)}
                />
                <CheckBoxWidget
                  label="Google Playstore"
                  checked={isAndroidBuildRequested ? true : buildDetails.android.isGoogleSelected}
                  isDisabled={isAndroidBuildRequested}
                  onChange={value => {
                    Analytics.track('editor:buildpublishflow_selectGoogleStore', {appId, selected: value});
                    setBuildDetails(prev => ({
                      ...prev,
                      android: {
                        ...prev.android,
                        isGoogleSelected: value,
                      },
                    }));
                  }}
                />
              </View>

              {/* iOS Details */}
              <IosDetails
                onPublish={onPublish}
                isAndroidBuildRequested={isAndroidBuildRequested}
                helpDocs={helpDocs.current}
                setMainDocRef={setMainDocRef}
                tooltipVisible={tooltipVisible}
                setTooltipVisible={setTooltipVisible}
                appleDeveloperAccountTypes={appleDeveloperAccountTypes}
                handleRequestBuild={handleRequestBuild}
                autoFetchIOSVersion={autoFetchIOSVersion}
                handleBuild={handleBuild}
                alreadyExistingMetaData={alreadyExistingMetaData}
                setAlreadyExistingMetaData={setAlreadyExistingMetaData}
                appDetails={appDetails}
                isLegacyApp={isLegacyApp}
              />

              {/* Google Playstore Details */}
              <PlaystoreDetails
                onPublish={onPublish}
                isIOSBuildRequested={isIOSBuildRequested}
                helpDocs={helpDocs.current}
                setMainDocRef={setMainDocRef}
                tooltipVisible={tooltipVisible}
                setTooltipVisible={setTooltipVisible}
              />
            </ScrollView>
          </View>

          {/* Right Panel */}
          <Helpdocs
            helpDocs={helpDocs.current}
            setMainDocFn={fn => {
              setMainDocRef.current = fn;
            }}
          />
        </View>

        {/* Confirmation Modal */}
        <ConfirmationModal
          visible={isModalVisible}
          onCancel={handleCancelIOSBuild}
          onConfirm={handleConfirmIOSBuild}
          isLoaderActive={requestConfirmationLoader}
          message="App & Account details can't be edited after the build is requested. Do you wish to Continue?"
        />

        {/* Success Modal */}
        <PublishSuccessModal
          visible={showSuccessModal}
          onClose={() => setShowSuccessModal(false)}
          appStoreAccountName={alreadyExistingMetaData?.ios?.appStoreDevAccountName || 'Apptile'}
          playStoreAccountName={
            buildDetails.android.isGoogleSelected
              ? alreadyExistingMetaData?.android?.playStoreDevAccountName || 'Floryo'
              : undefined
          }
        />
      </View>
      <Footer canProceed={isLegacyApp || isAndroidBuildRequested || isIOSBuildRequested} />
    </>
  );
};

interface MessageBannerProps {
  type: 'success' | 'error'; // Type of the message
  message: string; // Message text
}

const MessageBanner: React.FC<MessageBannerProps> = ({type, message}) => {
  const iconProps = {
    name: type === 'success' ? 'checkcircle' : 'exclamationcircleo',
    size: 20,
    color: type === 'success' ? '#6AC370' : '#DC3545',
  };

  return (
    <View style={[styles.banner, type === 'success' ? styles.successConnectAccount : styles.errorConnectAccount]}>
      <TextElement style={styles.connectAccountMessage}>
        <Icon {...iconProps} />
      </TextElement>
      <TextElement color="SECONDARY">{message}</TextElement>
    </View>
  );
};

const styles = StyleSheet.create({
  accountConnectedCard: {
    alignItems: 'center',
    backgroundColor: '#F5FFF7',
    borderRadius: 12,
    padding: 24,
    marginBottom: 24,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#D5F5E3',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  accountConnectedIconCircle: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#E9F9ED',
    alignItems: 'center',
    justifyContent: 'center',
  },
  accountConnectedTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2F855A',
    marginTop: 8,
    marginBottom: 2,
  },
  accountConnectedName: {
    fontSize: 15,
    color: '#333',
    marginBottom: 14,
  },
  disconnectButton: {
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 8,
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#DC3545',
  },
  disconnectButtonText: {
    color: '#DC3545',
    fontWeight: '500',
    fontSize: 15,
  },
  infoBox: {
    backgroundColor: '#F3F3F3',
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#D9D9D9',
    marginBottom: 20,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    fontWeight: '200',
  },
  infoText: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 20,
    fontWeight: '400',
  },
  boldText: {
    fontWeight: '600',
    color: '#333',
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
    color: '#A0A0A0',
  },

  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  successCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    height: 140,
    marginBottom: 20,
    gap: 10,
  },
  successIcon: {
    fontSize: 32,
    color: '#38A169',
    marginTop: 30,
  },
  successMessage: {
    fontSize: 16,
    fontWeight: '400',
  },

  wrapper: {
    flex: 1,

    backgroundColor: '#ECE9E1',
    padding: 20,
    height: '100%',
  },
  content: {
    flexDirection: 'row', // Ensure left and right panels are side by side
    justifyContent: 'space-between',
    flex: 1,
    height: '100%',
  },
  leftPanel: {
    width: '60%', // Occupies 60% of the width
    marginRight: 20,
    height: '100%',
    flexGrow: 1,
  },
  rightPanel: {
    width: '38%', // Occupies 38% of the width, leaving space for padding
  },
  card: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    // marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ECECEC',
    height: '100%',
    // overflow: 'scroll',
  },
  innerCard: {
    // backgroundColor: '#F9F9F9',
    // padding: 15,
    borderRadius: 12,
    marginVertical: 10,

    // borderWidth: 1,
    // borderColor: '#E0E0E0',
  },
  heading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginVertical: 15,
  },
  subHeading: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginVertical: 10,
  },
  checkboxGroup: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  checkbox: {
    flex: 1,
    padding: 10,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  checkboxWithError: {
    marginTop: 10,
    marginBottom: 15,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  checked: {
    backgroundColor: '#E6F0FF',
    borderColor: '#007BFF',
  },
  checkboxText: {
    fontSize: 14,
    color: '#333',
  },
  accordionHeader: {
    paddingVertical: 20,
    paddingHorizontal: 15,
    backgroundColor: '#FFF',
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  accordionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  radioGroup: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  radioButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 8,
    marginRight: 10,
  },
  radioSelected: {
    backgroundColor: '#E6F0FF',
    borderColor: '#007BFF',
  },
  radioText: {
    fontSize: 14,
    color: '#333',
  },
  requestButton: {
    backgroundColor: '#1060E0',
    fontWeight: 200,
    width: 'max-content',
    minWidth: 150,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  requestButtonDisabled: {
    backgroundColor: '#A9A9A9', // Light gray for a disabled look
    fontWeight: '200', // Keep the same weight
    width: 'max-content',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 34,
    fontSize: 14,
    alignItems: 'center',
    marginVertical: 20,
    opacity: 0.6, // Reduce opacity for disabled effect
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  note: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  // highlight: {
  //   fontWeight: '600',
  //   color: '#007BFF',
  // },
  highlight: {
    backgroundColor: '#F4F4F4', // Soft pastel yellow for subtle highlight
    borderColor: '#DEDEDE', // Light border to enhance visibility
    borderWidth: 1,
  },
  input: {
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    marginBottom: 15,
    backgroundColor: '#F3F3F3',
  },
  placeholder: {
    height: 150,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 10,
  },

  apiCheckbox: {
    flexDirection: 'row', // Ensures checkbox and text are aligned in a row
    alignItems: 'center', // Vertically centers the checkbox and text
    padding: 10, // Adds some padding for better touch area
    borderWidth: 1, // Border for the checkbox
    borderColor: '#CCCCCC', // Light grey border color
    borderRadius: 8, // Rounded corners
    backgroundColor: '#FFFFFF', // Background color
  },
  errorText: {
    color: '#D9534F', // Red color to indicate an error
    fontSize: 14, // Standard readable font size
    marginTop: 5, // Adds spacing above the error text
    lineHeight: 18, // Adjusts line height for better readability
  },
  connectButton: {
    backgroundColor: '#007BFF', // Primary blue color for the button
    padding: 12, // Adds padding for better click area
    borderRadius: 8, // Rounded corners
    alignItems: 'center', // Centers text inside the button
    marginTop: 15, // Adds spacing from the element above
  },
  linkContainer: {
    flexDirection: 'row',
    gap: 10,
    margin: 5,
  },
  link: {
    color: '#1060E0',
    fontSize: 14,
    fontWeight: 500,

    textDecorationLine: 'underline',
    marginBottom: 15,
  },
  banner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    width: '100%',
  },
  successConnectAccount: {
    backgroundColor: '#F0FFF5',
    borderColor: '#28A745',
    gap: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  errorConnectAccount: {
    backgroundColor: '#FFF5F5',
    borderColor: '#DC3545',
    gap: 10,
    alignItems: 'center',
  },
  connectAccountMessage: {
    fontSize: 16,

    color: '#333',
    flexShrink: 1,
  },
  storeCheckBoxStyles: {flexDirection: 'row', marginVertical: 10, gap: 50, justifyContent: 'flex-start'},
  accordionContainer: {flexDirection: 'row', justifyContent: 'space-between'},

  button: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#007bff',
    borderRadius: 5,
    alignItems: 'center',
  },

  linkText: {
    color: '#007bff',
    textDecorationLine: 'underline',
  },
});

export default BuildDetails;
