import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core'; // For Checkmark icon
import TextElement from '../../../components-v2/base/TextElement';
import {darkTheme} from '../styles';

type CheckBoxWidgetProps = {
  label: string;
  checked: boolean;
  onChange: (value: boolean) => void;
  isDisabled?: boolean;
};

export const CheckBoxWidget: React.FC<CheckBoxWidgetProps> = ({label, checked, onChange, isDisabled = false}) => {
  const [isChecked, setChecked] = useState(checked);

  useEffect(() => {
    setChecked(checked);
  }, [checked]);
  const handlePress = () => {
    if (!isDisabled) {
      const newValue = !isChecked;
      setChecked(newValue);
      onChange(newValue);
    }
  };

  return (
    <View style={styles.checkboxContainer}>
      <Pressable
        style={[styles.checkbox, isChecked && styles.checkedBox, isDisabled && styles.disabledBox]}
        onPress={handlePress}
        disabled={isDisabled}>
        {isChecked && (
          <MaterialCommunityIcons
            name="check"
            size={16}
            color="#FFF" // Checkmark color
          />
        )}
      </Pressable>
      <TextElement style={[styles.label, isDisabled && styles.disabledText]}>{label}</TextElement>
    </View>
  );
};

const styles = StyleSheet.create({
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1.5,
    borderColor: darkTheme.checkboxActive,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkedBox: {
    backgroundColor: darkTheme.checkboxActive,
    borderColor: darkTheme.checkboxActive,
  },
  disabledBox: {
    backgroundColor: darkTheme.checkboxInactive,
    borderColor: darkTheme.checkboxInactive,
  },
  label: {
    fontSize: 14,
    color: darkTheme.checkboxText,
  },
  disabledText: {
    // color: '#9CA3AF',
  },
});

export default CheckBoxWidget;
