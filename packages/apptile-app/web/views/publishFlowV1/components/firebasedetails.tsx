import React, {useState, useRef, useEffect} from 'react';
import Analytics from '@/root/web/lib/segment';
import {View, StyleSheet, Text, TextInput, TouchableOpacity, Image, ScrollView, Linking} from 'react-native';
import RadioGroup from './radioGroup';
import {MaterialCommunityIcons} from 'apptile-core';
import {usePublishContext} from '../context';
import Footer from './footer';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../../store/EditorRootState';
import Tooltip from './tooltip';
import HelpDocs from './helpDocsPanel';
import TextElement from '../../../components-v2/base/TextElement';
import CheckBoxWidget from './checkBoxWidget';
import FileUploadButton from './fileUploadButton';
import {darkTheme} from '../styles';

const FirebaseDetails: React.FC = () => {
  const {
    firebaseProjectId,
    setFirebaseProjectId,
    initialFirebaseProjectId,
    setInitialFirebaseProjectId,
    isFirebaseInviteChecked,
    setFirebaseInviteChecked,
    buildDetails,
    setBuildDetails,
  } = usePublishContext();
  const {isIOSBuildRequested} = buildDetails.ios;
  const [tooltipVisible, setTooltipVisible] = useState<number | null>(null); // Track txooltip for specific input
  const {isAndroidBuildRequested} = buildDetails.android;
  const assets = buildDetails.assets;

  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const [selectedOption, setSelectedOption] = useState<string>(assets.firebaseServiceAccountKeyFile ? 'Yes' : 'No');

  const firebaseOptions = [
    {label: 'Yes', value: 'Yes'},
    {label: 'No', value: 'No'},
  ];

  const setMainDocRef = useRef(null);
  const [errors, setErrors] = useState(false);
  const [loading, setLoading] = useState(false);

  const helpDocRefs = useRef([
    {
      title: 'How to download firebase service account key file',
      url: 'https://help.apptile.com/en/articles/********-download-firebase-service-account-json-file',
    },
    {
      title: 'Steps to create a Firebase account',
      url: 'https://help.apptile.com/en/articles/9482340-how-to-create-a-firebase-account',
    },
  ]);

  useEffect(() => {
    if (isAndroidBuildRequested || isIOSBuildRequested) {
      setFirebaseInviteChecked(true);
    }
  }, [isAndroidBuildRequested, isIOSBuildRequested]);

  useEffect(() => {
    if (buildDetails.assets.firebaseServiceAccountKeyFile) {
      setSelectedOption('Yes');
    }
  }, [buildDetails.assets.firebaseServiceAccountKeyFile]);

  const openFirebaseLink = () => {
    Analytics.track('editor:buildpublishflow_openFirebaseConsole', {});
    Linking.openURL('https://console.firebase.google.com');
  };

  const handleBlur = (value: string) => {
    if (isAndroidBuildRequested || isIOSBuildRequested) return;
    Analytics.track('editor:buildpublishflow_firebaseProjectIdBlur', {value});
    setErrors(value?.trim() === '');
  };

  const onSubmit = async () => {
    Analytics.track('editor:buildpublishflow_firebaseSubmitClicked', {});
    try {
      setLoading(true);
      if (initialFirebaseProjectId !== firebaseProjectId) {
        Analytics.track('editor:buildpublishflow_firebaseProjectIdChanged', {value: firebaseProjectId});
        await BuildManagerApi.patchAppSettings(appId, {
          firebaseProjectId,
        });
        setInitialFirebaseProjectId(firebaseProjectId);
      }
    } catch (err) {
      // error('Firebase Error', err);
      Analytics.track('editor:buildpublishflow_firebaseError', {error: String(err)});
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <View style={styles.wrapper}>
        {/* Main Content */}
        <View style={styles.content}>
          {/* Left Panel */}
          <View style={styles.leftPanel}>
            <View style={styles.card}>
              {/* Section Header */}
              <View style={styles.sectionHeader}>
                <TextElement style={styles.sectionTitle}>2. Add Firebase Details</TextElement>
              </View>

              {!(isIOSBuildRequested || isAndroidBuildRequested) && (
                <>
                  {/* Section 1: Firebase Account */}

                  {/* Section 2: Info and Inputs */}

                  <View style={styles.infoBox}>
                    <MaterialCommunityIcons name="information-outline" color={darkTheme.primaryText} size={18} />
                    <TextElement style={styles.infoText}>
                      Creating a Firebase Account is mandatory to ensure you have{' '}
                      <TextElement style={styles.boldText}>Push Notifications</TextElement> &{' '}
                      <TextElement style={styles.boldText}>Google Analytics</TextElement> configured for your App.
                    </TextElement>
                  </View>
                </>
              )}

              <View>
                <View onMouseEnter={() => setTooltipVisible(25)} onMouseLeave={() => setTooltipVisible(null)}>
                  <FileUploadButton
                    label="Firebase Service Account Key File"
                    fileType="file"
                    disabled={isAndroidBuildRequested || isIOSBuildRequested}
                    accept=".json"
                    assetClass="firebaseServiceAccountKeyFile"
                    iconName="cloud-upload-outline"
                    style={{backgroundColor: darkTheme.secondaryCardBackground}}
                    onFileSelect={file => {
                      Analytics.track('editor:buildpublishflow_firebaseServiceAccountKeyUploaded', {});
                      setBuildDetails({
                        ...buildDetails,
                        assets: {
                          ...buildDetails.assets,
                          firebaseServiceAccountKeyFile: file,
                        },
                      });
                    }}
                    existingFileName={
                      buildDetails?.assets?.firebaseServiceAccountKeyFile ? 'firebaseServiceAccountKey.json' : null
                    }
                    existingFileURL={buildDetails?.assets?.firebaseServiceAccountKeyFile}
                  />
                  {(isIOSBuildRequested || isAndroidBuildRequested) && tooltipVisible === 25 && (
                    <Tooltip
                      message="Firebase details can't be changed if either IOS or Android build is requested."
                      visible
                    />
                  )}
                </View>

                {/* <TextElement style={styles.heading} color="SECONDARY" fontSize="sm">
                  Firebase Project ID
                </TextElement> */}
                {/* <View onMouseEnter={() => setTooltipVisible(25)} onMouseLeave={() => setTooltipVisible(null)}>
                  <TextInput
                    readOnly={isIOSBuildRequested || isAndroidBuildRequested}
                    style={
                      !(isIOSBuildRequested || isAndroidBuildRequested)
                        ? [styles.input, errors.firebaseProjectId && styles.errorBorder]
                        : styles.disabledInput
                    }
                    placeholder="Add project ID here"
                    placeholderTextColor={'#939393'}
                    value={firebaseProjectId}
                    onChangeText={setFirebaseProjectId}
                    onFocus={() => {
                      setMainDocRef.current(helpDocRefs.current[1]);
                    }}
                    onBlur={() => handleBlur(firebaseProjectId)}
                  />
                  {errors && (
                    <View style={styles.textInputErrorContainer}>
                      <MaterialCommunityIcons name="information-outline" size={14} color="red" />
                      <Text style={styles.errorText}>Firebase Project Id is required.</Text>
                    </View>
                  )}
                  {(isIOSBuildRequested || isAndroidBuildRequested) && tooltipVisible === 25 && (
                    <Tooltip
                      message="Firebase details can't be changed if either IOS or Android build is requested."
                      visible
                    />
                  )}
                </View> */}
              </View>
              {/*
              {!(isAndroidBuildRequested || isIOSBuildRequested) && (
                <CheckBoxWidget
                  label="I have invited and shared owner access of firebase <NAME_EMAIL>"
                  checked={isFirebaseInviteChecked}
                  isDisabled={false}
                  onChange={value => {
                    setFirebaseInviteChecked(value);
                  }}
                />
              )} */}

              {/* {!(isIOSBuildRequested || isAndroidBuildRequested) && (
                <View style={styles.infoBox}>
                  <MaterialCommunityIcons name="information-outline" color="#000000" size={18} />
                  <TextElement style={styles.infoText}>
                    Please share Owner Access to{' '}
                    <TextElement style={styles.boldText} color="SECONDARY" fontSize="sm">
                      <EMAIL>
                    </TextElement>{' '}
                    on your Firebase Project before you go forward.
                  </TextElement>
                </View>
              )} */}
            </View>
          </View>

          {/* Right Panel */}
          <HelpDocs
            helpDocs={helpDocRefs.current}
            setMainDocFn={fn => {
              setMainDocRef.current = fn;
            }}
          />
        </View>
      </View>
      <Footer
        canProceed={
          isAndroidBuildRequested ||
          isIOSBuildRequested ||
          ((!loading && buildDetails?.assets?.firebaseServiceAccountKeyFile) ?? false)
        }
        onNext={onSubmit}
      />
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: darkTheme.background,
    paddingHorizontal: 20,
    paddingVertical: 10,
    height: '100%',
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: darkTheme.primaryText,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  stepText: {
    fontSize: 16,
    color: darkTheme.secondaryText,
  },
  completedStep: {
    color: darkTheme.success,
    fontWeight: 'bold',
  },
  activeStep: {
    color: darkTheme.primaryText,
    fontWeight: 'bold',
  },
  content: {
    flexDirection: 'row',
    flex: 1,
    height: '100%',
  },
  leftPanel: {
    width: '60%',
    paddingRight: 20,
    height: '100%',
  },
  rightPanel: {
    width: '50%',
    height: '100%',
  },
  card: {
    backgroundColor: darkTheme.cardBackground,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    height: '100%',
  },
  heading: {
    fontWeight: '600',
    marginBottom: 15,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 15,
  },
  radioButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 6,
    marginRight: 10,
    backgroundColor: '#F9F9F9',
  },
  radioButtonActive: {
    backgroundColor: '#007BFF',
    borderColor: '#007BFF',
  },
  radioButtonText: {
    fontSize: 16,
    color: '#666',
  },
  radioButtonTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  infoBox: {
    backgroundColor: darkTheme.secondaryCardBackground,
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    marginBottom: 15,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    fontWeight: '200',
  },
  infoText: {
    fontSize: 14,
    color: darkTheme.primaryText,
    lineHeight: 20,
    fontWeight: '200',
  },
  boldText: {
    fontWeight: '600',
    color: darkTheme.primaryText,
  },
  link: {
    textDecorationLine: 'underline',
    marginBottom: 15,
  },
  input: {
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    marginBottom: 15,
    backgroundColor: darkTheme.inputBackground,
    borderWidth: 1,
    borderColor: darkTheme.inputBorder,
    color: darkTheme.inputText,
    outline: 'none',
  },
  noteBox: {
    backgroundColor: '#F9F9F9',
    padding: 15,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ECECEC',
  },
  note: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  highlightText: {
    fontWeight: '600',
    color: '#007BFF',
  },
  image: {
    width: '100%',
    height: 150,
    borderRadius: 6,
    marginBottom: 15,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  videoGuide: {
    marginTop: 20,
  },
  subHeading: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  videoPlaceholder: {
    height: 150,
    backgroundColor: '#ECECEC',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#CCCCCC',
  },
  videoText: {
    fontSize: 14,
    color: '#999',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  backButton: {
    padding: 10,
    backgroundColor: '#CCCCCC',
    borderRadius: 5,
  },
  nextButton: {
    padding: 10,
    backgroundColor: '#007BFF',
    borderRadius: 5,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  linkContainer: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 20,
  },
  disabledInput: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
    color: '#A0A0A0',
    padding: 12,
  },
  textInputErrorContainer: {
    flexDirection: 'row',

    gap: 5,
  },
  errorBorder: {
    borderWidth: 1,
    borderColor: '#FF2424',
  },
  errorText: {
    color: '#FF2424',
    fontSize: 12,
    fontWeight: 400,
    marginBottom: 10,
  },
  storeCheckBoxStyles: {},
  sectionHeader: {
    width: '100%',
    height: 51,
    gap: 8,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomWidth: 1,
    borderBottomColor: darkTheme.primaryText,
    paddingTop: 12,
    paddingRight: 18,
    paddingBottom: 12,
    paddingLeft: 18,
    backgroundColor: darkTheme.headerBackground,
    marginBottom: 20,
    marginLeft: -20,
    marginRight: -20,
    paddingLeft: 38,
    paddingRight: 38,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: darkTheme.primaryText,
    lineHeight: 24,
  },
});

export default FirebaseDetails;
