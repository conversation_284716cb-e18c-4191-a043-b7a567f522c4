// Dark theme styles for Publish Flow V1
export const darkTheme = {
  // Main backgrounds
  background: '#000000',
  headerBackground: '#1A1D20',
  cardBackground: '#1A1D20',
  secondaryCardBackground: '#292B32',
  modalBackground: '#2A2A2A',

  // Text colors
  primaryText: '#FFFFFF',
  secondaryText: '#B0B0B0',
  tertiaryText: '#808080',

  // Border colors
  primaryBorder: '#333333',
  secondaryBorder: '#2A2A2A',
  activeBorder: '#1060E0',

  // Button colors
  primaryButton: '#1060E0',
  primaryButtonText: '#FFFFFF',
  secondaryButton: '#333333',
  secondaryButtonText: '#FFFFFF',
  disabledButton: '#404040',
  disabledButtonText: '#666666',

  // Input colors
  inputBackground: '#2A2A2A',
  inputBorder: '#404040',
  inputText: '#FFFFFF',
  inputPlaceholder: '#808080',

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Stepper colors
  stepperActive: '#1060E0',
  stepperCompleted: '#4CAF50',
  stepperInactive: '#404040',
  stepperLine: '#333333',

  // Radio button colors
  radioActive: '#1060E0',
  radioInactive: '#404040',
  radioText: '#FFFFFF',

  // Checkbox colors
  checkboxActive: '#1060E0',
  checkboxInactive: '#404040',
  checkboxText: '#FFFFFF',

  // Tooltip colors
  tooltipBackground: '#2A2A2A',
  tooltipText: '#FFFFFF',
  tooltipBorder: '#404040',

  // Help panel colors
  helpPanelBackground: '#1A1D20',
  helpPanelText: '#FFFFFF',
  helpPanelBorder: '#333333',

  // Upload button colors
  uploadButtonBackground: '#292B32',
  uploadButtonBorder: '#404040',
  uploadButtonText: '#FFFFFF',

  // Build list colors
  buildCardBackground: '#1A1A1A',
  buildCardBorder: '#333333',
  buildStatusSuccess: '#4CAF50',
  buildStatusPending: '#FF9800',
  buildStatusError: '#F44336',
};

// Light theme for comparison/fallback
export const lightTheme = {
  background: '#FFFFFF',
  headerBackground: '#FFFFFF',
  cardBackground: '#FFFFFF',
  modalBackground: '#FFFFFF',

  primaryText: '#333333',
  secondaryText: '#666666',
  tertiaryText: '#999999',

  primaryBorder: '#E5E5E5',
  secondaryBorder: '#D0D0D0',
  activeBorder: '#1060E0',

  primaryButton: '#1060E0',
  primaryButtonText: '#FFFFFF',
  secondaryButton: '#FFFFFF',
  secondaryButtonText: '#000000',
  disabledButton: '#A0A0A0',
  disabledButtonText: '#FFFFFF',

  inputBackground: '#F3F3F3',
  inputBorder: '#DADADA',
  inputText: '#333333',
  inputPlaceholder: '#999999',

  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  stepperActive: '#1060E0',
  stepperCompleted: '#4CAF50',
  stepperInactive: '#E0E0E0',
  stepperLine: '#D3D3D3',

  radioActive: '#1060E0',
  radioInactive: '#CCC',
  radioText: '#555',

  checkboxActive: '#1060E0',
  checkboxInactive: '#CCC',
  checkboxText: '#555',

  tooltipBackground: '#FFFFFF',
  tooltipText: '#333333',
  tooltipBorder: '#E5E5E5',

  helpPanelBackground: '#FFFFFF',
  helpPanelText: '#333333',
  helpPanelBorder: '#E5E5E5',

  uploadButtonBackground: '#FFFFFF',
  uploadButtonBorder: '#DADADA',
  uploadButtonText: '#333333',

  buildCardBackground: '#FFFFFF',
  buildCardBorder: '#E5E5E5',
  buildStatusSuccess: '#4CAF50',
  buildStatusPending: '#FF9800',
  buildStatusError: '#F44336',
};

// Export current theme (can be switched based on user preference)
export const currentTheme = darkTheme;
