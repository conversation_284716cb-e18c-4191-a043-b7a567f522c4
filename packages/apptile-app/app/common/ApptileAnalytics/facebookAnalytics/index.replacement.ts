import {AppEventsLogger, Settings} from 'react-native-fbsdk-next';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import {IApptileAnalyticsEvent} from '../ApptileAnalyticsTypes';
import _ from 'lodash';

const FBEventParams = AppEventsLogger.AppEventParams;
const FBEvents = AppEventsLogger.AppEvents;

const defaultEventHandler = (_eventProperty: IApptileAnalyticsEvent['properties']) => {};

const addToCartEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const valueToSum = parseFloat(_.get(eventProperty, 'price', 0));

  const payload = {
    [FBEventParams.ContentType]: _.get(eventProperty, 'productType'),
    [FBEventParams.Content]: JSON.stringify([
      {
        id: _.get(eventProperty, 'productId'),
        quantity: _.get(eventProperty, 'quantity'),
      },
    ]),
    [FBEventParams.Currency]: _.get(eventProperty, 'currency'),
  };

  if (typeof valueToSum !== 'number') {
    logger.info('sendToFacebook', 'addToCart', FBEvents.AddedToCart, {payload});
    AppEventsLogger.logEvent(FBEvents.AddedToCart, payload);
  } else {
    logger.info('sendToFacebook', 'addToCart', FBEvents.AddedToCart, {payload, valueToSum});
    AppEventsLogger.logEvent(FBEvents.AddedToCart, valueToSum, payload);
  }
};

const initiateCheckoutEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const valueToSum = _.sumBy(
    _.map(_.get(eventProperty, 'items', []), itemEntry => parseFloat(_.get(itemEntry, 'price', 0))),
  );

  const payload = {
    [FBEventParams.Content]: JSON.stringify(
      _.map(_.get(eventProperty, 'items', []), itemEntry => {
        return {
          id: _.get(itemEntry, 'productId'),
          quantity: _.get(itemEntry, 'quantity'),
        };
      }),
    ),
    [FBEventParams.Currency]: _.get(eventProperty, 'currency'),
    [FBEventParams.NumItems]: _.get(eventProperty, 'items', []).length,
  };

  if (typeof valueToSum !== 'number') {
    logger.info('sendToFacebook', 'initiateCheckout', FBEvents.InitiatedCheckout, {payload});
    AppEventsLogger.logEvent(FBEvents.InitiatedCheckout, payload);
  } else {
    logger.info('sendToFacebook', 'initiateCheckout', FBEvents.InitiatedCheckout, {payload, valueToSum});
    AppEventsLogger.logEvent(FBEvents.InitiatedCheckout, valueToSum, payload);
  }
};

const purchaseEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const valueToSum = _.sumBy(
    _.map(_.get(eventProperty, 'items', []), itemEntry => parseFloat(_.get(itemEntry, 'price', 0))),
  );

  const payload = {
    [FBEventParams.Content]: JSON.stringify(
      _.map(_.get(eventProperty, 'items', []), itemEntry => {
        return {
          id: _.get(itemEntry, 'productId'),
          quantity: _.get(itemEntry, 'quantity'),
        };
      }),
    ),
    [FBEventParams.Currency]: _.get(eventProperty, 'currency'),
    [FBEventParams.NumItems]: _.get(eventProperty, 'items', []).length,
  };

  if (typeof valueToSum !== 'number') {
    logger.info('sendToFacebook', 'purchase', FBEvents.Purchased, {payload});
    AppEventsLogger.logEvent(FBEvents.Purchased, payload);
  } else {
    logger.info('sendToFacebook', 'purchase', FBEvents.Purchased, {payload, valueToSum});
    AppEventsLogger.logEvent(FBEvents.Purchased, valueToSum, payload);
  }
};

const addToWishlistEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const valueToSum = parseFloat(_.get(eventProperty, 'price', 0));

  const payload = {
    [FBEventParams.ContentType]: _.get(eventProperty, 'productType'),
    [FBEventParams.Content]: JSON.stringify([
      {
        id: _.get(eventProperty, 'productId'),
        quantity: _.get(eventProperty, 'quantity'),
      },
    ]),
    [FBEventParams.Currency]: _.get(eventProperty, 'currency'),
  };

  if (typeof valueToSum !== 'number') {
    logger.info('sendToFacebook', 'addToWishlist', FBEvents.AddedToWishlist, {payload});
    AppEventsLogger.logEvent(FBEvents.AddedToWishlist, payload);
  } else {
    logger.info('sendToFacebook', 'addToWishlist', FBEvents.AddedToWishlist, {payload, valueToSum});
    AppEventsLogger.logEvent(FBEvents.AddedToWishlist, valueToSum, payload);
  }
};

const searchEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    [FBEventParams.SearchString]: _.get(eventProperty, 'term'),
  };

  logger.info('sendToFacebook', 'search', FBEvents.Searched, {payload});
  AppEventsLogger.logEvent(FBEvents.Searched, payload);
};

const pageViewEventHandler = (eventProperty: IApptileAnalyticsEvent['properties']) => {
  const payload = {
    [FBEventParams.ContentType]: 'pageView',
    [FBEventParams.ContentID]: _.get(eventProperty, 'pageName'),
  };

  logger.info('sendToFacebook', 'pageView', FBEvents.ViewedContent, {payload});
  AppEventsLogger.logEvent(FBEvents.ViewedContent, payload);
};

const customHandlers = {
  addToCart: addToCartEventHandler,
  initiateCheckout: initiateCheckoutEventHandler,
  purchase: purchaseEventHandler,
  addToWishlist: addToWishlistEventHandler,
  search: searchEventHandler,
  pageView: pageViewEventHandler,
};

const standardEvent = Object.keys(customHandlers);

export class Facebook {
  static async setAdvertiserTracking() {
    const result = await check(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);
    switch (result) {
      case RESULTS.GRANTED:
        await Settings.setAdvertiserTrackingEnabled(true);
        break;
      default:
        await Settings.setAdvertiserTrackingEnabled(false);
    }
  }

  static async prepareSdk() {
    Settings.setGraphAPIVersion('v17.0');
    await Facebook.setAdvertiserTracking();
  }

  static async sendEvent(eventName: string, eventProperty: IApptileAnalyticsEvent['properties']) {
    try {
      if (!eventName || !standardEvent.includes(eventName)) return;
      eventProperty = eventProperty?.toJS ? eventProperty.toJS() : eventProperty ?? {};

      const eventHandler = this.getHandler(eventName);
      eventHandler(eventProperty);
    } catch (err) {
      logger.error(err);
    }
  }

  static getHandler(eventName: string) {
    const handler = _.get(customHandlers, eventName, defaultEventHandler);
    return handler;
  }
}

//! Custom event support is disable due to parameter datatypes constraints in SDk, cause events not reflect in dashboard
