export interface ApplePayContact {
  city: string;
  country: string;
  state: string;
  postalCode: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  addressLine1?: string;
  addressLine2?: string;
}

export interface ApplePayLineItem {
  label: string;
  amount: number;
}

export type ApplePayPaymentNetwork = 'VISA' | 'MASTERCARD' | 'DISCOVER' | 'AMERICAN_EXPRESS' | 'DINERS_CLUB';

export interface ApplePayErrorItem {
  code?: number;
  message: string;
  userInfo?: any;
}
export interface ApplePaySessionPayload {
  checkoutId: string;
  payeeName: string;
  currencyCode: string;
  paymentDue: number;
  countryCode: string;
  supportedNetworks: ApplePayPaymentNetwork[];
  lineItems: ApplePayLineItem[];
  requiresShipping: boolean;
}
export interface ApplePayShippingMethod {
  id?: string;
  label: string;
  amount: number;
  description?: string;
}

export interface ApplePayContactUpdateResponse {
  lineItems: ApplePayLineItem[];
  shippingMethods?: ApplePayShippingMethod[];
  errors?: ApplePayErrorItem[];
}
export type ApplePayShippingUpdateResponse = ApplePayContactUpdateResponse;

export type ApplePayContactUpdateCallback = (contact: ApplePayContact) => Promise<ApplePayContactUpdateResponse>;
export type ApplePayShippingUpdateCallback = (
  contact: ApplePayShippingMethod,
) => Promise<ApplePayShippingUpdateResponse>;

export type ApplePayAuthorizePaymentCallback = (paymentResponse: ApptilePaymentResponse) => Promise<any>;

export type ApplePayServiceStatic = {
  initSession: (merchantIdentifier: string, paymentSession: ApplePaySessionPayload) => Promise<void>;
  canMakePayment: () => Promise<boolean>;
};

export interface ApplePayPKPaymentResponse {
  shippingContact?: ApplePayPKContact;
  shippingMethod?: ApplePayPKShippingMethod;
  billingContact: ApplePayPKContact;
  token: ApplePayPKToken;
}

export interface ApplePayPKShippingMethod {
  amount: string;
  label: string;
  detail: string;
  identifier: string;
  type: number;
}

export interface ApplePayPKToken {
  paymentData: string;
  transactionIdentifier: string;
  paymentMethod: {
    network: string;
    type: string;
    displayName: string;
  };
}

export interface ApplePayPKContact {
  emailAddress: string;
  postalAddress: {
    street: string;
    city: string;
    country: string;
    postalCode: string;
    subLocality: string;
    subAdministrativeArea: string;
    state: string;
    isoCountryCode: string;
  };
  name: {
    nameSuffix: string;
    givenName: string;
    namePrefix: string;
    middleName: string;
    nickname: string;
    familyName: string;
  };
  phoneNumber: string;
}

export interface ApptileShippingMethodResponse extends ApplePayShippingMethod {
  identifier?: string;
}
export interface ApptilePaymentToken {
  paymentData: string;
  transactionIdentifier: string;
  paymentMethodNetwork: string;
  paymentMethodType: string;
  paymentMethodDisplayName: string;
}

export interface ApptilePaymentResponse {
  shippingContact?: ApplePayContact;
  billingContact: ApplePayContact;
  shippingMethod?: ApplePayShippingMethod;
  token: ApptilePaymentToken;
}
