import {NativeEventEmitter, NativeModules} from 'react-native';
import _ from 'lodash';

import {
  ApplePayAuthorizePaymentCallback,
  ApplePayContact,
  ApplePayContactUpdateCallback,
  ApplePayContactUpdateResponse,
  ApplePayPKPaymentResponse,
  ApplePayServiceStatic,
  ApplePaySessionPayload,
  ApplePayShippingUpdateCallback,
  ApptilePaymentToken,
  ApptileShippingMethodResponse,
} from './ApplePayServiceTypes';

const ApplePay = NativeModules['ApptileApplePay'];

const eventEmitter = new NativeEventEmitter(ApplePay); //new NativeEventEmitter(NativeModules.ApplePayEventEmitter);

const ApplePayService = ((): ApplePayServiceStatic => {
  return {
    initSession: async (
      merchantIdentifier: string,
      paymentSession: ApplePaySessionPayload,
      onShippingContactUpdated: ApplePayContactUpdateCallback,
      onShippingMethodUpdated: ApplePayShippingUpdateCallback,
      onPaymentAuthorized: ApplePayAuthorizePaymentCallback,
    ) => {
      ApplePay.createSession(merchantIdentifier, paymentSession);
      const canMakePayment = await ApplePay.canMakePayments();
      if (!canMakePayment) logger.info('Apple pay unable to make payment canMakePayment: ', canMakePayment);
      if (canMakePayment) {
        eventEmitter.removeAllListeners('onShippingContactUpdated');
        eventEmitter.removeAllListeners('onShippingMethodUpdated');
        eventEmitter.removeAllListeners('onAuthorizePayment');

        eventEmitter.addListener('onShippingContactUpdated', async event => {
          logger.info('Apple pay event: onShippingContactUpdated init: ', event);
          let contact = transformToApplePayContact(event);
          let updates: ApplePayContactUpdateResponse = {
            lineItems: paymentSession.lineItems,
          };
          if (onShippingContactUpdated) {
            updates = await onShippingContactUpdated(contact);
          }
          logger.info('Apple pay event: onShippingContactUpdated completed: ', JSON.stringify(updates));
          ApplePay.updateContactCallback(updates);
        });

        eventEmitter.addListener('onShippingMethodUpdated', async event => {
          logger.info('Apple pay event: onShippingMethodUpdated init: ', event);
          if (onShippingMethodUpdated) {
            const lineItems = await onShippingMethodUpdated(event);
            if (lineItems) {
              ApplePay.updateShippingMethodCallback({
                lineItems: lineItems,
              });
            }
            logger.info('Apple pay event: onShippingMethodUpdated completed: ', lineItems);
          }
        });

        eventEmitter.addListener('onAuthorizePayment', async event => {
          logger.info('Apple pay event: onAuthorizePayment init: ', event);
          const apptilePaymentResponse = transformToApptileAuthorizedPaymentResponse(event);

          if (apptilePaymentResponse?.shippingContact && onShippingContactUpdated) {
            await onShippingContactUpdated(apptilePaymentResponse?.shippingContact);
            logger.info(
              'Apple pay event: onAuthorizePayment shipping Contat Updated: ',
              apptilePaymentResponse?.shippingContact,
            );
          }

          if (onPaymentAuthorized) {
            logger.info('Apple pay payment authorization init: ', apptilePaymentResponse);
            const orderCreated = await onPaymentAuthorized(apptilePaymentResponse);
            logger.info('Apple pay onPaymentAuthorized ran orderCreated: ', orderCreated);

            if (orderCreated) {
              ApplePay.complete();
              logger.info('Apple pay Order Created Successfully: ', orderCreated);
            }
          }
        });
        const paymentResponse = await ApplePay.initApplePay();
        logger.info(paymentResponse);
      } else {
        logger.info('Apple pay can not be initiated as canMakePayment:', canMakePayment);
      }
    },
    canMakePayment: async () => {
      if (!ApplePay || !ApplePay.canMakePayments) {
        logger.info('Apple pay canMakePayment:', ApplePay);
        return false;
      }
      const canMakePayment = await ApplePay.canMakePayments();
      logger.info('Apple pay canMakePayment:', canMakePayment);
      return !!canMakePayment;
    },
  };
})();

export default ApplePayService;

function transformToApplePayContact(pkContact): ApplePayContact {
  let street = pkContact?.postalAddress?.street;
  let addressLine1, addressLine2;
  if (!_.isEmpty(street)) {
    let lines: string[] = street.split('\n');
    addressLine1 = lines?.length > 0 ? lines[0] : '';
    addressLine2 = lines?.length > 1 ? lines.slice(1).join(' ') : '';
  }
  return {
    city: pkContact?.postalAddress?.city,
    country: pkContact?.postalAddress?.country,
    state: pkContact?.postalAddress?.state,
    postalCode: pkContact?.postalAddress?.postalCode,
    firstName: pkContact?.name?.givenName,
    lastName: pkContact?.name?.familyName,
    phone: pkContact?.phoneNumber,
    email: pkContact?.emailAddress,
    addressLine1,
    addressLine2,
  };
}

function transformToApptileShippingMethod(
  pkshippingMethod: ApplePayPKPaymentResponse['shippingMethod'],
): ApptileShippingMethodResponse {
  return {
    amount: _.toNumber(pkshippingMethod?.amount) ?? 0,
    label: pkshippingMethod?.label ?? '',
    identifier: pkshippingMethod?.identifier,
    description: pkshippingMethod?.detail,
  };
}

function transformToApptileToken(pkToken: ApplePayPKPaymentResponse['token']): ApptilePaymentToken {
  return {
    paymentData: pkToken?.paymentData ?? '',
    transactionIdentifier: pkToken?.transactionIdentifier,
    paymentMethodNetwork: pkToken?.paymentMethod?.network,
    paymentMethodType: pkToken?.paymentMethod?.type,
    paymentMethodDisplayName: pkToken?.paymentMethod?.displayName,
  };
}

function transformToApptileAuthorizedPaymentResponse(pkPayment: ApplePayPKPaymentResponse): any {
  return {
    shippingContact: pkPayment?.shippingContact ? transformToApplePayContact(pkPayment.shippingContact) : null,
    shippingMethod: pkPayment?.shippingMethod ? transformToApptileShippingMethod(pkPayment.shippingMethod) : null,
    billingContact: transformToApplePayContact(pkPayment.billingContact),
    token: transformToApptileToken(pkPayment.token),
  };
}
// const onPress = async () => {
//   logger.info(applePay);

//   applePay.createSession('com.apple.net', {
//     checkoutId: 'checkoutId',
//     needsShipping: true,
//     currencyCode: 'USD',
//     paymentDue: '100.00',
//     countryCode: 'US',
//     supportedNetworks: ['Visa'],
//   });

//   const canMakePayment = await applePay.canMakePayments();
//   if (canMakePayment) {
//     const paymentResponse = applePay.initApplePay();
//     // logger.info(paymentResponse);
//   }

//   eventEmitter.removeAllListeners('onShippingContactUpdated');
//   eventEmitter.removeAllListeners('onShippingMethodUpdated');
//   eventEmitter.removeAllListeners('onAuthorizePayment');

//   // setTimeout(() => {
//   //   applePay.abort();
//   // }, 5000);

//   eventEmitter.addListener('onShippingContactUpdated', event => {
//     applePay.updateSession({
//       params: {
//         summaryItems: [
//           {
//             label: 'Total',
//             amount: '100',
//           },
//           {
//             label: 'Taxes',
//             amount: '10',
//           },
//         ],
//       },
//     });
//     logger.info(`updateSession event`, event);
//   });

//   eventEmitter.addListener('onShippingMethodUpdated', event => {
//     logger.info(`event`, event);
//     applePay.updateSession({
//       params: {
//         summaryItems: [
//           {
//             label: 'Total',
//             amount: '100',
//           },
//           {
//             label: 'Taxes',
//             amount: '10',
//           },
//         ],
//       },
//     });
//   });

//   eventEmitter.addListener('onAuthorizePayment', event => {
//     logger.info(`event`, event);
//     applePay.complete();
//   });

//   logger.info(canMakePayment);

//   // applePay.initApplePay((err, name) => {
//   //   logger.info(err, name);
//   // });
// };
