export const LOGI<PERSON>_MESSAGES = {
  SUCCESS: {
    LOGIN_SUCCESS: 'Logged in successfully.',
  },
  ERROR: {
    EMPTY_FIELDS: 'Please fill all the fields.',
    LOGIN_FAILED: 'Invalid email-id or password. Please try again.',
    <PERSON><PERSON><PERSON>_INIT_FAILED: 'Failed to initialize Zego engine. Please check your setup.',
    EMPTY_EMAIL: 'Email address cannot be empty. Please enter your email.',
    EMPTY_PASSWORD: 'Password cannot be empty. Please enter your password.',
  },
} as const;

export const DASHBOARD_MESSAGES = {
  SUCCESS: {
    STREAM_DELETION: 'Stream deleted successfully.',
    STREAM_CREATED: 'Stream created successfully.',
  },
};

export const VIDEO_CLIPPING = {
  SUCCESS: {
    CLIP_ACTIVATION: 'Clip Activated successfully.',
    CLIP_DELETION: 'Clip Disabled successfully.',
    SELECTION_SUCCESS: 'Product selected successfully.',
  },
  ERROR: {
    SELECTION_ERROR: 'Product selection failure. Please try again.',
  },
};

export const LIVE_STREAM = {
  SUCCESS: {
    PRODUCT_ADDED: 'Product added successfully.',
    PRODUCT_REMOVED: 'Product removed successfully.',
  },
  ERROR: {
    FB_COMMENTS: 'Error in fetching facebook comments. Will retry in few seconds.',
    INSTA_COMMENTS:
      'Error in fetching instagram comments. Will retry in few seconds. If issue persists, please try to rejoin.',
    INSTA_CHANNEL_DISCONNECT: 'Your Instagram channel has been disconnected. Please recreate stream to continue.',
    REFRSH: 'Something went wrong. Please try to join stream again.',
    CONNECTION_ISSUE: 'Unable to connnect. Please contact support',
    PRODUCT_EXIST: 'Product already exists.',
  },
};

export const CREATE_STREAM = {
  SUCCESS: {
    PRODUCT_ADDED: 'Product added successfully.',
    PRODUCT_REMOVED: 'Product removed successfully.',
    COLLECTION_ADDED: 'Products from collection added successfully.',
    IMAGE_UPLOAD: 'Image uploaded successfully.',
    IMAGE_REMOVED: 'Image removed successfully.',
  },
  ERROR: {
    SHOPIFY_PRODUCT_404: 'Product does not have onlineStoreUrl.',
    PRODUCT_EXIST: 'Product already exists.',
    COLLECTION_EXIST: 'Collection already exists.',
    IMAGE_UPLOAD: 'Something went wrong. Please try again.',
  },
};
