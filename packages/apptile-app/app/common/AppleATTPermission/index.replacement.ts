import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';

const checkATTPermission = async () => {
  try {
    const result = await check(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);
    switch (result) {
      case RESULTS.UNAVAILABLE:
        logger.info('This feature is not available (on this device / in this context)');
        break;
      case RESULTS.DENIED:
        logger.info('The permission has not been requested / is denied but requestable');
        return request(PERMISSIONS.IOS.APP_TRACKING_TRANSPARENCY);
      case RESULTS.LIMITED:
        logger.info('The permission is limited: some actions are possible');
        break;
      case RESULTS.GRANTED:
        logger.info('The permission is granted');
        break;
      case RESULTS.BLOCKED:
        logger.info('The permission is denied and not requestable anymore');
        break;
    }
  } catch (error) {
    logger.error(error);
  }
};

export default checkATTPermission;
