export interface IStampedGetRewardsResponse {
  customer: {
    customerId: string;
    customerEmail: string;
    customerFirstName: string;
    customerLastName: string;
    customerTags: any;
    customerLocale: any;
    vipTierTitle: string;
    vipTierId: number;
    vipTierGoalValue: number;
    vipTierEligibleValue: number;
    urlReferral: string;
    totalOrders: number;
    totalSpent: number;
    isAcceptMarketing: boolean;
    dateBirthday: any;
    dateVipTierEntered: string;
    dateVipTierUntil: any;
    metafields: any;
    authToken: string;
  };
  points: {
    points: number;
    points_current_with_name: string;
  };
  campaigns: {
    earnings: Array<{
      campaignEvent: number;
      isCompleted: boolean;
      totalCount: number;
      campaignEventString: string;
      pointsFullName: string;
      id: number;
      title: string;
      titlePublic: string;
      description: string;
      urlImage: string;
      points?: number;
      isSystem: boolean;
      isActive: boolean;
      isEnabledNotifications: boolean;
      dateCreated: string;
      campaignRulesList: Array<{
        id: number;
        title: any;
        description: any;
        columnType: string;
        compareValue: string;
        matchType: string;
        inclusionType: number;
        rewardCampaignSpend: any;
        dateCreated: string;
      }>;
      pointsMultiplier?: number;
      isEnabledRules?: boolean;
      intervalDelay?: number;
      limitNumberPerCustomer?: number;
      limitDaysPerCustomer?: number;
      isEnabledLimits?: boolean;
    }>;
    spendings: Array<{
      codePrefix?: string;
      discountValue: number;
      pointsMinimum?: number;
      pointsMaximum?: number;
      entitledCollectionIds: string;
      campaignType: number;
      isRedeemable: boolean;
      totalCount: number;
      campaignTypeString: string;
      pointsFullName: string;
      id: number;
      title: string;
      titlePublic: string;
      description: string;
      urlImage: string;
      points: number;
      pointsMultiplier?: number;
      isSystem: boolean;
      isActive: boolean;
      dateCreated: string;
      campaignRulesList: any[];
      entitledProductIds?: string;
      isEnabledNotifications?: boolean;
    }>;
    coupons: any[];
    referrals: {
      friend: {
        id: number;
        title: string;
        titlePublic: string;
        titleActivity: any;
        description: any;
        urlImage: any;
        urlNotification: any;
        intervalDelay: any;
        codePrefix: any;
        discountValue: number;
        allocationMethod: any;
        limitAmountMinimum: number;
        limitDurationDays: any;
        entitledCollectionIds: string;
        entitledProductIds: any;
        entitledVariantIds: any;
        campaignType: number;
        rewardType: number;
        isDeleted: any;
        shopId: number;
        dateCreated: string;
        campaignTypeString: string;
        rewardTypeString: string;
      };
      advocate: {
        id: number;
        title: string;
        titlePublic: string;
        titleActivity: any;
        description: any;
        urlImage: any;
        urlNotification: any;
        intervalDelay: any;
        codePrefix: any;
        discountValue: number;
        allocationMethod: any;
        limitAmountMinimum: any;
        limitDurationDays: any;
        entitledCollectionIds: string;
        entitledProductIds: any;
        entitledVariantIds: any;
        campaignType: number;
        rewardType: number;
        isDeleted: any;
        shopId: number;
        dateCreated: string;
        campaignTypeString: string;
        rewardTypeString: string;
      };
      refer_title: string;
      refer_description: string;
    };
    vip_tiers: Array<{
      id: number;
      title: string;
      description: any;
      goalValue: number;
      urlImage: string;
      listBenefits: Array<{id: number; description: string; vipTierId: number; dateCreated: string}>;
      listRewards: Array<{
        id: number;
        title: string;
        titlePublic: string;
        titleActivity: any;
        description: any;
        urlImage: any;
        urlNotification: any;
        codePrefix: any;
        discountValue: number;
        allocationMethod: any;
        limitAmountMinimum: any;
        limitDurationDays: any;
        entitledCollectionIds: any;
        entitledProductIds: any;
        entitledVariantIds: any;
        rewardType: number;
        isDeleted: any;
        vipTierId: number;
        dateCreated: string;
        rewardTypeString: string;
      }>;
      dateCreated: string;
    }>;
  };
  links: any;
  html: any;
  referrer: any;
  branding: boolean;
  d: any[];
}

export interface TStampedCustomer {
  customerId: string;
  customerEmail: string;
  customerFirstName: string;
  customerLastName: string;
  vipTierTitle: string;
  vipTierId: number;
  totalOrders: number;
  totalSpent: number;
}

export interface TStampedPoint {
  points: number;
  pointsCurrentWithName: string;
}

export interface TStampedEarning {
  isCompleted: boolean;
  totalCount: number;
  campaignEventString: string;
  pointsFullName: string;
  id: number;
  title: string;
  titlePublic: string;
  description: string;
  urlImage: string;
  points?: number;
  isSystem: boolean;
  isActive: boolean;
  isEnabledNotifications: boolean;
  dateCreated: string;
  pointsMultiplier?: number;
  isEnabledRules?: boolean;
}

export interface TStampedSpending {
  campaignEvent: number;
  isCompleted: boolean;
  totalCount: number;
  campaignEventString: string;
  pointsFullName: string;
  id: number;
  title: string;
  titlePublic: string;
  description: string;
  urlImage: string;
  points: number;
  isSystem: boolean;
  isActive: boolean;
  isEnabledNotifications: boolean;
  dateCreated: string;
  campaignRulesList: Array<string>;
}

export interface TStampedMembershipTier {
  id: number | undefined;
  title: string;
  description: any;
  goalValue: number;
  urlImage: string;
  listBenefits: Array<{id: number; description: string; vipTierId: number; dateCreated: string}>;
  listRewards: Array<{
    id: number;
    title: string;
    titlePublic: string;
    discountValue: number;
    rewardType: number;
    isDeleted: any;
    vipTierId: number;
    dateCreated: string;
    rewardTypeString: string;
  }>;
  dateCreated: string;
}

export interface TApptileGetRewardsResponse {
  customer: TStampedCustomer;
  points: TStampedPoint;
  earnings: Array<TStampedEarning>;
  vipTiers: Array<TStampedMembershipTier>;
}

export interface IStampedReviewsResponse {
  page: number;
  data: TApptileGetReviewsData[];
  lang: string;
  shop: string;
  template: string;
  elementId: number;
  total: number;
  totalAll: number;
  totalAllWithNPS: number;
  rating: number;
  ratingAll: number;
  translations: {
    label_verified_buyer: string;
    label_shop_now: string;
  };
  isPro: boolean;
}

export interface TApptileGetReviewsData {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewDate: string;
  reviewVerifiedType: number;
  reviewReply: string;
  productId: number;
  productName: string;
  productSKU: string;
  productVariantName: string;
  shopProductId: number;
  productUrl: string;
  productImageUrl: string;
  productImageLargeUrl: string;
  productImageThumbnailUrl: string;
  avatar: string;
  location: string;
  countryIso: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  isRecommend: boolean;
  reviewType: number;
  widgetType: string;
  reviewOptionsList: {
    message: string;
    value: string;
  }[];
  featured: boolean;
}

export interface TApptileGetReviewsResponse {
  page: number;
  reviews: TApptileGetReviewsData[];
  lang: string;
  shop: string;
  template: string;
  elementId: number;
  total: number;
  totalAll: number;
  totalAllWithNPS: number;
  rating: number;
  ratingAll: number;
  translations: {
    label_verified_buyer: string;
    label_shop_now: string;
  };
  isPro: boolean;
}

export interface IStampedWriteReviewsResponse {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewVerifiedType: number;
  productId: number;
  productName: string;
  productUrl: string;
  productImageUrl: string;
  location: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  reviewType: number;
  reviewOptionsList: any[];
  featured: boolean;
}

export interface TApptileWriteReviewsResponse {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewVerifiedType: number;
  productId: number;
  productName: string;
  productUrl: string;
  productImageUrl: string;
  location: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  reviewType: number;
  reviewOptionsList: any[];
  featured: boolean;
}

export interface IStampedCreateProductReviewBadgeResponse {
  productId: string;
  rating: number;
  count: number;
  countQuestions: number;
  c: boolean;
  badge: string;
  badgeqna: string;
}
