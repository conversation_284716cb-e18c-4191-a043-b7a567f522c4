import {ActionHandler} from '../../../triggerAction';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
// import _ from 'lodash';
import {StampedQueryDetails, executeQuery, runQuery} from '..';
export interface StampedActionInterface {
  getCustomerRewards: ActionHandler;
}

export interface stampedProductsPayload {
  customerId: string;
  customerEmail: string;
}

export type stampedRemoveProductsPayload = Omit<stampedProductsPayload, 'productHandle'>;

class StampedActions implements StampedActionInterface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    keys: string[],
  ) {
    const modelUpdates = [] as Array<{selector: Array<string>; newValue: any}>;
    keys.forEach((key, index) => {
      const lineItemsSelector = selector.concat([key]);
      modelUpdates.push({
        selector: lineItemsSelector,
        newValue: lineItems[index],
      });
    });

    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  getCustomerRewards = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: stampedProductsPayload,
  ) => {
    let {customerId, customerEmail} = params;
    try {
      const {data} = await runQuery(model, config, 'GetCustomerRewards', params);
      dispatch(
        modelUpdateAction([
          {
            selector: [config.get('id'), 'customerRewards'],
            newValue: data,
          },
        ]),
      );
    } catch (err) {
      console.log(`Got Error while running action getCustomerRewards`, err);
      return {customerId, customerEmail};
    }
    // runQuery
  };
}

const stampedActions = new StampedActions();
export default stampedActions;
