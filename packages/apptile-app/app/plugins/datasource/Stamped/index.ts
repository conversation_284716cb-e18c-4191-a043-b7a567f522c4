import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call, put, select, spawn} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IStampedCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformCreateProductReviewBadge,
  TransformCreateReviews,
  TransformGetReviews,
  TransformGetReviewsV1,
  TransformGetRewards,
} from './transformer';
import {generateHmac} from './utils';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import {IStampedGetRewardsResponse} from './types';
import _ from 'lodash';
import {modelUpdateAction} from 'apptile-core';
import stampedActions from './actions/stampedActions';

export interface StampedPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  publicKey: string;
  privateKey: string;
  storeHash: string;
  storeUrl: string;
  shopifyDS: string;
  getCustomerRewards: any;
  customerRewards: IStampedGetRewardsResponse;
  queryRunner: any;
  headers: Record<string, any>;
}

type IEditableParams = Record<string, any>;

export type StampedQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseStampedQuerySpec: Partial<StampedQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
    publicKey: '',
    privateKey: '',
    storeHash: '',
    storeUrl: '',
    headers: {},
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};

const StampedApiRecords: Record<string, Partial<StampedQueryDetails>> = {
  GetCustomerRewards: {
    ...baseStampedQuerySpec,
    queryType: 'post',
    endpoint: '/v2/rewards/init',
    endpointResolver: (endpoint, inputParams) => {
      const {publicKey, storeHash} = inputParams;
      const queryParams = {
        apiKey: publicKey,
        sId: storeHash,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    inputResolver: (inputVariables, inputParams) => {
      const {privateKey} = inputParams;
      const {customerId, customerEmail} = inputVariables;
      return {
        customerId: customerId,
        customerEmail: customerEmail,
        authToken: generateHmac(customerId + customerEmail, privateKey).toString(),
      };
    },
    transformer: TransformGetRewards,
    editableInputParams: {
      customerId: '',
      customerEmail: '',
    },
  },

  GetCustomerReviews: {
    ...baseStampedQuerySpec,
    queryType: 'get',
    endpoint: '/widget/reviews',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {publicKey, storeUrl, after, ...rest} = inputParams;
      const queryParams = {
        storeUrl: storeUrl,
        apiKey: publicKey,
        ...rest,
      };

      //calulate the next page and set it to the query params
      getNextPage ? (queryParams.page = after ? after + 1 : 1) : null;

      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformGetReviews,
    isPaginated: true,

    editableInputParams: {
      productId: '',
      sortReviews: '',
      minRating: 0,
      take: 2,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productId} = inputVariables;
      return !!productId;
    },
  },

  GetCustomerReviewsV1: {
    ...baseStampedQuerySpec,
    queryType: 'get',
    endpoint: '/widget/reviews',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {publicKey, storeUrl, after, ...rest} = inputParams;
      const queryParams = {
        storeUrl: storeUrl,
        apiKey: publicKey,
        ...rest,
      };

      //calulate the next page and set it to the query params
      getNextPage ? (queryParams.page = after ? after + 1 : 1) : null;

      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformGetReviewsV1,
    isPaginated: true,

    editableInputParams: {
      productId: '',
      sortReviews: '',
      minRating: 0,
      take: 2,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productId} = inputVariables;
      return !!productId;
    },
  },

  CreateCustomerReviews: {
    ...baseStampedQuerySpec,
    queryType: 'post',
    endpoint: '/reviews3',
    endpointResolver: (endpoint, inputParams) => {
      const {publicKey, storeHash} = inputParams;
      const queryParams = {
        sId: storeHash,
        apiKey: publicKey,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    transformer: TransformCreateReviews,
    inputResolver: (inputVariables, inputParams) => {
      const {
        productId,
        author,
        email,
        location,
        reviewRating,
        reviewTitle,
        reviewMessage,
        reviewRecommendProduct,
        productName,
        productSKU,
        productImageUrl,
        productUrl,
        reviewSource,
      } = inputVariables;

      return {
        productId,
        author,
        email,
        location,
        reviewRating,
        reviewTitle,
        reviewMessage,
        reviewRecommendProduct,
        productName,
        productSKU,
        productImageUrl,
        productUrl,
        reviewSource,
      };
    },
    editableInputParams: {
      productId: '',
      author: '',
      email: '',
      location: '',
      reviewRating: '',
      reviewTitle: '',
      reviewMessage: '',
      reviewRecommendProduct: '',
      productName: '',
      productSKU: '',
      productImageUrl: '',
      productUrl: '',
      reviewSource: '',
    },
  },

  CreateProductReviewBadge: {
    ...baseStampedQuerySpec,
    queryType: 'post',
    endpoint: '/widget/badges',
    endpointResolver: (endpoint, inputParams) => {
      const queryParams = {
        isIncludeBreakdown: 'true',
        isincludehtml: 'true',
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },

    transformer: TransformCreateProductReviewBadge,
    inputResolver: (inputVariables, inputParams) => {
      const {productIds} = inputVariables;

      const {publicKey, storeUrl} = inputParams;

      return {
        productIds: productIds.map((id: string) => {
          return {
            productId: id,
          };
        }),
        apiKey: publicKey,
        storeUrl,
      };
    },
    editableInputParams: {
      productIds: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productIds} = inputVariables;
      return !!productIds;
    },
  },

  RedeemPoints: {
    contextInputParams: {
      headers: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'},
      ...baseStampedQuerySpec,
    },
    isPaginated: false,
    queryType: 'post',
    endpoint: '/v2/rewards/redeem',
    endpointResolver: (endpoint, inputParams) => {
      const {apiKey, sId, campaignId, points} = inputParams;
      const queryParams = {
        apiKey: apiKey,
        sId: sId,
        campaignId: campaignId,
        points: points,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      customerId: '',
      customerEmail: '',
      points: '',
      campaignId: '',
      apiKey: '',
      sId: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  getCustomerRewards: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return stampedActions.getCustomerRewards;
    },
    actionMetadata: {
      editableInputParams: {
        customerId: '',
        customerEmail: '',
      },
    },
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'stamped',
  type: 'datasource',
  name: 'Stamped Integration',
  description: 'Stamped Integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const StampedEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api-v3.Stamped.io',
      },
    },
    {
      type: 'codeInput',
      name: 'publicKey',
      props: {
        label: 'Stamped Public Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'privateKey',
      props: {
        label: 'Stamped Private Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'storeHash',
      props: {
        label: 'Stamped Store Hash',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'storeUrl',
      props: {
        label: 'Stamped Store URL',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyDS',
      props: {
        label: 'Shopify Datasource',
        placeholder: '{{shopify}}',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<StampedPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, StampedPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<StampedQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(
          queryDetails.queryType,
          endpoint,
          {...typedDataVariables},
          {
            ...options,
            headers: {...queryDetails.headers},
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = StampedApiRecords[queryName];
  const queryDetails = StampedApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'Stamped',
  config: {
    apiBaseUrl: 'https://api-v3.Stamped.io',
    publicKey: '',
    privateKey: '',
    storeHash: '',
    storeUrl: '',
    getCustomerRewards: 'action',
    queryRunner: 'queryrunner',
    shopifyDS: '',
  } as StampedPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<StampedPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      const authHeaders = dsModelValues?.get('authHeaders') ?? {};
      config.headers = {...config.headers, ...authHeaders};
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    const shopifyDSModel = dsModelValues?.get('shopifyDS');
    let queryRunner = dsModelValues?.get('queryRunner');

    const customerRewards = dsModelValues?.get('customerRewards');

    const updateAvailable = _.isEmpty(customerRewards) ? true : false;

    if (!pageLoad) {
      if (
        !_.isEmpty(queryRunner) &&
        shopifyDSModel?.loggedInUserAccessToken?.accessToken &&
        shopifyDSModel?.loggedInUser?.id &&
        updateAvailable
      ) {
        const inputParams = {
          customerId: shopifyDSModel?.loggedInUser?.id
            ? parseInt(shopifyDSModel?.loggedInUser?.id.split('/').pop())
            : 0,
          customerEmail: shopifyDSModel?.loggedInUser?.email,
        };
        console.log('STAMPED FETCH REWARDS', inputParams);
        yield spawn(function* () {
          try {
            pageModels = state.stageModel.getModelValue([]);
            dsModelValues = pageModels.get(dsConfig.get('id'));

            const {data} = yield call(runQuery, dsModelValues, dsConfig, 'GetCustomerRewards', inputParams);
            console.log(`STAMPED Data`, data);

            yield put(
              modelUpdateAction([
                {
                  selector: [dsConfig.get('id'), 'customerRewards'],
                  newValue: data,
                },
              ]),
            );
          } catch (error) {
            console.log(`Error fetching STAMPED REWARDS`, error);
            // TODO: show Toast
          }
        });
        console.log('STAMPED FETCH WISHLIST');
      }
      return;
    }

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'x-shopify-app-id': dsConfig.config?.get('appId') ?? '',
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return StampedApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = StampedApiRecords && StampedApiRecords[queryName] ? StampedApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IStampedCredentials): Partial<StampedPluginConfigType> | boolean {
    const {apiBaseUrl, publicKey, privateKey, storeHash, storeUrl} = credentials;
    if (!(apiBaseUrl && publicKey && privateKey && storeHash && storeUrl)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      publicKey: publicKey,
      privateKey: privateKey,
      storeHash: storeHash,
      storeUrl: storeUrl,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'publicKey', 'privateKey', 'storeHash', 'storeUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'stamped';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = StampedApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    // if (queryName == 'GetCustomerRewards') {
    //   yield put(
    //     modelUpdateAction([
    //       {
    //         selector: [dsConfig.get('id'), 'customerRewards'],
    //         newValue: response?.data,
    //       },
    //     ]),
    //   );
    // }
    return response;
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: StampedEditors,
});
