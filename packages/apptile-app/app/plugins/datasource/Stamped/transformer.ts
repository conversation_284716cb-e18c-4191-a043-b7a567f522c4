import _ from 'lodash';
import {
  IStampedCreateProductReviewBadgeResponse,
  IStampedGetRewardsResponse,
  IStampedReviewsResponse,
  IStampedWriteReviewsResponse,
  TApptileCreateProductReviewBadgeResponse,
  TApptileGetReviewsResponse,
  TApptileGetRewardsResponse,
  TApptileWriteReviewsResponse,
  TStampedEarning,
  TStampedMembershipTier,
  TStampedSpending,
} from './types';

const transformMembershipTier = (vipTier: any): TStampedMembershipTier => {
  return _.pick(vipTier, [
    'id',
    'title',
    'description',
    'titlePublic',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'pointsMultiplier',
    'goalValue',
    'listBenefits',
    'listRewards',
  ]);
};

const transformEarning = (earning: any): TStampedEarning => {
  return _.pick(earning, [
    'id',
    'title',
    'description',
    'titlePublic',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'pointsMultiplier',
  ]);
};

const transformSpendings = (spending: any): TStampedSpending => {
  return _.pick(spending, [
    'campaignEvent',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'id',
    'title',
    'titlePublic',
    'description',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'campaignRulesList',
  ]);
};

export const TransformGetRewards = (data: IStampedGetRewardsResponse) => {
  const {customer, points, campaigns} = data;
  const {earnings, vip_tiers, spendings} = campaigns;
  const {points_current_with_name, ...restPoints} = points;
  const result = {
    customer: _.pick(customer, [
      'customerId',
      'customerEmail',
      'customerFirstName',
      'customerLastName',
      'vipTierTitle',
      'vipTierId',
      'totalOrders',
      'totalSpent',
    ]),
    points: {
      ...restPoints,
      pointsCurrentWithName: points_current_with_name,
    },
    vipTiers: vip_tiers && vip_tiers.length > 0 ? _.map(vip_tiers, vip_tier => transformMembershipTier(vip_tier)) : [],
    earnings: earnings && earnings.length > 0 ? _.map(earnings, earning => transformEarning(earning)) : [],
    spending: spendings && spendings.length > 0 ? _.map(spendings, spending => transformSpendings(spending)) : [],
  } as TApptileGetRewardsResponse;

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};

export const TransformGetReviews = (reviewData: IStampedReviewsResponse) => {
  const {data, ...rest} = reviewData;
  const result = {
    reviews:
      data &&
      data.length !== 0 &&
      data.map(o =>
        _.pick(o, [
          'id',
          'author',
          'reviewTitle',
          'reviewMessage',
          'reviewRating',
          'reviewDate',
          'reviewUserPhotos',
          'reviewUserVideos',
          'reviewVerifiedType',
          'reviewReply',
          'reviewReplyDate',
          'productId',
          'productName',
          'productSKU',
          'productUrl',
          'productImageUrl',
          'productImageLargeUrl',
          'productImageThumbnailUrl',
          'productDescription',
          'avatar',
          'location',
          'reviewVotesUp',
          'reviewVotesDown',
          'userReference',
          'dateCreated',
          'dateReplied',
          'reviewType',
          'widgetType',
          'reviewOptionsList',
        ]),
      ),
    ...rest,
  } as TApptileGetReviewsResponse;

  return {
    data: result,
    hasNextPage: reviewData.data?.length !== 0,
    paginationMeta: {after: reviewData.page},
  };
};

export const TransformGetReviewsV1 = (reviewData: IStampedReviewsResponse) => {
  const {data} = reviewData;
  const result = data.map(o =>
    _.pick(o, [
      'id',
      'author',
      'reviewTitle',
      'reviewMessage',
      'reviewRating',
      'reviewDate',
      'reviewUserPhotos',
      'reviewUserVideos',
      'reviewVerifiedType',
      'reviewReply',
      'reviewReplyDate',
      'productId',
      'productName',
      'productSKU',
      'productUrl',
      'productImageUrl',
      'productImageLargeUrl',
      'productImageThumbnailUrl',
      'productDescription',
      'avatar',
      'location',
      'reviewVotesUp',
      'reviewVotesDown',
      'userReference',
      'dateCreated',
      'dateReplied',
      'reviewType',
      'widgetType',
      'reviewOptionsList',
    ]),
  );

  return {
    data: result,
    hasNextPage: reviewData.data.length !== 0,
    paginationMeta: {after: reviewData.page},
  };
};

export const TransformCreateReviews = (reviewData: IStampedWriteReviewsResponse) => {
  const result = _.pick(reviewData, ['id']) as TApptileWriteReviewsResponse;

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};

export const TransformCreateProductReviewBadge = (reviewData: Array<IStampedCreateProductReviewBadgeResponse>) => {
  const result =
    reviewData &&
    reviewData.length !== 0 &&
    (reviewData.map(o =>
      _.pick(o, ['productId', 'rating', 'count', 'countQuestions', 'c', 'badge', 'badgeqna']),
    ) as TApptileCreateProductReviewBadgeResponse);

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};
