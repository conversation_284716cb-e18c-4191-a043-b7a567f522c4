import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IOrderLimiterCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import advanceOrderLimiterActions from './actions/AdvanceOrderLimiterCheckoutAction';

export type AdvanceOrderLimiterPluginConfigType = DatasourcePluginConfig &
  IOrderLimiterCredentials & {increaseCartLineItemQuantity: string};

export const advanceOrderLimiterApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  increaseCartLineItemQuantity: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return advanceOrderLimiterActions.increaseCartLineItemQuantity;
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        sellingPlanId: '',
        itemPrice: '',
        successToastText: 'Item added successfully!',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'advanceOrderLimiter',
  type: 'datasource',
  name: 'Advance Order Limiter',
  description: 'AdvanceOrderLimiter integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const advanceOrderLimiterEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'shopifyDatasourceId',
      defultValue: 'shopify',
      props: {
        label: 'Shopify Datasource ID',
        placeHolder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumOrderValue',
      props: {
        label: 'Maximum Order Value',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumOrderItems',
      props: {
        label: 'Maximum Order Items',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumItemQuantity',
      props: {
        label: 'Maximum Item Quantity',
      },
    },
    {
      type: 'codeInput',
      name: 'orderValueExceededErrorText',
      props: {
        label: 'Order Value Exceeded Error Text',
      },
    },
    {
      type: 'codeInput',
      name: 'orderItemsExceededErrorText',
      props: {
        label: 'Max Total Item Count Exceeded Error Text',
      },
    },
    {
      type: 'codeInput',
      name: 'singleItemCountExceededErrorText',
      props: {
        label: 'Max Single Count Exceeded Error Text',
      },
    },
    {
      type: 'codeInput',
      name: 'productItemCountExceededErrorText',
      props: {
        label: 'Max Product Item Count Exceeded Error Text',
      },
      hidden: model => model.get('strategy') !== 'metafield',
    },
    {
      type: 'codeInput',
      name: 'groupItemCountExceededErrorText',
      props: {
        label: 'Max Item Count Per Group Exceeded Error Text',
      },
      hidden: model => model.get('strategy') !== 'metafield',
    },
    {
      type: 'codeInput',
      name: 'productMetafieldLimiterMaxKey',
      props: {
        label: 'product metafield order limiter key name',
      },
      hidden: model => model.get('strategy') !== 'metafield',
    },
    {
      type: 'codeInput',
      name: 'collectionMetafieldLimiterMaxKey',
      props: {
        label: 'collection metafield order limiter key name',
      },
      hidden: model => model.get('strategy') !== 'metafield',
    },
    {
      type: 'radioGroup',
      name: 'strategy',
      props: {
        label: 'limiter strategy',
        options: ['metafield', 'none'],
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'advanceOrderLimiter',
  config: {
    ...baseDatasourceConfig,
    shopifyDatasourceId: 'shopify',
    isExecuting: false,
    maximumOrderValue: '',
    maximumOrderItems: '',
    maximumItemQuantity: '',
    orderValueExceededErrorText: '',
    orderItemsExceededErrorText: '',
    singleItemCountExceededErrorText: '',
    productItemCountExceededErrorText: '',
    groupItemCountExceededErrorText: '',
    productMetafieldLimiterMaxKey: '',
    collectionMetafieldLimiterMaxKey: '',
    strategy: 'metafield',
    increaseCartLineItemQuantity: 'action',
  } as AdvanceOrderLimiterPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<AdvanceOrderLimiterPluginConfigType>,
    dsModelValues: any,
  ) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return advanceOrderLimiterApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      advanceOrderLimiterApiRecords && advanceOrderLimiterApiRecords[queryName]
        ? advanceOrderLimiterApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IOrderLimiterCredentials,
  ): Partial<AdvanceOrderLimiterPluginConfigType> | boolean {
    const {maximumOrderValue, maximumOrderItems, maximumItemQuantity} = credentials;
    return {
      maximumOrderValue,
      maximumOrderItems,
      maximumItemQuantity,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['maximumOrderValue', 'maximumOrderItems', 'maximumItemQuantity'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'advanceOrderLimiter';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: advanceOrderLimiterEditors,
});
