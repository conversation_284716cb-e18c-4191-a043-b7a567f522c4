import {modelUpdateAction} from 'apptile-core';
import {navigateToScreen} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {AppConfig, AppModelType} from 'apptile-core';
import _ from 'lodash';
import {executeQuery} from '..';
import {ModelChange, Selector} from 'apptile-core';
import { LocalStorage } from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';

export interface IAuthActionsDatasourcePluginConfigType {
  signInUser: string;
  signOutUser: string;
  // verifyAuthSession: string;
  loggedInUserAccessToken: any;
  authLoader: boolean;
  updateStoreStatus: string;
}

export const authActionsDatasourcePluginConfig: IAuthActionsDatasourcePluginConfigType = {
  signInUser: TriggerActionIdentifier,
  signOutUser: TriggerActionIdentifier,
  // verifyAuthSession: TriggerActionIdentifier,
  loggedInUserAccessToken: '',
  authLoader: false,
  updateStoreStatus: TriggerActionIdentifier,
};

export const authActionDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'screenSelector',
      name: 'signInRedirectScreenId',
      props: {
        label: 'Post signIn Redirect Screen',
      },
    },
    {
      type: 'screenSelector',
      name: 'signOutRedirectScreenId',
      props: {
        label: 'Post signOut Redirect Screen',
      },
    },
  ],
};

export const authActionsDatasourcePropertySettings: PluginPropertySettings = {
  signInUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signInUser;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        password: '',
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: false,
      },
    },
  },
  signOutUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signOutUser;
    },
    actionMetadata: {
      editableInputParams: {
        skipPostSignOutRedirect: false,
      },
    },
  },
  loggedInUserAccessToken: {
    getValue(model, renderedValue, selector) {
      return renderedValue;
    },
  },
  updateStoreStatus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.updateStoreStatus;
    },
    actionMetadata: {
      editableInputParams: {
        storeStatus: '',
        collectionId: '',
      },
    },
  },
};

export interface ISignInUserPayload {
  email: string;
  password: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
  skipPostAuthRedirect: boolean;
}

export interface IAuthActionsInterface {
  signInUser: ActionHandler;
}

export interface IRenewCustomerAccessTokenPayload {
  customerAccessToken: string;
}

export interface ISignInUserWithCustomerAccessToken {
  customerAccessToken: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
  skipPostAuthRedirect: boolean;
}

class AuthActions implements IAuthActionsInterface {
  // private async queryExecutor(model: any, querySchema: ShopifyQueryDetails, inputVariables: any) {
  //   const queryRunner = model.get('queryRunner');
  //   const shopConfig = model.get('shop');
  //   const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;

  //   const response = await queryRunner.runQuery(querySchema.queryType, querySchema.gqlTag, input, {});
  //   const {transformedData, transformedError} = processShopifyGraphqlQueryResponse(response, querySchema, shopConfig);
  //   return {transformedData, transformedError};
  // }

  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];
    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }

  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('snackShackPartner');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  private async updateLocalStore(key: string, value: any) {
    await LocalStorage.setValue(key, value);
  }

  private async storeLoggedInUserDetails(value: any) {
    await this.updateLocalStore(`loggedInUser`, value);
  }

  private async storeloggedInUserAccessToken(value: any) {
    await this.updateLocalStore(`loggedInUserAccessToken`, value);
  }

  private async setErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });

    setTimeout(() => dispatch(modelUpdateAction(newModelUpdates, undefined, true)), 1);
  }

  private async setBadParamErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private async setSuccessMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    customerData: any,
    message: string,
  ) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    toast.show(message, {
      type: 'success',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private preSignInUser = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as ISignInUserPayload;
    const {email, password} = payload;
    let customerData = null;

    const {data: signInResponse, errors: transformedError} = await this.queryExecutor(model, config, 'login', {
      email,
      password,
    });

    if (!signInResponse || !signInResponse?.accessToken) {
      console.log(`Error signInResponse`, signInResponse);
      return {customerData, error: _.first(transformedError)?.message};
    }

    await this.storeloggedInUserAccessToken(signInResponse);

    customerData = {
      loggedInUserAccessToken: signInResponse,
    };
    return {customerData, error: null};
  };

  signInUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    const payload = params as ISignInUserPayload;
    const {successMessage, skipPostAuthRedirect, errorMessage, badParameterMessage} = payload;
    try {
      const signInRedirectScreenId = model.get('signInRedirectScreenId');
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const {customerData, error} = await this.preSignInUser(dispatch, config, model, selector, params);

      if (error) {
        console.log(`error`, error);
        this.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          skipPostAuthRedirect,
          error,
          badParameterMessage,
        );
        return;
      }

      if (!signInRedirectScreenId) {
        console.log(`Please configure signIn screen in shopify`);
      }

      this.setSuccessMessage(dispatch, config, model, selector, skipPostAuthRedirect, customerData, successMessage);
      if (!skipPostAuthRedirect) {
        dispatch(navigateToScreen(signInRedirectScreenId, {}));
      }
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
    }
  };

  signOutUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const payload = params;
    const {skipPostSignOutRedirect} = payload;
    const signOutRedirectScreenId = model.get('signOutRedirectScreenId');

    try {
      const resetloggedInUserValue = null;
      await this.storeloggedInUserAccessToken(resetloggedInUserValue);
      await this.storeLoggedInUserDetails(resetloggedInUserValue);

      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['loggedInUser']),
        newValue: resetloggedInUserValue,
      });

      modelUpdates.push({
        selector: selector.concat(['loggedInUserAccessToken']),
        newValue: resetloggedInUserValue,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));

      if (!signOutRedirectScreenId) {
        console.log(`Please configure signOut screen in shopify`);
      }

      if (!skipPostSignOutRedirect) {
        dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      }
    } catch (error) {
      console.log(`error while sign out`);
      if (!skipPostSignOutRedirect) {
        dispatch(navigateToScreen(signOutRedirectScreenId, {}));
      }
    }
  };

  /**
   * Verify existing auth session validity and logout if it is expired or session disabled from admin
   */
  // verifyAuthSession = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
  //   const payload = params;
  //   const {skipPostSignOutRedirect} = payload;
  //   const signOutRedirectScreenId = model.get('signOutRedirectScreenId');
  //   const loggedInUserAccessToken = model.get('loggedInUserAccessToken');
  //   let newModelUpdates: ModelChange[] = [];

  //   try {
  //     const queries = this.getQueries();

  //     const {transformedData: fetchCustomerResponse} = await this.queryExecutor(model, config, queries?.FetchCustomer, {
  //       customerAccessToken: loggedInUserAccessToken?.accessToken,
  //     });

  //     if (!fetchCustomerResponse?.id) {
  //       console.log(`Error fetchCustomerResponse`, fetchCustomerResponse);
  //       newModelUpdates.push({
  //         selector: selector.concat(['loggedInUser']),
  //         newValue: null,
  //       });

  //       newModelUpdates.push({
  //         selector: selector.concat(['loggedInUserAccessToken']),
  //         newValue: null,
  //       });
  //       dispatch(modelUpdateAction(newModelUpdates, undefined, true));

  //       if (!signOutRedirectScreenId) {
  //         console.log(`Please configure signOut screen in shopify`);
  //       }

  //       if (!skipPostSignOutRedirect) {
  //         dispatch(navigateToScreen(signOutRedirectScreenId, {}));
  //       }
  //       return;
  //     }

  //     await this.storeLoggedInUserDetails(fetchCustomerResponse);
  //     newModelUpdates.push({
  //       selector: selector.concat(['loggedInUser']),
  //       newValue: fetchCustomerResponse,
  //     });
  //     dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  //   } catch (error) {
  //     console.log(`error in verifyAuthSession`);
  //   }
  // };

  updateStoreStatus = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const payload = params;
    const {storeStatus, collectionId} = payload;

    const response = await this.queryExecutor(model, config, 'updateStoreStatus', {
      storeStatus,
      collectionId
    })

    const updatedStoreStatus = response?.data?.updatedStoreStatus
    dispatch(modelUpdateAction([{
      selector: selector.concat(['storeStatus']),
      newValue: updatedStoreStatus,
    }], undefined, true))

    return toast.show('Store Status Updated', {
      type: 'success',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
  };
}

const authActions = new AuthActions();
export default authActions;
