import {Editors} from 'apptile-core';
import { LocalStorage as localStorage } from 'apptile-core';
import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  IAuthActionsDatasourcePluginConfigType,
  authActionDatasourceEditor,
  authActionsDatasourcePluginConfig,
  authActionsDatasourcePropertySettings,
} from './actions/authActions';
import {TransformSnackShackOrders} from './transformers';

// import {transformGetProductData, transformListReviewsData} from './transformers';

export type SnackShackPartnerConfigType = DatasourcePluginConfig &
  IAuthActionsDatasourcePluginConfigType & {
    queryRunner: any;
  };

type SnackShackPartnerQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const SnackShackPartnerApiRecords: Record<string, SnackShackPartnerQueryDetails> = {
  fetchOrders: {
    queryType: 'get',
    endpoint: '/api/orders',
    //TODO : Enable Pagination
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {after} = paginationMeta ?? {};
      const offsetString = after ? `&offset=${after}` : '';
      return `${endpoint}?limit=${inputParams?.perPage ?? 10}${offsetString}`;
    },
    editableInputParams: {
      perPage: '',
    },
    transformer: TransformSnackShackOrders,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
    isPaginated: true,
  },
  fetchFilteredOrders: {
    queryType: 'get',
    endpoint: '/api/orders',
    //TODO : Enable Pagination
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {status} = inputParams;
      const {after} = paginationMeta ?? {};
      const offsetString = after ? `&offset=${after}` : '';
      const ordersStatus = status ? `&status=${status}` : '';
      return `${endpoint}?limit=${inputParams?.perPage ?? 10}${offsetString}${ordersStatus}`;
    },
    editableInputParams: {
      perPage: '',
      status: '',
    },
    transformer: TransformSnackShackOrders,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
    isPaginated: true,
  },
  updateOrder: {
    queryType: 'post',
    endpoint: '/api',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}/orders/${inputParams.orderId}/updateStatus`;
    },
    inputResolver: inputParams => {
      return {status: inputParams.status};
    },
    editableInputParams: {
      orderId: '',
      status: '',
    },
    isPaginated: false,
  },
  login: {
    queryType: 'post',
    endpoint: '/api/users/login',
    inputResolver: inputParams => {
      return {email: inputParams.email, password: inputParams.password};
    },
    editableInputParams: {
      email: '',
      password: '',
    },
    isPaginated: false,
  },
  changePassword: {
    queryType: 'post',
    endpoint: '/api/users/change-password',
    inputResolver: inputParams => {
      return {password: inputParams.password, confirmPassword: inputParams.confirmPassword};
    },
    editableInputParams: {
      password: '',
      confirmPassword: '',
    },
  },
  resetPasswordGetOTP: {
    queryType: 'post',
    endpoint: '/api/users/reset-password/get-otp',
    inputResolver: inputParams => {
      return {phone: inputParams.phone};
    },
    editableInputParams: {
      phone: '',
    },
  },
  resetPasswordVerifyOTP: {
    queryType: 'post',
    endpoint: '/api/users/reset-password/verify-otp',
    inputResolver: inputParams => {
      return {sessionId: inputParams.sessionId, code: inputParams.code};
    },
    editableInputParams: {
      sessionId: '',
      code: '',
    },
  },
  resetPasswordChangePassword: {
    queryType: 'post',
    endpoint: '/api/users/reset-password/change-password',
    inputResolver: inputParams => {
      return {
        sessionId: inputParams.sessionId,
        password: inputParams.password,
        confirmPassword: inputParams.confirmPassword,
      };
    },
    editableInputParams: {
      sessionId: '',
      password: '',
      confirmPassword: '',
    },
  },
  updateStoreStatus: {
    queryType: 'post',
    endpoint: '/api/shopify/update-store-status',
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      return endpoint;
    },
    inputResolver: inputParams => {
      return {storeStatus: inputParams.storeStatus, collectionId: inputParams.collectionId};
    },
    editableInputParams: {
      storeStatus: '',
      collectionId: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  ...authActionsDatasourcePropertySettings,
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'snackShackPartner',
  type: 'datasource',
  name: 'SnackShack Partner',
  description: 'To process orders on SnackShack Partner stands',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const SnackShackPartnerEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Url',
        placeholder: '',
      },
    },
    ...(authActionDatasourceEditor.basic as Editors<any>),
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<SnackShackPartnerConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, SnackShackPartnerConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const loggedInUserAccessToken = dsModelValues?.get('loggedInUserAccessToken') ?? null;

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables, ...contextInputParam},
      {
        headers: {
          Authorization: loggedInUserAccessToken?.accessToken ? `Bearer ${loggedInUserAccessToken?.accessToken}` : '',
        },
      },
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'snackShackPartner',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'http://localhost:3000',
    queryRunner: 'queryrunner',
    ...authActionsDatasourcePluginConfig,
  } as SnackShackPartnerConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<SnackShackPartnerConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    const loggedInUserAccessToken = yield call(localStorage.getValue, 'loggedInUserAccessToken');

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'loggedInUserAccessToken'],
          newValue: loggedInUserAccessToken ?? null,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return SnackShackPartnerApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      SnackShackPartnerApiRecords && SnackShackPartnerApiRecords[queryName]
        ? SnackShackPartnerApiRecords[queryName]
        : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'snackShackPartner';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = SnackShackPartnerApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: SnackShackPartnerEditors,
});
