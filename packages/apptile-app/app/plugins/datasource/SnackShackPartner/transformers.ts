import _ from 'lodash';
import {TransformerFunction} from '../RestApi';
import {formatQueryReturn} from '../ShopifyV_22_10/utils/utils';

export const TransformSnackShackOrders: TransformerFunction = (response: any) => {
  const orders = _.get(response, 'orders', {});
  const {hasMore: hasNextPage, cursor: after} = response ?? {};
  return formatQueryReturn(orders, response, {after}, hasNextPage);
};
