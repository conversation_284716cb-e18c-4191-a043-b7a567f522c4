import {select} from 'redux-saga/effects';
import {selectAppConfig} from 'apptile-core';
import {GetRegisteredPlugin} from 'apptile-core';

export function generateRandomPassword(length: number) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-+=<>?';
  let password = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  return password;
}

export function* getShopifyDS() {
  const state = yield select();
  const datasource = 'shopify';
  var pageModels = state.stageModel.getModelValue([]);
  const shopifyModelValues = pageModels.get(datasource);
  const ApptileAppConfig = yield select(selectAppConfig);
  const shopifyConfig = ApptileAppConfig.getPlugin('shopify');
  const shopifyPluginType = shopifyModelValues.get('pluginType');

  const shopifyModel = GetRegisteredPlugin(shopifyPluginType);

  return {shopifyConfig, shopifyModelValues, shopifyModel};
}
