import {call} from 'redux-saga/effects';
import {modelUpdateAction} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';

import _ from 'lodash';
import {generateRandomPassword, getShopifyDS} from '../utils';
import {executeQuery} from '../index';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {
  SignUpUserPayload,
  VerifySignUpPayload,
  VerifySignInPayload,
  ShopifySignInPayload,
  SingUpStepPayload,
  SingInStepPayload,
  IAuthActionsInterface,
  IAuthActionsDatasourcePluginConfigType,
} from '../types';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';

export const authActionsDatasourcePluginConfig: IAuthActionsDatasourcePluginConfigType = {
  signUpUser: TriggerActionIdentifier,
  verifySignUp: TriggerActionIdentifier,
  signInUser: TriggerActionIdentifier,
  verifySignIn: TriggerActionIdentifier,
  setSignInStep: TriggerActionIdentifier,
  setUpProfile: TriggerActionIdentifier,
  setSignUpStep: TriggerActionIdentifier,
  signInWithShopify: TriggerActionIdentifier,
  signUpWithShopify: TriggerActionIdentifier,
  resetCustomerData: TriggerActionIdentifier,
};

export const authActionDatasourceEditor: PluginEditorsConfig<any> = {};

export const authActionsDatasourcePropertySettings: PluginPropertySettings = {
  // Action to send OTP to Jolene Auth Endpoint For SignUp

  signUpUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signUpUser;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        mobile: '',
        firstname: '',
        lastname: '',
        password: '',
      },
    },
  },

  // Action to verify through OTP For SignUp

  verifySignUp: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.verifySignUp;
    },
    actionMetadata: {
      editableInputParams: {
        otp: '',
        successMessage: '',
      },
    },
  },

  // Action to setup profile(Which includes setting up user profile with details like dob,gender)

  setUpProfile: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.setUpProfile;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        mobile: '',
        countryCode: '',
        firstname: '',
        lastname: '',
        dob: '',
        gender: '',
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
      },
    },
  },

  // Action to send OTP to Jolene Auth Endpoint For SignIn

  signInUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signInUser;
    },
    actionMetadata: {
      editableInputParams: {
        mobile: '',
      },
    },
  },

  // Action to verify through OTP For SignIn

  verifySignIn: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.verifySignIn;
    },
    actionMetadata: {
      editableInputParams: {
        mobile: '',
        otp: '',
        successMessage: '',
        errorMessage: '',
        skipPostAuthRedirect: '{{true}}',
        badParameterMessage: '',
      },
    },
  },

  /**** Action to set model state for signIn Page Step,
  which is used to decide which step to be shown on screen ****/

  setSignInStep: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.setSignInStep;
    },
    actionMetadata: {
      editableInputParams: {
        signInPageStep: '',
      },
    },
  },
  /**** Action to set model state for signUp Page Step,
  which is used to decide which step to be shown on screen ****/

  setSignUpStep: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.setSignUpStep;
    },
    actionMetadata: {
      editableInputParams: {
        signUpPageStep: '',
      },
    },
  },

  //Action to Login User through Shopify DataSource

  signInWithShopify: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return AuthActions.signInWithShopify;
    },
    actionMetadata: {
      editableInputParams: {
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
      },
    },
  },
  //Action to SignUp  User through Shopify DataSource

  signUpWithShopify: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.signUpWithShopify;
    },
    actionMetadata: {
      editableInputParams: {
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
      },
    },
  },
  resetCustomerData: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.resetCustomerData;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
};

export interface SignInUserPayload {
  mobile: string;
}

class AuthActions implements IAuthActionsInterface {
  private static async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = AuthActions.getQueries();
    const queryDetails = queries[queryName];

    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }

  private static getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('Jolene Otp Login');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  static async setSuccessMessage(dispatch: any, config: PluginConfig, model: any, selector: Selector, message: string) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(message, {
      type: 'success',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  setBadParamErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    customerData: any,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });
    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  signInUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const payload = params as SignInUserPayload;

    const {mobile} = payload;
    const countryCode = model?.get('countryCode');

    const setAuthLoading = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(setAuthLoading, undefined, true));

    const {data: signInResponse, errors} = await AuthActions.queryExecutor(model, config, 'signIn', {
      mobile,
      countryCode,
    });

    if (errors) {
      const errorMessage = Array.isArray(errors) ? errors[0] : errors;
      console.error(errorMessage, 'Error While Verifying OTP For Jolene');
      const resetCustomerData = {mobile};
      AuthActions.setBadParamErrorMessage(
        dispatch,
        config,
        model,
        selector,
        true,
        errorMessage,
        resetCustomerData,
        undefined,
      );
      return;
    }

    const customerDetailModelUpdates = [
      {
        selector: selector.concat(['mobile']),
        newValue: mobile,
      },
      {
        selector: selector.concat(['signInPageStep']),
        newValue: 'VERIFY_SIGN_IN',
      },
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },
    ];
    dispatch(modelUpdateAction(customerDetailModelUpdates, undefined, true));
  };

  signUpUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      const payload = params as SignUpUserPayload;
      const {email, mobile, firstname, lastname, password} = payload;
      const countryCode = model?.get('countryCode');
      // const password = generateRandomPassword(8); //generate random 8 char password
      const setAuthLoading = [
        {
          selector: selector.concat(['authLoader']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(setAuthLoading, undefined, true));

      const {data: signUpResponse, errors} = await AuthActions.queryExecutor(model, config, 'signUp', {
        email,
        mobile,
        countryCode,
        password,
        areUpdatesOpted: true,
      });

      if (errors) {
        const errorMessage = Array.isArray(errors) ? errors[0] : errors;
        console.error(errorMessage, 'Error While SignUp User For Jolene');
        const CustomerData = {email, mobile, firstname, lastname,password};
        AuthActions.setBadParamErrorMessage(
          dispatch,
          config,
          model,
          selector,
          true,
          errorMessage,
          CustomerData,
          undefined,
        );
        return;
      }

      const customerDetailModelUpdates = [
        {
          selector: selector.concat(['email']),
          newValue: email,
        },
        {
          selector: selector.concat(['password']),
          newValue: password,
        },
        {
          selector: selector.concat(['mobile']),
          newValue: mobile,
        },
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
        {
          selector: selector.concat(['firstname']),
          newValue: firstname,
        },
        {
          selector: selector.concat(['lastname']),
          newValue: lastname,
        },
        {
          selector: selector.concat(['signUpPageStep']),
          newValue: 'VERIFY_SIGNUP',
        },
      ];

      dispatch(modelUpdateAction(customerDetailModelUpdates, undefined, true));
    } catch (error) {
      console.log(`Error signUpResponse`, error);
    }
  };
  verifySignUp = function* (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) {
    const email = model?.get('email');
    const mobile = model?.get('mobile');
    const countryCode = model?.get('countryCode');

    const {otp, successMessage} = params as VerifySignUpPayload;
    try {
      const setAuthLoading = [
        {
          selector: selector.concat(['authLoader']),
          newValue: true,
        },
      ];

      dispatch(modelUpdateAction(setAuthLoading, undefined, true));

      const {data: response, errors} = yield call(AuthActions.queryExecutor, model, config, 'verifySignUp', {
        email,
        mobile,
        otp,
        countryCode,
      });

      if (errors) {
        const errorMessage = Array.isArray(errors) ? errors[0] : errors;
        console.error(errorMessage, 'Error While SignUp User For Jolene');
        if (errorMessage == 'User already exists on shopify') {
          const modelUpdates = [
            {
              selector: selector.concat(['signUpPageStep']),
              newValue: 'SIGN_UP',
            },
          ];
          dispatch(modelUpdateAction(modelUpdates, undefined, true));
        }

        AuthActions.setBadParamErrorMessage(dispatch, config, model, selector, true, errorMessage, {}, undefined);
        return;
      }

      const modelUpdates = [
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
        {
          selector: selector.concat(['signUpPageStep']),
          newValue: 'PERSONAL_DETAILS',
        },
      ];
      dispatch(modelUpdateAction(modelUpdates, undefined, true));

      AuthActions.setSuccessMessage(dispatch, config, model, selector, successMessage);
    } catch (error) {
      console.log(`Error while Verifying SignUp for Jolene`, error);
      AuthActions.setBadParamErrorMessage(dispatch, config, model, selector, true, error, {}, undefined);
    }
  };

  setUpProfile = function* (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) {
    const {
      email,
      mobile,
      countryCode,
      firstname,
      lastname,
      dob,
      gender,
      successMessage,
      badParameterMessage,
      errorMessage,
    } = params;
    const setAuthLoading = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(setAuthLoading, undefined, true));

    const {data: response, errors} = yield call(AuthActions.queryExecutor, model, config, 'setUpProfile', {
      mobile,
      countryCode,
      firstname,
      lastname,
      dob,
      gender,
      email,
    });

    if (errors) {
      const errorMessage = Array.isArray(errors) ? errors[0] : errors;
      console.error(errorMessage, 'Error While Setting Up Profile for Jolene');

      AuthActions.setBadParamErrorMessage(dispatch, config, model, selector, true, errorMessage, {}, undefined);
      return;
    }

    const {email: userEmail, password: userPassword} = response;

    const modelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },

      {
        selector: selector.concat(['email']),
        newValue: userEmail,
      },
      {
        selector: selector.concat(['password']),
        newValue: userPassword,
      },
    ];
    dispatch(modelUpdateAction(modelUpdates, undefined, true));

    yield AuthActions.signInWithShopify(
      dispatch,
      config,
      model,
      selector,
      {email: userEmail, password: userPassword, successMessage, badParameterMessage, errorMessage},
      undefined,
      undefined,
    );
  };

  static signInWithShopify = function* (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) {
    const payload = params as ShopifySignInPayload;
    const {email, password, successMessage, badParameterMessage, errorMessage} = payload;
    try {
      const {shopifyModelValues, shopifyConfig} = yield getShopifyDS();
      const signInUserAction = shopifyModelValues.get('signInUser');
      const {errorMessage: error} = yield call(
        signInUserAction,
        dispatch,
        shopifyConfig,
        shopifyModelValues,
        ['shopify'],
        {email, password, successMessage, badParameterMessage, errorMessage},
        true,
      );

      if (error) {
        console.error(error);
        return;
      }

      const modelUpdate = [
        {
          selector: selector.concat(['signUpPageStep']),
          newValue: 'SIGN_UP',
        },
        {
          selector: selector.concat(['email']),
          newValue: '',
        },
        {
          selector: selector.concat(['firstname']),
          newValue: '',
        },
        {
          selector: selector.concat(['lastname']),
          newValue: '',
        },
        {
          selector: selector.concat(['password']),
          newValue: '',
        },
        {
          selector: selector.concat(['mobile']),
          newValue: '',
        },
      ];
      dispatch(modelUpdateAction(modelUpdate, undefined, true));
    } catch (error) {
      console.log(`Error while Verifying SignUp for Jolene`, error);
      return AuthActions.setBadParamErrorMessage(dispatch, config, model, selector, false, error, {}, undefined);
    }
  };

  signUpWithShopify = function* (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) {
    const payload = params as ShopifySignInPayload;
    const email = model?.get('email');
    const password = model?.get('password');
    const mobile = model?.get('mobile');
    const countryCode = model?.get('countryCode');
    const phone = `+${countryCode}${mobile}`;

    try {
      const {shopifyModelValues, shopifyConfig} = yield getShopifyDS();
      const signUpUserAction = shopifyModelValues.get('signUpUser');
      const {signUpSuccess, error} = yield call(
        signUpUserAction,
        dispatch,
        shopifyConfig,
        shopifyModelValues,
        ['shopify'],
        {email, password, phone, ...payload},
        true,
      );

      if (error) {
        console.error(error);
      }

      const modelUpdate = [
        {
          selector: selector.concat(['signUpPageStep']),
          newValue: 'SIGN_UP',
        },
      ];
      dispatch(modelUpdateAction(modelUpdate, undefined, true));
    } catch (error) {
      console.log(`Error while Verifying SignUp for Jolene`, error);
    }
  };

  setSignUpStep = (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      const {signUpPageStep} = params as SingUpStepPayload;

      const stepModelUpdate = [
        {
          selector: selector.concat(['signUpPageStep']),
          newValue: signUpPageStep,
        },
      ];
      dispatch(modelUpdateAction(stepModelUpdate, undefined, true));
    } catch (error) {
      console.log(error);
    }
  };

  setSignInStep = (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      const {signInPageStep} = params as SingInStepPayload;

      const stepModelUpdate = [
        {
          selector: selector.concat(['signInPageStep']),
          newValue: signInPageStep,
        },
      ];

      dispatch(modelUpdateAction(stepModelUpdate, undefined, true));
    } catch (error) {
      console.log(error);
    }
  };
  resetCustomerData = (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      const resetCustomerDataModelUpdate = [
        {
          selector: selector.concat(['email']),
          newValue: '',
        },
        {
          selector: selector.concat(['mobile']),
          newValue: '',
        },
        {
          selector: selector.concat(['firstname']),
          newValue: '',
        },
        {
          selector: selector.concat(['lastname']),
          newValue: '',
        },
      ];

      dispatch(modelUpdateAction(resetCustomerDataModelUpdate, undefined, true));
    } catch (error) {
      console.log(error);
    }
  };
  static setBadParamErrorMessage = function (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    errorMessage: string,
    customerData: any,
    customErrorMessage: string | undefined,
  ) {
    const errorString = customErrorMessage ?? errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  };
  verifySignIn = function* (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) {
    try {
      const {mobile, otp, successMessage, errorMessage, skipPostAuthRedirect, badParameterMessage} =
        params as VerifySignInPayload;
      const countryCode = model?.get('countryCode');
      const setAuthLoading = [
        {
          selector: selector.concat(['authLoader']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(setAuthLoading, undefined, true));

      const {data: verifySignInResponse, errors} = yield call(
        AuthActions.queryExecutor,
        model,
        config,
        'verifySignIn',
        {
          mobile,
          countryCode,
          otp,
        },
      );

      if (errors) {
        const errorMessage = Array.isArray(errors) ? errors[0] : errors;
        console.error(errorMessage, 'Error While Verifying OTP For Jolene');

        AuthActions.setBadParamErrorMessage(dispatch, config, model, selector, true, errorMessage, {}, undefined);
        return;
      }

      const {email, password} = verifySignInResponse;
      const {shopifyModelValues, shopifyConfig} = yield getShopifyDS();
      const loginUser = shopifyModelValues.get('signInUser');
      const {success, error: shopifySingInError} = yield call(
        loginUser,
        dispatch,
        shopifyConfig,
        shopifyModelValues,
        ['shopify'],
        {
          email,
          password,
          successMessage,
          errorMessage,
          skipPostAuthRedirect,
          badParameterMessage,
        },
      );

      if (shopifySingInError) {
        console.error(shopifySingInError);
        return;
      }

      const modelUpdates = [
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
        {
          selector: selector.concat(['mobile']),
          newValue: '',
        },
        {
          selector: selector.concat(['signInPageStep']),
          newValue: 'SIGN_IN',
        },
      ];
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      // this.setErrorMessage(dispatch, config, model, selector, skipPostAuthRedirect, error?.message, errorMessage);
    }
  };
}

const authActions = new AuthActions();
export default authActions;
