import {modelUpdateAction} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';

import _ from 'lodash';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {IClickpostActionsDatasourcePluginConfigType, IClickpostActionsInterface} from '../types';
import {PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import AjaxQueryRunner from '../../AjaxWrapper/model';
import {navigateToScreen} from 'apptile-core';

export const clickpostActionsDatasourcePluginConfig: IClickpostActionsDatasourcePluginConfigType = {
  getSecurityKey: TriggerActionIdentifier,
};

export const clickpostActionDatasourceEditor: PluginEditorsConfig<any> = {};

export const clickpostActionsDatasourcePropertySettings: PluginPropertySettings = {
  // Action to get security key for order
  getSecurityKey: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return clickpostActions.getSecurityKey;
    },
    actionMetadata: {
      editableInputParams: {waybill: ''},
    },
  },
};

class ClickpostActions implements IClickpostActionsInterface {
  static async setErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    errorMessage: string,
    customErrorMessage: string | undefined,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
  }

  getSecurityKey = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    try {
      const {waybill} = params;

      if (!waybill) {
        ClickpostActions.setErrorMessage(dispatch, config, model, selector, "Waybill doesn't exist", undefined);
        return;
      }

      const setReturnLoadingTrue = [
        {
          selector: selector.concat(['returnLoader']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(setReturnLoadingTrue, undefined, true));

      const apptileServerUrl = model?.get('apptileServerUrl');
      const clickpostJoleneUrl = model?.get('clickpostJoleneUrl');
      const appId = model?.get('appId');

      const clickpostQueryRunner = AjaxQueryRunner();
      clickpostQueryRunner.initClient(clickpostJoleneUrl, config => {
        return config;
      });
      const clickpostQueryResponse = await clickpostQueryRunner.runQuery(
        'get',
        `/api/v1/track-order?waybill=${waybill}`,
        {},
        {},
      );

      const apptileQueryRunner = AjaxQueryRunner();
      apptileQueryRunner.initClient(apptileServerUrl, config => {
        return config;
      });
      const apptileQueryResponse = await apptileQueryRunner.runQuery(
        'get',
        `/apptile-clickpost/api/clickpost/get-security-key?appId=${appId}&waybill=${waybill}&cp_id=${clickpostQueryResponse.data.result.cp_id}`,
        {},
        {},
      );

      const newModelUpdates = [
        {
          selector: selector.concat(['securityKey']),
          newValue: apptileQueryResponse.data.securityKey,
        },
      ];

      const returnWebviewScreen = model?.get('returnWebviewScreenId');

      dispatch(modelUpdateAction(newModelUpdates, undefined, true));
      const setReturnLoadingFalse = [
        {
          selector: selector.concat(['returnLoader']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(setReturnLoadingFalse, undefined, true));
      dispatch(
        navigateToScreen(returnWebviewScreen, {
          returnUrl: `${clickpostJoleneUrl}/returns?waybill=${waybill}&security_key=${apptileQueryResponse.data.securityKey}`,
        }),
      );
    } catch (error: any) {
      console.error(error);

      const setReturnLoadingFalse = [
        {
          selector: selector.concat(['returnLoader']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(setReturnLoadingFalse, undefined, true));
      ClickpostActions.setErrorMessage(dispatch, config, model, selector, error.message, 'Something Went Wrong');
      return;
    }
  };
}

const clickpostActions = new ClickpostActions();
export default clickpostActions;
