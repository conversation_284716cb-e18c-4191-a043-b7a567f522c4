import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {authActionsDatasourcePluginConfig, authActionsDatasourcePropertySettings} from './actions/authactions';
import {clickpostActionsDatasourcePluginConfig, clickpostActionsDatasourcePropertySettings} from './actions/clickpostActions';

import {IAuthActionsDatasourcePluginConfigType, IClickpostActionsDatasourcePluginConfigType, IClickpostActionsInterface} from './types';

export type JoleneAuthConfigType = DatasourcePluginConfig &
  IAuthActionsDatasourcePluginConfigType & IClickpostActionsDatasourcePluginConfigType & {
    queryRunner: any;
  };

type JoleneAuthQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const JoleneAuthApiRecords: Record<string, JoleneAuthQueryDetails> = {
  signUp: {
    queryType: 'post',
    endpoint: '/v1/sign-up/start',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      email: '',
      mobile: '',
      countryCode: '',
      password: '',
      areUpdatesOpted: '',
    },
  },

  verifySignUp: {
    queryType: 'post',
    endpoint: '/v1/sign-up/verify',

    endpointResolver: (endpoint: string, inputParams: any, paginationMeta: any) => {
      return endpoint;
    },
    editableInputParams: {
      email: '',
      mobile: '',
      otp: '',
      countryCode: '',
    },
  },
  signIn: {
    queryType: 'post',
    endpoint: '/v1/login/start',
    endpointResolver: (endpoint: string, inputParams: any) => {
      return endpoint;
    },

    editableInputParams: {
      mobile: '',
      countryCode: '',
    },
    isPaginated: false,
  },

  setUpProfile: {
    queryType: 'post',
    endpoint: '/v1/sign-up/setup-profile',

    editableInputParams: {
      email: '',
      mobile: '',
      countryCode: '',
      firstname: '',
      lastname: '',
      dob: '',
      gender: '',
    },
    isPaginated: false,
  },

  verifySignIn: {
    queryType: 'post',
    endpoint: '/v1/login/verify',
    inputResolver: (inputParams: any) => {
      return {email: inputParams.email, password: inputParams.password};
    },
    editableInputParams: {
      mobile: '',
      otp: '',
      countryCode: '',
    },
    isPaginated: false,
  },
};

const propertySettings: PluginPropertySettings = {
  ...authActionsDatasourcePropertySettings,
  ...clickpostActionsDatasourcePropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'JoleneAuth',
  type: 'datasource',
  name: 'Jolene OTP Login',
  description: 'Enable OTP Login for Jolene',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const JoleneAuthEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'countryCode',
      props: {
        label: 'Country Code',
        placeholder: '91',
      },
    },
    {
      type: 'codeInput',
      name: 'apptileServerUrl',
      props: {
        label: 'apptileServerUrl',
        placeholder: 'https://api.apptile.io',
      },
    },
    {
      type: 'codeInput',
      name: 'clickpostJoleneUrl',
      props: {
        label: 'clickpostJoleneUrl',
        placeholder: 'https://jolene.clickpost.in',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'appId',
        placeholder: 'App Id',
      },
    },
    {
      type: 'screenSelector',
      name: 'returnWebviewScreenId',
      props: {
        label: 'Return Webview Screen',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let endpoint = queryDetails.endpoint;

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, inputVariables);
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    return {rawData, data: transformedData, hasNextPage: false, paginationMeta: null};
  } catch (error) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: error?.response?.data?.message ?? ['Please Contact Support!'],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'Jolene Otp Login',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://qaqoqn2zyg.execute-api.ap-south-1.amazonaws.com/Dev',
    countryCode: '91',
    queryRunner: 'queryrunner',
    email: '',
    password: '',
    mobile: '',
    firstname: '',
    lastname: '',
    securityKey: '',
    otpSentCount: '0', // can be used in future to rate limit/ throttle the Resend OTP Request
    signInPageStep: 'SIGN_IN', //decides which step to show on the signup page
    signUpPageStep: 'SIGN_UP', //decides which step to show on the signup page
    authLoader: 'false', // shows loading state for user perception
    returnLoader: false, // shows loading state for fetching return url data
    ...authActionsDatasourcePluginConfig,
    ...clickpostActionsDatasourcePluginConfig,
  } as JoleneAuthConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<JoleneAuthConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config?.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return JoleneAuthApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      JoleneAuthApiRecords && JoleneAuthApiRecords[queryName] ? JoleneAuthApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'joleneAuth';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ) {
    const queryDetails = JoleneAuthApiRecords[queryName];
    if (!queryDetails) return;

    return {
      rawData: '',
      data: '',
      hasNextPage: false,
      paginationMeta: null,
      errors: [],
      hasError: false,
    };
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: JoleneAuthEditors,
});
