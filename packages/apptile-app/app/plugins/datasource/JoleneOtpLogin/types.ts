import {ActionHandler} from '../../triggerAction';
export interface SignUpUserPayload {
  email: string;
  password: string;
  mobile: string;
  firstname: string;
  lastname: string;
}

export interface VerifySignUpPayload {
  otp: string;
  successMessage: string;
}
export interface VerifySignInPayload {
  mobile: string;
  otp: string;
  successMessage: string;
  errorMessage: string;
  skipPostAuthRedirect: string;
  badParameterMessage: string;
}

export interface ShopifySignInPayload {
  email: string;
  password: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
}

export interface SingUpStepPayload {
  signUpPageStep: 'SING_UP' | 'VERIFY_SIGNUP' | 'PERSONAL_DETAILS';
}

export interface SingInStepPayload {
  signInPageStep: 'SING_IN' | 'VERIFY_SIGN_IN';
}

export interface IAuthActionsInterface {
  signUpUser: ActionHandler;
  signInUser: ActionHandler;
  verifySignUp: ActionHandler;
  verifySignIn: ActionHandler;
  setSignInStep: ActionHandler;
  setSignUpStep: ActionHandler;
  signInWithShopify: ActionHandler;
  setUpProfile: ActionHandler;
  resetCustomerData: ActionHandler;
}

export interface IClickpostActionsInterface {
  getSecurityKey: ActionHandler;
}

export interface IAuthActionsDatasourcePluginConfigType {
  signUpUser: string;
  verifySignUp: string;
  signInUser: string;
  verifySignIn: string;
  setSignInStep: string;
  setUpProfile: string;
  setSignUpStep: string;
  signInWithShopify: string;
  signUpWithShopify: string;
  resetCustomerData: string;
}

export interface IClickpostActionsDatasourcePluginConfigType {
  getSecurityKey: string;
}
