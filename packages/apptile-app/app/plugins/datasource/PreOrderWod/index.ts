import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import wrapDatasourceModel from '../wrapDatasourceModel';

export interface PreOrderWodPluginConfigType extends DatasourcePluginConfig {
  queryRunner: any;
}

type IEditableParams = Record<string, any>;

type PreOrderWodQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

const preOrderWodApiRecords: Record<string, PreOrderWodQueryDetails> = {};
const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'preOrderWod',
  type: 'datasource',
  name: 'Preorder Now WOD',
  description: 'Recharge Payments integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const preOrderWodEditors: PluginEditorsConfig<any> = {
  basic: [],
};

export default wrapDatasourceModel({
  name: 'preOrderWod',
  config: {
    ...baseDatasourceConfig,
  } as PreOrderWodPluginConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<PreOrderWodPluginConfigType>, dsModelValues: any) => {
    const queryRunner = AjaxQueryRunner();

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return preOrderWodApiRecords;
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'preOrderWod';
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: preOrderWodEditors,
});
