import {PluginConfig, PluginConfigType} from 'apptile-core';
import {GetRegisteredPluginInfo, PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
// import MeragiQueryDetails from '../index';
import {
  // makeInputParamsResolver, 
  queryRunner
} from '../utils';
import {isEmpty} from 'lodash';
import { LocalStorage }  from 'apptile-core';

export const MERAGI_ACCESS_TOKEN = 'MERAGI_ACCESS_TOKEN';

export interface ISignInUserPayload {
  phone: string;
}

export interface IVerifyOtpPayload {
  id: string;
  eventId: string;
  otp: string;
}

export interface IMeragiAuthActionsPluginConfig {
  loginUser: string;
  signOutUser: string;
  verifyOtp: string;
  user: any;
  accessToken: any;
  authLoader: boolean;
  isloggedInUser: string;
  signInRedirectScreenId: string;
  otpContext: any;
}

export const meragiAuthActionsPluginConfig: IMeragiAuthActionsPluginConfig = {
  loginUser: TriggerActionIdentifier,
  signOutUser: TriggerActionIdentifier,
  verifyOtp: TriggerActionIdentifier,
  user: '',
  accessToken: '',
  authLoader: false,
  isloggedInUser: '',
  signInRedirectScreenId: '',
  otpContext: '',
};

const getQueries = () => {
  const shopifyDatasouceConfig = GetRegisteredPluginInfo('meragi');
  const queries = shopifyDatasouceConfig?.plugin?.getQueries();
  return queries;
};

const updateLocalStore = async (key: string, value: any) => {
  await LocalStorage.setValue(key, value);
};

// const queryExecutor = async (
//   config: PluginConfigType<any>,
//   model: any,
//   querySchema: MeragiQueryDetails,
//   inputVariables: any,
// ) => {
//   const queryRunner = model?.get('queryRunner');
//   const input = querySchema.inputResolver ? querySchema.inputResolver(inputVariables) : inputVariables;
//   let {endpointResolver, endpoint, contextInputParams} = querySchema ?? {};
//   const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
//   const contextInputVariables = contextInputParamsResolver(config, model);
//   endpoint = endpointResolver && endpointResolver(endpoint, {...contextInputVariables, ...input});

//   const response = await queryRunner.runQuery(querySchema.queryType, endpoint, input);
//   return response;
// };

const loginUser = async (
  dispatch: any,
  config: PluginConfig,
  model: any,
  selector: Selector,
  params: any,
  appConfig: any,
  appModel: any,
) => {
  const loadingModelUpdates = [
    {
      selector: selector.concat(['authLoader']),
      newValue: true,
    },
  ];
  dispatch(modelUpdateAction(loadingModelUpdates));

  const payload = params as ISignInUserPayload;
  const {phone} = payload;
  let otpContext = null;
  try {
    const queries = getQueries();
    const loginResponse = await queryRunner(queries?.LoginUser, config, model, {
      phone,
    });
    if (loginResponse?.data && loginResponse?.data?.id) {
      otpContext = loginResponse.data;
    }
  } catch (error) {
    console.log(`Error signInResponse`, error);
    return {success: false, errorMessage: error};
  } finally {
    const loadedModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },
      {
        selector: selector.concat(['otpContext']),
        newValue: otpContext,
      },
    ];
    dispatch(modelUpdateAction(loadedModelUpdates));
  }
};

const verifyOtp = async (
  dispatch: any,
  config: PluginConfig,
  model: any,
  selector: Selector,
  params: any,
  appConfig: any,
  appModel: any,
) => {
  const otpContext = model?.get('otpContext');
  if (isEmpty(otpContext)) {
    console.error('verifyOtp called with no otpContext');
    return;
  }
  const {eventId, id} = otpContext;
  const loadingModelUpdates = [
    {
      selector: selector.concat(['authLoader']),
      newValue: true,
    },
  ];
  dispatch(modelUpdateAction(loadingModelUpdates));

  const payload = params as Partial<IVerifyOtpPayload>;
  const {otp} = payload;
  let accessToken = null,
    user = null;
  try {
    const queries = getQueries();
    const verifyOtpResponse = await queryRunner(queries?.VerifyOtp, config, model, {
      id,
      eventId,
      otp,
    });

    if (!verifyOtpResponse || !verifyOtpResponse?.data?.accessToken) {
      console.log(`Error verifyOtp`, verifyOtpResponse);
    } else {
      accessToken = verifyOtpResponse?.data?.accessToken;
    }
    const userResponse = await queryRunner(queries?.GetUser, config, model, {
      accessToken,
    });
    if (userResponse && userResponse.data) {
      user = userResponse.data;
    }
    await updateLocalStore(MERAGI_ACCESS_TOKEN, accessToken);
  } catch (error) {
    console.log(`Error signInResponse`, error);
    return {success: false, errorMessage: error};
  } finally {
    const loadedModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },
      {
        selector: selector.concat(['accessToken']),
        newValue: accessToken,
      },
      {
        selector: selector.concat(['user']),
        newValue: user,
      },
      // {
      //   selector: selector.concat(['otpContext']),
      //   newValue: null,
      // },
    ];
    dispatch(modelUpdateAction(loadedModelUpdates));
  }
};

export const meragiAuthActionsPropertySettings: PluginPropertySettings = {
  isloggedInUser: {
    getValue: model => {
      // const user = model?.get('user');
      // const accessToken = model?.get('accessToken');
      const user = model?.user;
      const accessToken = model?.accessToken;
      return !!user && !!accessToken;
    },
  },
  loginUser: {
    type: TriggerActionIdentifier,
    getValue() {
      return loginUser;
    },
    actionMetadata: {
      editableInputParams: {
        phone: '',
      },
    },
  },
  verifyOtp: {
    type: TriggerActionIdentifier,
    getValue() {
      return verifyOtp;
    },
    actionMetadata: {
      editableInputParams: {
        otp: '',
      },
    },
  },
  user: {
    updatesProps: ['isloggedInUser'],
  },
  accessToken: {
    updatesProps: ['isloggedInUser'],
  },
};
