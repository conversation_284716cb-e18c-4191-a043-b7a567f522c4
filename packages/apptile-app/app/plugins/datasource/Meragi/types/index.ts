import {DatasourceQueryDetail} from '../../../query';

type IEditableParams = Record<string, any>;

type TransformerFunction = (
  data: any,
  currencyFormatter: Function | undefined,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

export type MeragiQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  headerResolver?: (inputVariables: any) => Record<string, any>;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => boolean;
};
