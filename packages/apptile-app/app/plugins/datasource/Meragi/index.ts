import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
// import {TransformFacets, TransformSearchResults, TransformSuggestions} from './transformer';
import { LocalStorage } from 'apptile-core';
import _ from 'lodash';
import {
  IMeragiAuthActionsPluginConfig,
  MERAGI_ACCESS_TOKEN,
  meragiAuthActionsPluginConfig,
  meragiAuthActionsPropertySettings,
} from './actions/authActions';
import {queryRunner} from './utils';
import type {MeragiQueryDetails} from './types';

export interface MeragiPluginConfigType extends DatasourcePluginConfig, IMeragiAuthActionsPluginConfig {
  eventId: string;
  queryRunner: any;
}

const baseMeragiQuerySpec: Partial<MeragiQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    eventId: '',
    accessToken: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {startIndex} = paginationMeta ?? {};
    return startIndex ? {...inputVariables, startIndex} : inputVariables;
  },
};

const MeragiApiRecords: Record<string, Partial<MeragiQueryDetails>> = {
  LoginUser: {
    ...baseMeragiQuerySpec,
    queryType: 'post',
    endpoint: '/api/user/',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {eventId} = inputParams;
      const queryParams = {};
      const resolvedEndpoint = `${endpoint}${eventId}/login`;
      return resolvedEndpoint;
    },
    isPaginated: false,
    editableInputParams: {
      phone: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {phone} = inputVariables;
      return !!phone;
    },
  },
  VerifyOtp: {
    ...baseMeragiQuerySpec,
    queryType: 'post',
    endpoint: '/api/user/',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {id, eventId, otp} = inputParams;
      const resolvedEndpoint = `${endpoint}${eventId}/verifyOtp`;
      return resolvedEndpoint;
    },
    isPaginated: false,
    editableInputParams: {
      id: '',
      eventId: '',
      otp: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {id, eventId, otp} = inputVariables;
      return !!id && !!eventId && !!otp;
    },
  },
  GetUser: {
    ...baseMeragiQuerySpec,
    queryType: 'get',
    endpoint: '/api/user/',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {eventId} = inputParams;
      const resolvedEndpoint = `${endpoint}${eventId}/me`;
      return resolvedEndpoint;
    },
    headerResolver: inputParams => {
      const {accessToken} = inputParams;
      if (accessToken) {
        return {
          Authorization: 'Bearer ' + accessToken,
        };
      }
      return {};
    },
    isPaginated: false,
    editableInputParams: {},
  },
  GetEvents: {
    ...baseMeragiQuerySpec,
    queryType: 'get',
    endpoint: '/api/event/',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {eventId} = inputParams;
      const queryParams = {};
      const resolvedEndpoint = `${endpoint}${eventId}/programs${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    headerResolver: inputParams => {
      const {accessToken} = inputParams;
      if (accessToken) {
        return {
          Authorization: 'Bearer ' + accessToken,
        };
      }
      return {};
    },
    isPaginated: false,
    editableInputParams: {},
  },
  GetItinerary: {
    ...baseMeragiQuerySpec,
    queryType: 'get',
    endpoint: '/api/event/',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {eventId} = inputParams;
      const resolvedEndpoint = `${endpoint}${eventId}/itinerary`;
      return resolvedEndpoint;
    },
    headerResolver: inputParams => {
      const {accessToken} = inputParams;
      if (accessToken) {
        return {
          Authorization: 'Bearer ' + accessToken,
        };
      }
      return {};
    },
    isPaginated: false,
    editableInputParams: {},
  },
};

const propertySettings: PluginPropertySettings = {
  ...meragiAuthActionsPropertySettings,
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'meragi',
  type: 'datasource',
  name: 'Meragi',
  description: 'Meragi event source.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const MeragiEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'eventId',
      props: {
        label: 'Meragi Event ID',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'meragi',
  config: {
    eventId: '',
    queryRunner: 'queryrunner',
    ...meragiAuthActionsPluginConfig,
  } as MeragiPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<MeragiPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient('https://meragi-api.apptile.io', undefined);
    const accessToken = yield call(LocalStorage.getValue, MERAGI_ACCESS_TOKEN);
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'accessToken'],
          newValue: accessToken,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return MeragiApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = MeragiApiRecords && MeragiApiRecords[queryName] ? MeragiApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: Object): Partial<MeragiPluginConfigType> | boolean {
    const {eventId} = credentials;
    if (!eventId) return false;
    return {
      eventId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return [];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'meragi';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = MeragiApiRecords[queryName];

    if (!queryDetails) return;

    let queryResponse = yield call(queryRunner, queryDetails, dsConfig, dsModelValues, inputVariables, options);

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const shopifyDSModel = dsModelValues?.get('shopifyDS');
      const {
        data,
        hasNextPage,
        paginationMeta: pageData,
      } = queryDetails.transformer(rawData, shopifyDSModel?.formatCurrency);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: MeragiEditors,
});
