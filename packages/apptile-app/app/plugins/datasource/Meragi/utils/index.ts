import {PluginConfigType} from 'apptile-core';
import {MeragiPluginConfigType} from '..';
import {AppPageTriggerOptions, PluginModelType} from 'apptile-core';
import {isEmpty} from 'lodash';
import {MeragiQueryDetails} from '../types';

export const contextInputParamsResolver = (
  contextInputParams: {[key: string]: string} | undefined,
  dsConfig: PluginConfigType<MeragiPluginConfigType>,
  dsModelValues: PluginModelType,
) => {
  const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, MeragiPluginConfigType>;
  if (!dsPluginConfig || isEmpty(contextInputParams)) return;

  return Object.entries(contextInputParams).reduce((acc, [key, _]) => {
    if (dsPluginConfig && dsPluginConfig.get(key)) {
      return {...acc, [key]: dsPluginConfig.get(key)};
    }
    if (dsModelValues && dsModelValues.get(key)) {
      return {...acc, [key]: dsModelValues.get(key)};
    }
    return acc;
  }, {});
};

export const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          // eslint-disable-next-line radix
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
    // return acc;
  }, {});
};

export async function queryRunner(
  queryDetails: Partial<MeragiQueryDetails>,
  config: any,
  model: any,
  inputVariables: any,
  options: AppPageTriggerOptions | undefined = {},
) {
  const {getNextPage, paginationMeta} = options;

  let {endpointResolver, endpoint, contextInputParams, headerResolver} = queryDetails ?? {};
  const contextParameters = contextInputParamsResolver(contextInputParams, config, model);

  let isReadyToRun = true;
  let typedInputVariables, inputParameters;
  if (queryDetails && queryDetails.editableInputParams) {
    typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

    if (queryDetails?.checkInputVariabes) {
      isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
    }
  }

  if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
    typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
  }

  inputParameters = queryDetails.inputResolver
    ? queryDetails.inputResolver(typedInputVariables, contextParameters)
    : typedInputVariables;

  endpoint = endpointResolver && endpointResolver(endpoint, {...contextParameters, ...inputParameters}, getNextPage);
  const headers = headerResolver ? headerResolver({...contextParameters, ...inputParameters}) : {};

  const queryRunner = model.get('queryRunner');
  let queryResponse;

  if (!isReadyToRun) {
    queryResponse = {
      errors: {message: 'Missing input variables'},
      data: null,
    };
  } else {
    try {
      queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, inputParameters, {
        ...options,
        headers: {...headers},
      });
    } catch (error) {
      logger.error('error', error);
    }
  }
  return queryResponse;
}
