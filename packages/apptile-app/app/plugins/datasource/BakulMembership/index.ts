import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import bakulFreshAction from './actions/bakulFreshAction';
import { LocalStorage } from 'apptile-core';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import {objectToQueryString} from '../ProductFilter/actions/utils';

export type BakulPluginConfigType = {
  apiBaseUrl: string;
  queryRunner: any;
  selectedMemberOnlyProduct: string;
  chooseMemberOnlyProduct: string;
  syncBakulMembershipStatus: string;
  clearBakulMembershipState: string;
  bakulMembership: any;
};

type BakulQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<BakulPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, BakulPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const bakulApiRecords: Record<string, BakulQueryDetails> = {
  GetMembershipStatus: {
    queryType: 'get',
    endpoint: '/api/v1/membership/mine',
    editableInputParams: {
      platformUserId: '',
    },
    queryHeadersResolver: inputParams => {
      return {'X-Platform-User-Id': inputParams?.platformUserId ?? ''};
    },
    isPaginated: false,
  },
  GetOrderWindowStatus: {
    queryType: 'get',
    endpoint: '/api/v1/order-window/status',
    editableInputParams: {},
    isPaginated: false,
  },
  GetWeeklyMemberOnlyProductBuyCount: {
    queryType: 'get',
    endpoint: '/api/v1/membership/weekly/buy-count',
    editableInputParams: {
      platformUserId: '',
    },
    queryHeadersResolver: inputParams => {
      return {'X-Platform-User-Id': inputParams?.platformUserId ?? ''};
    },
    isPaginated: false,
  },
  GetMembershipProducts: {
    queryType: 'get',
    endpoint: '/api/v1/membership/products',
    editableInputParams: {
      platformUserId: '',
    },
    queryHeadersResolver: inputParams => {
      return {'X-Platform-User-Id': inputParams?.platformUserId ?? ''};
    },
    isPaginated: false,
  },
  SearchAddresses: {
    queryType: 'get',
    endpoint: '/api/v1/location',
    editableInputParams: {
      platformUserId: '',
      query: '',
    },
    queryHeadersResolver: inputParams => {
      return {'X-Platform-User-Id': inputParams?.platformUserId ?? ''};
    },
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const {query} = inputVariables ?? {};
      let input = {
        q: query || '',
      };
      const resolvedEndpoint = `${endpoint}?${objectToQueryString(input)}`;
      return resolvedEndpoint;
    },
    isPaginated: false,
  },
  SyncOrderStatus: {
    queryType: 'put',
    endpoint: '/api/v1/membership/order',
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const {orderId} = inputVariables;
      const resolvedEndpoint = `${endpoint}/${orderId}/sync`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      platformUserId: '',
      orderId: '',
    },
    queryHeadersResolver: inputParams => {
      return {'X-Platform-User-Id': inputParams?.platformUserId ?? ''};
    },
    isPaginated: false,
  },
};

const propertySettings: PluginPropertySettings = {
  chooseMemberOnlyProduct: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return bakulFreshAction.chooseMemberOnlyProduct;
    },
    actionMetadata: {
      editableInputParams: {
        selectedMemberOnlyProduct: '',
      },
    },
  },
  syncBakulMembershipStatus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return bakulFreshAction.syncBakulMembershipStatus;
    },
    actionMetadata: {
      editableInputParams: {
        platformUserId: '',
      },
    },
    updatesProps: ['bakulMembership'],
  },
  clearBakulMembershipState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return bakulFreshAction.syncBakulMembershipStatus;
    },
    updatesProps: ['bakulMembership'],
  },
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'bakul',
  type: 'datasource',
  name: 'Bakul Membership Integration',
  description: 'Bakul custom membership integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const bakulEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'https://stage-bakul.apptile.io',
      props: {
        label: 'API Base url',
        placeholder: 'https://stage-bakul.apptile.io',
      },
    },
  ],
};

export const executeQuery = async (
  queryName: 'GetMembershipStatus' | 'GetOrderWindowStatus',
  dsConfig: PluginConfigType<any>,
  dsModelValues: any,
  inputVariables: any,
  options: any = {},
) => {
  const queryDetails = bakulApiRecords[queryName];

  if (!queryDetails) return;
  const {getNextPage, paginationMeta} = options;

  let contextInputParam;
  if (queryDetails && queryDetails.contextInputParams) {
    const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
    contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
  }

  let typedInputVariables, typedDataVariables;
  if (queryDetails && queryDetails.editableInputParams) {
    typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
  }
  if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
    typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
  }

  typedDataVariables = queryDetails.inputResolver
    ? queryDetails.inputResolver(typedInputVariables)
    : typedInputVariables;

  let {endpoint, endpointResolver} = queryDetails;
  let headers = {};
  if (queryDetails.queryHeadersResolver) headers = queryDetails.queryHeadersResolver(typedInputVariables);

  let queryOptions = {
    ...options,
    headers,
  };

  if (endpointResolver) {
    endpoint =
      endpointResolver &&
      endpointResolver(
        endpoint,
        {
          ...typedInputVariables,
          ...contextInputParam,
        },
        paginationMeta,
      );
  }

  const queryRunner = dsModelValues.get('queryRunner');
  const queryResponse = await queryRunner.runQuery(
    queryDetails.queryType,
    endpoint,
    {...typedDataVariables, ...contextInputParam},
    queryOptions,
  );

  const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
  let transformedData = rawData;
  let queryHasNextPage, paginationDetails;
  if (queryDetails && queryDetails.transformer) {
    const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, queryDetails.transformers);

    transformedData = data;
    queryHasNextPage = hasNextPage;
    paginationDetails = pageData;
  }
  // const data = transformedData;
  return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
};

export default wrapDatasourceModel({
  name: 'BakulMembership',
  config: {
    apiBaseUrl: 'https://stage-bakul.apptile.io',
    apiAccessKey: '',
    queryRunner: 'queryrunner',
    selectedMemberOnlyProduct: '',
    chooseMemberOnlyProduct: TriggerActionIdentifier,
    syncBakulMembershipStatus: TriggerActionIdentifier,
    clearBakulMembershipState: TriggerActionIdentifier,
    bakulMembership: '',
  } as BakulPluginConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) => {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'selectedMemberOnlyProduct'],
          newValue: null,
        },
        {
          selector: [dsConfig.get('id'), 'bakulMembership'],
          newValue: (await LocalStorage.getValue('bakulMembership')) ?? null,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return bakulApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = bakulApiRecords && bakulApiRecords[queryName] ? bakulApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = bakulApiRecords[queryName];

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options;

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }
    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let {endpoint, endpointResolver} = queryDetails;
    let headers = {};
    if (queryDetails.queryHeadersResolver) headers = queryDetails.queryHeadersResolver(typedInputVariables);

    let queryOptions = {
      ...options,
      headers,
    };

    if (endpointResolver) {
      endpoint =
        endpointResolver &&
        endpointResolver(
          endpoint,
          {
            ...typedInputVariables,
            ...contextInputParam,
          },
          paginationMeta,
        );
    }

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables, ...contextInputParam},
      queryOptions,
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {
        data,
        hasNextPage,
        paginationMeta: pageData,
      } = queryDetails.transformer(rawData, queryDetails.transformers);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }
    // const data = transformedData;
    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: bakulEditors,
});
