import {modelUpdateAction} from 'apptile-core';
import {LocalStorage} from 'apptile-core';
import {executeQuery} from '..';
import {PluginConfig} from 'apptile-core';
import {ModelChange, Selector} from 'apptile-core';
import _ from 'lodash';

export interface IBakulFreshChooseMemberOnlyProductPayload {
  selectedMemberOnlyProduct: string;
}

export interface IBakulFreshSyncMembershipStatusPayload {
  platformUserId: string;
}

export interface IBakulFreshActionInterface {}

class BakulFreshAction implements IBakulFreshActionInterface {
  private async setLocalStore(key: string, value: any) {
    await LocalStorage.setValue(key, value);
  }

  private async storeBakulMembership(value: any) {
    await this.setLocalStore(`bakulMembership`, value);
  }

  chooseMemberOnlyProduct = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params as IBakulFreshChooseMemberOnlyProductPayload;
    const {selectedMemberOnlyProduct} = payload;

    const modelUpdates: ModelChange[] = [];
    modelUpdates.push({
      selector: selector.concat(['selectedMemberOnlyProduct']),
      newValue: selectedMemberOnlyProduct,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  syncBakulMembershipStatus = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params as IBakulFreshSyncMembershipStatusPayload;
    const {platformUserId} = payload;
    const modelUpdates: ModelChange[] = [];

    try {
      const previousBakulMembership = model.get('bakulMembership');
      const numerircPlatformUserId = platformUserId?.split('/')?.splice(-1);
      const GetMembershipStatusResponse = await executeQuery('GetMembershipStatus', config, model, {
        platformUserId: numerircPlatformUserId,
      });

      await this.storeBakulMembership(GetMembershipStatusResponse?.data ?? null);

      if (!_.isEqual(previousBakulMembership, GetMembershipStatusResponse?.data)) {
        modelUpdates.push({
          selector: selector.concat(['bakulMembership']),
          newValue: GetMembershipStatusResponse?.data ?? null,
        });
        dispatch(modelUpdateAction(modelUpdates, undefined, true));
      }
    } catch (error) {
      console.log(`Could not sync bakulMembership state`, error);
      await this.storeBakulMembership(null);
      modelUpdates.push({
        selector: selector.concat(['bakulMembership']),
        newValue: null,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    }
  };

  clearBakulMembershipState = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const modelUpdates: ModelChange[] = [];
    await this.storeBakulMembership(null);
    modelUpdates.push({
      selector: selector.concat(['bakulMembership']),
      newValue: null,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const bakulFreshAction = new BakulFreshAction();
export default bakulFreshAction;
