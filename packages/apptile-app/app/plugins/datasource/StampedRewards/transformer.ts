import _ from 'lodash';
import {
  IStampedGetRewardsResponse,
  TApptileGetRewardsResponse,
  TStampedEarning,
  TStampedMembershipTier,
  TStampedSpending,
} from './types';

const transformMembershipTier = (vipTier: any): TStampedMembershipTier => {
  return _.pick(vipTier, [
    'id',
    'title',
    'description',
    'titlePublic',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'pointsMultiplier',
    'goalValue',
    'listBenefits',
    'listRewards',
  ]);
};

const transformEarning = (earning: any): TStampedEarning => {
  return _.pick(earning, [
    'id',
    'title',
    'description',
    'titlePublic',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'pointsMultiplier',
  ]);
};

const transformSpendings = (spending: any): TStampedSpending => {
  return _.pick(spending, [
    'campaignEvent',
    'isCompleted',
    'totalCount',
    'campaignEventString',
    'pointsFullName',
    'id',
    'title',
    'titlePublic',
    'description',
    'urlImage',
    'points',
    'isSystem',
    'isActive',
    'isEnabledNotifications',
    'dateCreated',
    'campaignRulesList',
  ]);
};

export const TransformGetRewards = (data: IStampedGetRewardsResponse) => {
  const {customer, points, campaigns} = data;
  const {earnings, vip_tiers, spendings} = campaigns;
  const {points_current_with_name, ...restPoints} = points;
  const result = {
    customer: _.pick(customer, [
      'customerId',
      'customerEmail',
      'customerFirstName',
      'customerLastName',
      'vipTierTitle',
      'vipTierId',
      'totalOrders',
      'totalSpent',
    ]),
    points: {
      ...restPoints,
      pointsCurrentWithName: points_current_with_name,
    },
    vipTiers: vip_tiers && vip_tiers.length > 0 ? _.map(vip_tiers, vip_tier => transformMembershipTier(vip_tier)) : [],
    earnings: earnings && earnings.length > 0 ? _.map(earnings, earning => transformEarning(earning)) : [],
    spending: spendings && spendings.length > 0 ? _.map(spendings, spending => transformSpendings(spending)) : [],
  } as TApptileGetRewardsResponse;

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};
