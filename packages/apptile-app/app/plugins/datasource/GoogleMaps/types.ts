export interface IGoogleMapsAutoCompletePrediction {
  description: string;

  place_id: string;

  reference: string;
}

export interface IAddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface IGoogleMapsAutoCompleteResponse {
  predictions: IGoogleMapsAutoCompletePrediction[];
}

export interface IGoogleMapsPlaceDetailsResponse {
  html_attributions: string[];
  result: {
    address_components: IAddressComponent[];
  };
}
