import {modelUpdateAction} from 'apptile-core';
import {executeQuery} from '..';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';

//#region Interfaces

interface ISelectedPlace {
  state: string;
  city: string;
  country: string;
  zip: string;
}
export interface IGoogleMapsActionsDatasourcePluginConfig {
  getPlacesSuggestion: string;
  getPlaceDetails: string;
  predictions: any;
  sessionToken: string;
  selectedPlace: ISelectedPlace;
}

export interface IGoogleMaps {
  getPlacesSuggestion: ActionHandler;
  getPlaceDetails: ActionHandler;
}

//#endregion

export const googleMapsDatasourcePluginConfig: IGoogleMapsActionsDatasourcePluginConfig = {
  getPlacesSuggestion: TriggerActionIdentifier,
  getPlaceDetails: TriggerActionIdentifier,
  predictions: [],
  sessionToken: '',
};

export const googleMapsDatasourcePropertySettings: PluginPropertySettings = {
  getPlacesSuggestion: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return googleMapsActions.getPlacesSuggestion;
    },
    actionMetadata: {
      editableInputParams: {
        query: '',
      },
    },
  },
  getPlaceDetails: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return googleMapsActions.getPlaceDetails;
    },
    actionMetadata: {
      editableInputParams: {
        placeId: '',
      },
    },
  },
};

class GoogleMapsActions implements IGoogleMaps {
  //#region Private methods
  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];
    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }
  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('googleMaps');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  private generateUUID() {
    // Generate a random 32-bit number for the first part of the UUID
    const firstPart = (Math.random() * 0x100000000) >>> 0; // Use bitwise right shift to convert to 32-bit integer

    // Generate a random 16-bit number for the second part
    const secondPart = (Math.random() * 0x10000) >>> 0; // Use bitwise right shift to convert to 16-bit integer

    // Ensure the third part is a version 4 UUID (random)
    const thirdPart = 0x4000 | ((Math.random() * 0x1000) & 0x0fff); // Bitwise OR with 0x4000 to set the 13th character to '4'

    // Generate a random 16-bit number for the fourth part
    const fourthPart = (Math.random() * 0x10000) >>> 0; // Use bitwise right shift to convert to 16-bit integer

    // Ensure the fifth part starts with 8, 9, A, or B (hex digits)
    const fifthPart = 0x8000 | ((Math.random() * 0x1000) & 0x0fff); // Bitwise OR with 0x8000 to set the 17th character to '8', '9', 'A', or 'B'

    // Convert the parts to a formatted UUID string
    const uuid = `${this.padWithZeros(firstPart, 8)}-${this.padWithZeros(secondPart, 4)}-4${this.padWithZeros(
      thirdPart,
      3,
    )}-${this.padWithZeros(fourthPart, 4)}-${this.padWithZeros(fifthPart, 4)}`;

    return uuid;
  }

  private padWithZeros(number: number, length: number) {
    let str = number.toString(16);
    while (str.length < length) {
      str = '0' + str;
    }
    return str;
  }
  //#endregion

  getPlacesSuggestion = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    let sessionToken = model.get('sessionToken');
    const modelUpdates: ModelChange[] = [];
    if (!sessionToken || sessionToken === '') {
      sessionToken = this.generateUUID();
      modelUpdates.push({
        selector: selector.concat(['sessionToken']),
        newValue: sessionToken,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    }

    const {query} = params;
    const {data: predictions} = await this.queryExecutor(model, config, 'getSuggestion', {
      query,
      sessionToken,
    });

    modelUpdates.push({
      selector: selector.concat(['predictions']),
      newValue: predictions,
    });
    return dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  getPlaceDetails = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const sessionToken = model.get('sessionToken');
    const {placeId} = params;
    const modelUpdates: ModelChange[] = [];

    const {data: placeDetails} = await this.queryExecutor(model, config, 'getPlaceDetails', {
      sessionToken,
      placeId,
      fields: 'address_components',
    });

    modelUpdates.push({
      selector: selector.concat(['selectedPlace']),
      newValue: placeDetails,
    });
    modelUpdates.push({
      selector: selector.concat(['predictions']),
      newValue: [],
    });
    modelUpdates.push({
      selector: selector.concat(['sessionToken']),
      newValue: '',
    });
    return dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const googleMapsActions = new GoogleMapsActions();
export default googleMapsActions;
