import _ from 'lodash';
import {IGoogleMapsAutoCompleteResponse, IGoogleMapsPlaceDetailsResponse} from './types';
import {JSONMapperSchema, jsonArrayMapper} from '../ShopifyV_22_10/utils/utils';
export const GoogleMapsSuggestionsTransformer = (data: IGoogleMapsAutoCompleteResponse) => {
  if (!data) return;
  const {predictions} = data;
  const getSuggestionsSchema: JSONMapperSchema = [
    {
      field: 'title',
      path: 'description',
    },
    {
      field: 'placeId',
      path: 'place_id',
    },
  ];
  return {data: jsonArrayMapper(getSuggestionsSchema, predictions)};
};

export const GoogleMapsPlaceDetailsTransformer = (data: IGoogleMapsPlaceDetailsResponse) => {
  if (!data || !data?.result) return;
  const {address_components} = data.result;
  const state = _.find(address_components, component =>
    component.types.includes('administrative_area_level_1'),
  )?.long_name;

  const city = _.find(address_components, component => component.types.includes('locality'))?.long_name;

  const country = _.find(address_components, component => component.types.includes('country'))?.long_name;

  const zip = _.find(address_components, component => component.types.includes('postal_code'))?.long_name;

  return {
    data: {
      state,
      country,
      city,
      zip,
    },
  };
};
