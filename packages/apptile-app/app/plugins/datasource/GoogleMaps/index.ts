import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {GoogleMapsPlaceDetailsTransformer, GoogleMapsSuggestionsTransformer} from './transformer';
import {
  IGoogleMapsActionsDatasourcePluginConfig,
  googleMapsDatasourcePropertySettings,
  googleMapsDatasourcePluginConfig,
} from './actions/googleMapActions';
export type GoogleMapsConfigType = DatasourcePluginConfig &
  IGoogleMapsActionsDatasourcePluginConfig & {
    queryRunner: any;
  };

type GoogleMapsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariabes?: (inputVariables: Record<string, any>) => boolean;
};

const GoogleMapsApiRecords: Record<string, GoogleMapsQueryDetails> = {
  getSuggestion: {
    queryType: 'get',
    endpoint: '/maps/api/place/autocomplete/json',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?input=${encodeURIComponent(inputParams.query)}&sessiontoken=${inputParams.sessionToken}`;
    },
    editableInputParams: {
      query: '',
      sessionToken: '',
    },
    isPaginated: false,
    transformer: GoogleMapsSuggestionsTransformer,
    checkInputVariabes: (inputVariables: any) => {
      const {query} = inputVariables;
      return !!query;
    },
  },
  getPlaceDetails: {
    queryType: 'get',
    endpoint: '/maps/api/place/details/json',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?place_id=${inputParams.placeId}&fields=${inputParams.fields}&sessiontoken=${inputParams.sessionToken}`;
    },
    editableInputParams: {
      placeId: '',
      fields: '',
      sessionToken: '',
    },
    isPaginated: false,
    checkInputVariabes: (inputVariables: any) => {
      const {placeId} = inputVariables;
      return !!placeId;
    },
    transformer: GoogleMapsPlaceDetailsTransformer,
  },
};

const propertySettings: PluginPropertySettings = {
  ...googleMapsDatasourcePropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'googleMaps',
  type: 'datasource',
  name: 'Google Maps',
  description: 'Google Maps AutoComplete',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const GoogleMapsEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'API KEY',
        placeholder: '',
      },
    },
    // ...(authActionDatasourceEditor.basic as Editors<any>),
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<GoogleMapsConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, GoogleMapsConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const mapsApiKey = dsModelValues?.get('apiKey') ?? null;

    if (!mapsApiKey) throw new Error('Please give an API KEY');

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(queryDetails.queryType, `${endpoint}&key=${mapsApiKey}`, {
        ...typedDataVariables,
        ...contextInputParam,
      });
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'googleMaps',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'http://localhost:3000',
    queryRunner: 'queryrunner',
    ...googleMapsDatasourcePluginConfig,
  } as GoogleMapsConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<GoogleMapsConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return GoogleMapsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      GoogleMapsApiRecords && GoogleMapsApiRecords[queryName] ? GoogleMapsApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'googleMaps';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = GoogleMapsApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: GoogleMapsEditors,
});
