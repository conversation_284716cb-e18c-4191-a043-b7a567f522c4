import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';

export type AjaxQueryConfig = {};
export type AjaxContextSetter = (value: AxiosRequestConfig<any>) => AxiosRequestConfig<any>;

const resolvePayload = (data: any, config: any) => {
  let payload = data;
  if (config?.headers && config?.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
    // payload = new FormData();
    // Object.keys(data).forEach(key => {
    //   payload.append(key, data[key]);
    // });
    payload = Object.entries(data)
      .map(pair => `${pair[0]}=${pair[1]}`)
      .join('&');
  }
  return payload;
};

export default function AjaxQueryRunner() {
  const axiosInstance = axios.create();
  let API_BASE_URL = '';

  return {
    initClient: async function (apiBaseUrl: string, contextFn?: AjaxContextSetter) {
      API_BASE_URL = apiBaseUrl;
      if (contextFn) {
        axiosInstance.interceptors.request.use(contextFn);
      }
    },
    runQuery: async function (
      method: 'get' | 'post' | 'put' | 'patch' | 'delete',
      endpoint: string,
      data: any,
      config?: any,
    ): Promise<AxiosResponse<any, any>> {
      const url = API_BASE_URL + endpoint;
      switch (method) {
        case 'post':
          let payload = resolvePayload(data, config);
          return axiosInstance.post(url, payload, config);
        case 'put':
          return axiosInstance.put(url, data, config);
        case 'patch':
          return axiosInstance.patch(url, data, config);
        case 'delete':
          if (data) {
            const resolvedData = resolvePayload(data, config);
            config.data = resolvedData;
          }
          return axiosInstance.delete(url, config);
        case 'get':
        default:
          return axiosInstance.get(url, config);
      }
    },
  };
}
