import _ from 'lodash';
import {
  IProduct,
  ICloudSearchResponse,
  Product,
  ICSFilterValuesFefault,
  ICSFilterValuesCollection,
  ICSFilters,
  IProductFilter,
} from './types';

function cloudSearchLinkToHandle(link: string): string {
  return _.last(_.split(link ?? '', '/')) ?? '';
}

function formattedPrice(val: string | any, formatter: string | undefined): string {
  if (formatter && typeof formatter === 'string') {
    return `${formatter} ${val}`;
  }
  return _.toNumber(val).toString();
}

function CloudSearchItemToProduct(item: Product, formatter: string | undefined): IProduct {
  return {
    id: `gid://shopify/Product/${item?.product_id}`,
    handle: cloudSearchLinkToHandle(item?.url),
    name: item?.name,
    description: item?.hi_description,
    image_src: item?.image_src,
    sku: item?.sku,
    maxPrice: _.toNumber(item?.list_price ? item.list_price : item.price),
    minPrice: _.toNumber(item?.list_price ? item.list_price : item.price),
    displayMinPrice: formattedPrice(item?.list_price ? item.list_price : item.price, formatter),
    displayMaxPrice: formattedPrice(item?.list_price ? item.list_price : item.price, formatter),
    maxSalePrice: _.toNumber(item?.price),
    minSalePrice: _.toNumber(item?.price),
    displayMinSalePrice: formattedPrice(item?.price, formatter),
    displayMaxSalePrice: formattedPrice(item?.price, formatter),
    num_of_variants: item?.sort_int_num_variants,
  };
}

export const TransformSearchResults = (searchData: ICloudSearchResponse, offset: number | undefined) => {
  const {
    products,
    numFoundProducts,
    facets,
    categories,
    pages,
    numFoundCategories,
    resultsFor,
    suggestions,
    haveMoreResults,
    notEmpty,
    moneyFormat,
  } = searchData;

  let data: IProduct[] = [];
  if (_.isArray(products) && products.length > 0) {
    data = products.map(item => CloudSearchItemToProduct(item, moneyFormat.substring(0, 2)));
  }

  const hasNextPage = numFoundProducts > products.length;
  const paginationMeta = {
    currentPage: Math.ceil(products?.length / products?.length),
    totalPages: Math.ceil(numFoundProducts / products?.length),
    totalItems: numFoundProducts,
    offset: offset,
  };

  return {
    data: {
      products: data,
      facets,
      categories,
      pages,
      numFoundCategories,
      resultsFor,
      suggestions,
      haveMoreResults,
      notEmpty,
      moneyFormat,
    },
    hasNextPage,
    paginationMeta,
  };
};

export const TransformSuggestions = (searchData: ICloudSearchResponse, offset: number | undefined) => {
  const {
    products,
    numFoundProducts,
    facets,
    categories,
    pages,
    numFoundCategories,
    resultsFor,
    suggestions,
    haveMoreResults,
    notEmpty,
    moneyFormat,
  } = searchData;
  let items: IProduct[] = [];
  if (_.isArray(products) && products?.length > 0) {
    items = products.map(item => CloudSearchItemToProduct(item, moneyFormat.substring(0, 2)));
  }
  const data = {
    products: items,
    facets,
    categories,
    pages,
    numFoundCategories,
    resultsFor,
    suggestions,
    haveMoreResults,
    notEmpty,
    moneyFormat,
  };

  const hasNextPage = numFoundProducts > products.length;
  const paginationMeta = {
    currentPage: Math.ceil(products?.length / products?.length),
    totalPages: Math.ceil(numFoundProducts / products?.length),
    totalItems: numFoundProducts,
    offset: offset,
  };
  return {
    data,
    hasNextPage,
    paginationMeta,
  };
};

function CScountsToFilterValuesDefault(value: ICSFilterValuesFefault): IProductFilter {
  return {
    label: value?.value,
    count: value?.count,
    value: value?.value,
  };
}
function CScountsToFilterValuesCollection(value: ICSFilterValuesCollection): IProductFilter {
  return {
    label: value?.value?.name,
    value: value?.value?.handle,
    count: value?.count,
  };
}

function CSFacetsToFilters(item: ICSFilters) {
  let values = item?.type === 'collection' ? item?.counts?.map( value =>
    CScountsToFilterValuesCollection(value)) :  item?.counts?.map(value => CScountsToFilterValuesDefault(value) );
  return {
    label: item?.name,
    attribute: item?.id,
    type: item?.type,
    values: values,
    expanded: item?.expanded,
  };
}

export const TransformFacets = (searchData: ICloudSearchResponse) => {
  const {facets} = searchData;
  let data;
  if (_.isArray(facets) && facets.length > 0) {
    data = facets.map(item => CSFacetsToFilters(item));
  }

  return {
    data,
  };
};
