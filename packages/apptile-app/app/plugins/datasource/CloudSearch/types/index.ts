export interface Product {
  id: string;
  url: string;
  image_src: string;
  sku: string;
  product_id: string;
  price: number;
  list_price?: number;
  sort_int_num_variants: number;
  name: string;
  hi_name: string;
  description: string;
  hi_description: string;
  path: {
    id: number;
    name: string;
    url: string;
    handle: string;
  }[];
}

export interface IProduct {
  id: string;
  handle: string;
  name: string;
  description: string;
  image_src: string;
  sku: string;
  maxPrice: number;
  minPrice: number;
  displayMinPrice: string;
  displayMaxPrice: string;
  maxSalePrice: number;
  minSalePrice: number;
  displayMinSalePrice: string;
  displayMaxSalePrice: string;
  num_of_variants: number;
}

export interface IFacetCount {
  value: string | {name: string; handle: string};
  count: number;
}
export interface IFacet {
  name: string;
  counts: IFacetCount[];
  id: string;
  type: string;
  expanded: boolean;
  search?: boolean;
  searchPlaceholder?: string;
}

export interface IProductFilter {
    count: number;
    label: string;
    value: string;
}
export interface IProductFilters {
  values: IProductFilter[];
}

export interface ICSFilterValuesFefault {
  value: string;
  count: number;
}
export interface ICSFilterValuesCollection {
  value: {
    name: string;
    handle: string;
  };
  count: number;
}

export interface ICSFilters {
  name: string;
  type: string;
  counts: any[];
  id: string;
  expanded: boolean;
}


export interface ITranslation {
  lbl_see_more_results_for: string;
  lbl_showing_results_for: string;
  lbl_see_details: string;
  lbl_suggestions: string;
  lbl_did_you_mean: string;
  lbl_products: string;
  lbl_categories: string;
  lbl_pages: string;
  lbl_collections: string;
  lbl_cancel: string;
  sort_by: string;
  sort_relevance: string;
  sort_newest_arrivals: string;
  sort_lowest_price: string;
  sort_highest_price: string;
  total_n_results_for: string;
  total_of: string;
  total_showing: string;
  filters_link: string;
  filters_heading: string;
  filters_more: string;
  filters_less: string;
  filters_show_n_products: string;
  filters_reset_all: string;
}

export interface ICloudSearchResponse {
  products: Product[];
  numFoundProducts: number;
  facets: IFacet[];
  categories: any[];
  pages: any[];
  numFoundCategories: number;
  numFoundPages: number;
  resultsFor: string;
  suggestions: any[];
  haveMoreResults: boolean;
  poweredBy: boolean;
  modulePageUrl: string;
  demo: boolean;
  notEmpty: boolean;
  moneyFormat: string;
  translations: ITranslation;
  blocksOrder: {product: number; category: number; page: number};
  appearance: {linkColor: string; linkHoverColor: string};
  layout: string;
  filterWidgetSettings: {
    autoReload: boolean;
    buttonStyle: string;
    filterLayout: string;
    showItemCounts: boolean;
    buttonTextColor: string;
    mobileFilterLayout: string;
    buttonBackgroundColor: string;
    buttonBackgroundActiveColor: string;
    filtersEnabled: boolean;
    position: null;
  };
}
