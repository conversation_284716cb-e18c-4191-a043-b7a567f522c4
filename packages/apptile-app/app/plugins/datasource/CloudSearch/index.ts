import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {ICloudSearchCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
// import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {TransformFacets, TransformSearchResults, TransformSuggestions} from './transformer';
import _ from 'lodash';
export interface CloudSearchPluginConfigType extends ICloudSearchCredentials {
  queryRunner: any;
}

type IEditableParams = Record<string, any>;

type CloudSearchQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => boolean;
};
export type TransformerFunction = (
  data: any,
  offset?: number,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseCloudSearchQuerySpec: Partial<CloudSearchQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    shopDomain: '',
    platformType: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {startIndex} = paginationMeta ?? {};
    return startIndex ? {...inputVariables, startIndex} : inputVariables;
  },
};

const CloudSearchApiRecords: Record<string, Partial<CloudSearchQueryDetails>> = {
  SearchProducts: {
    ...baseCloudSearchQuerySpec,
    queryType: 'post',
    endpoint: '/search',
    endpointResolver: (endpoint, inputParams) => {
      const {shopDomain, platformType} = inputParams;

      const resolvedEndpoint = `${endpoint}?type=${platformType}&shop=${shopDomain}`;
      return resolvedEndpoint;
    },
    transformer: TransformSearchResults,

    paginationResolver(inputVariables, paginationMeta) {
      let newOffset;
      if (paginationMeta.hasOwnProperty('offset')) {
        newOffset = inputVariables.limits.products + Number.parseInt(paginationMeta?.offset);
      } else {
        newOffset = 0;
      }
      return {...inputVariables, offset: newOffset};
    },
    isPaginated: true,

    editableInputParams: {
      q: '',
      facet: true,
      filters: {},
      limits: {products: 3},
      collection_handle: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {q} = inputVariables;
      return !!q;
    },
  },

  GetProductFilters: {
    ...baseCloudSearchQuerySpec,
    queryType: 'post',
    endpoint: '/search',
    endpointResolver: (endpoint, inputParams) => {
      const {shopDomain, platformType} = inputParams;

      const resolvedEndpoint = `${endpoint}?type=${platformType}&shop=${shopDomain}`;
      return resolvedEndpoint;
    },
    transformer: TransformFacets,

    editableInputParams: {
      q: '',
      facet: true,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {q} = inputVariables;
      return !!q;
    },
  },

  GetSuggestions: {
    ...baseCloudSearchQuerySpec,
    queryType: 'post',
    endpoint: '/search',
    endpointResolver: (endpoint, inputParams) => {
      const {shopDomain, platformType} = inputParams;

      const resolvedEndpoint = `${endpoint}?type=${platformType}&shop=${shopDomain}`;
      return resolvedEndpoint;
    },
    transformer: TransformSuggestions,
    isPaginated: false,

    editableInputParams: {
      q: '',
      limits: {products: 5},
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'CloudSearch',
  type: 'datasource',
  name: 'Cloud Search',
  description: 'Cloud product search.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const CloudSearchEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'Api Base Url',
        placeholder: 'https://app.cloudsearchapp.com/api/v1',
      },
    },
    {
      type: 'codeInput',
      name: 'shopDomain',
      props: {
        label: 'Shop Name',
        placeholder: 'demo.myshopify.com',
      },
    },
    {
      type: 'codeInput',
      name: 'platformType',
      props: {
        label: 'Platform Type',
        placeholder: 'shopify',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<CloudSearchPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, CloudSearchPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
  }, {});
};
export default wrapDatasourceModel({
  name: 'Cloud Search',
  config: {
    apiBaseUrl: 'https://app.cloudsearchapp.com/api/v1',
    shopDomain: '',
    platformType: '',
    queryRunner: 'queryrunner',
  } as CloudSearchPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<CloudSearchPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'text/plain;charset=UTF-8',
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return CloudSearchApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      CloudSearchApiRecords && CloudSearchApiRecords[queryName] ? CloudSearchApiRecords[queryName] : null;

    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: ICloudSearchCredentials,
  ): Partial<CloudSearchPluginConfigType> | boolean {
    const {shopDomain, platformType, apiBaseUrl} = credentials;
    if (!shopDomain) return false;
    return {
      shopDomain,
      platformType,
      apiBaseUrl,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['shopDomain'];
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'cloudsearch';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = CloudSearchApiRecords[queryName];

    if (!queryDetails) return;

    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;

    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = yield call(
          queryRunner.runQuery,
          queryDetails.queryType,
          endpoint,
          JSON.stringify(typedDataVariables),
          {
            ...options,
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};

    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const offset = typedInputVariables?.offset ? typedInputVariables?.offset : 0;
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, offset);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: CloudSearchEditors,
});
