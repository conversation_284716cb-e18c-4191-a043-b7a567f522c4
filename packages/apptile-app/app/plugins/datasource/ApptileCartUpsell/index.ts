import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IApptileCartUpsellCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {call} from 'redux-saga/effects';

export type ApptileCartUpsellPluginConfigType = DatasourcePluginConfig &
  Partial<IApptileCartUpsellCredentials> & {
    appId: string;
    data: [];
  };

export const ApptileCartUpsellRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'ApptileCartUpsell',
  type: 'datasource',
  name: 'Apptile Cart Upsell',
  description: 'Apptile Cart Gamification',
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptileCartUpsellEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'APP ID',
        placeholder: '',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'ApptileCartUpsell',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io/apptile-shopify-discount-manager/api/v1/cart-upsell',
    appId: '',
    queryRunner: 'queryRunner',
    data: [],
  } as ApptileCartUpsellPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<ApptileCartUpsellPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    const appId = dsConfig.config.get('appId');

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        'Content-Type': 'application/json',
        'x-shopify-app-id': appId,
      };
      return config;
    });

    const response = yield call(queryRunner.runQuery, 'get', '/', {}, {});

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'data'],
          newValue: response.data,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ApptileCartUpsellRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      ApptileCartUpsellRecords && ApptileCartUpsellRecords[queryName] ? ApptileCartUpsellRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IApptileCartUpsellCredentials,
  ): Partial<ApptileCartUpsellPluginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'apptileCartUpsell';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptileCartUpsellEditors,
});
