import {modelUpdateAction} from 'apptile-core';
import {executeQuery} from '..';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import _ from 'lodash';

export interface IRewardActionsDatasourcePluginConfigType {
  getCustomerLoyaltyDetails: string;
  reedemPointsGenerateDiscountCode: string;
  clearRedeemedRewardsDiscount: string;
  redeemedPointDiscount: string;
  redeemedPointDiscountError: string;
  redeemedPointLoading: string;
  customerLoyaltyDetails: string;
}

export const rewardActionsDatasourcePluginConfig: IRewardActionsDatasourcePluginConfigType = {
  getCustomerLoyaltyDetails: TriggerActionIdentifier,
  reedemPointsGenerateDiscountCode: TriggerActionIdentifier,
  clearRedeemedRewardsDiscount: TriggerActionIdentifier,
  redeemedPointDiscount: '',
  redeemedPointDiscountError: '',
  redeemedPointLoading: '',
  customerLoyaltyDetails: '',
};

export interface IReedemPointsGenerateDiscountCodePayload {
  customerId: string;
  cartPrice: string;
  points: string;
}

export interface ICustomerLoyaltyDetailsPayloadInterface {
  customerId: string;
}

export interface IRewardActionsInterface {
  getCustomerLoyaltyDetails: ActionHandler;
  reedemPointsGenerateDiscountCode: ActionHandler;
  clearRedeemedRewardsDiscount: ActionHandler;
}

export const rewardActionsDatasourcePropertySettings: PluginPropertySettings = {
  getCustomerLoyaltyDetails: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return rewardActions.getCustomerLoyaltyDetails;
    },
    actionMetadata: {
      editableInputParams: {
        customerId: '',
      },
    },
  },
  reedemPointsGenerateDiscountCode: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return rewardActions.reedemPointsGenerateDiscountCode;
    },
    actionMetadata: {
      editableInputParams: {
        customerId: '',
        cartPrice: '',
        points: '',
      },
    },
  },
  clearRedeemedRewardsDiscount: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return rewardActions.clearRedeemedRewardsDiscount;
    },
  },
};

class RewardActions implements IRewardActionsInterface {
  private async queryExecutor(config: PluginConfig, model: any, queryName: string, inputVariables: any) {
    return await executeQuery(config, model, queryName, inputVariables);
  }

  private setReedemPointsErrorMessage(dispatch: any, selector: Selector, errorMessage: string) {
    let modelUpdates: ModelChange[] = [];
    modelUpdates.push({
      selector: selector.concat(['redeemedPointDiscountError']),
      newValue: errorMessage,
    });
    modelUpdates.push({
      selector: selector.concat(['redeemedPointLoading']),
      newValue: false,
    });
    toast.show(errorMessage, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 10},
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  getCustomerLoyaltyDetails = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    let modelUpdates: ModelChange[] = [];
    try {
      const payload = params as ICustomerLoyaltyDetailsPayloadInterface;
      const {customerId} = payload;

      const numericCustomerId = _.first(customerId?.split('/')?.splice(-1));

      if (!numericCustomerId) {
        const errorMessage = 'invalid customer Id supplied';
        console.log(errorMessage);
        return;
      }

      const {data, errors} = await this.queryExecutor(config, model, 'GetCustomerLoyaltyDetails', {
        customerId: numericCustomerId,
      });

      if (!_.isEmpty(errors)) {
        const errorMessage = _.first(errors) || 'An unknown error occured';
        console.log(errorMessage);
        return;
      }

      modelUpdates = [];
      modelUpdates.push({
        selector: selector.concat(['customerLoyaltyDetails']),
        newValue: data,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    } catch (error) {
      console.log(error?.message);
    }
  };
  reedemPointsGenerateDiscountCode = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    let modelUpdates: ModelChange[] = [];
    try {
      const payload = params as IReedemPointsGenerateDiscountCodePayload;
      const {customerId, cartPrice, points} = payload;

      modelUpdates.push({
        selector: selector.concat(['redeemedPointLoading']),
        newValue: true,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));

      const numericCustomerId = _.first(customerId?.split('/')?.splice(-1));

      if (!numericCustomerId) {
        const errorMessage = 'invalid customer Id supplied';
        this.setReedemPointsErrorMessage(dispatch, selector, errorMessage);
        return;
      }

      const {data, errors} = await this.queryExecutor(config, model, 'ReedemPointsGenerateDiscountCode', {
        customerId: numericCustomerId,
        cartPrice,
        points,
      });

      if (!_.isEmpty(errors)) {
        const errorMessage = _.first(errors) || 'An unknown error occured';
        this.setReedemPointsErrorMessage(dispatch, selector, errorMessage);
        return;
      }

      modelUpdates = [];
      modelUpdates.push({
        selector: selector.concat(['redeemedPointDiscount']),
        newValue: data,
      });
      modelUpdates.push({
        selector: selector.concat(['redeemedPointLoading']),
        newValue: false,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    } catch (error) {
      const errorMessage = error?.message || 'An unknown error occured';
      this.setReedemPointsErrorMessage(dispatch, selector, errorMessage);
    }
  };

  clearRedeemedRewardsDiscount = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    let modelUpdates: ModelChange[] = [];
    modelUpdates.push({
      selector: selector.concat(['redeemedPointDiscount']),
      newValue: null,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const rewardActions = new RewardActions();
export default rewardActions;
