import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {jsonToQueryString} from '../RestApi/utils';
import {DatasourcePluginConfig, IAitrillionCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {TransformGetDiscountDetails, TransformGetRewardPoints, TransformGetUnusedDiscounts} from './transformer';
import {
  IRewardActionsDatasourcePluginConfigType,
  rewardActionsDatasourcePropertySettings,
  rewardActionsDatasourcePluginConfig,
} from './actions/rewardActions';

export interface AitrillionPluginConfigType extends DatasourcePluginConfig, IRewardActionsDatasourcePluginConfigType {
  apiBaseUrl: string;
  serverApiBaseUrl: string;
  appId: string;
  // customerAccessToken: string;
  storeApiAuthenticationKey: string;
  queryRunner: any;
  headers: Record<string, any>;
}

type IEditableParams = Record<string, any>;

type AitrillionQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any, contextInputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => string;
  apiBaseUrlResolver: (dsModel: any) => string;
  editableInputParams: IEditableParams;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseAitrillionQuerySpec: Partial<AitrillionQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    // apiBaseUrl: '',
    storeApiAuthenticationKey: '',
  },
  endpointResolver: (endpoint, inputParams, getNextPage) => {
    return endpoint;
  },
  apiBaseUrlResolver: (dsModel: any) => {
    return dsModel.get('apiBaseUrl');
  },
  transformer: data => {
    return {data, hasNextPage: false, paginationMeta: {}};
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
  inputResolver: (inputVariables: any) => {
    return inputVariables;
  },
  queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
    return {};
  },
};

export const AitrillionApiRecords: Record<string, Partial<AitrillionQueryDetails>> = {
  FetchProductReviews: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/api/reviews',
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('serverApiBaseUrl');
    },
    editableInputParams: {
      product_ids: '',
      customerAccessToken: '',
    },
    contextInputParams: {
      appId: '',
    },
    endpointResolver: (endpoint, inputParams) => {
      // let product_ids = '';
      // if (inputParams?.product_ids) product_ids = inputParams.product_ids;
      // return `${endpoint}?product_ids=${product_ids}`;
      const {product_ids} = inputParams;
      const queryParams = {product_ids};
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {customerAccessToken} = inputVariables ?? {};
      const {appId} = contextInputVariables ?? {};
      return {
        'x-shopify-customer-access-token': customerAccessToken,
        'x-shopify-app-id': appId,
      };
    },
  },
  FetchProductId: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/api/products',
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('serverApiBaseUrl');
    },
    isPaginated: false,
    editableInputParams: {
      handle: '',
      customerAccessToken: '',
    },
    contextInputParams: {
      appId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {handle} = inputParams;
      const queryParams = {handle};
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {customerAccessToken} = inputVariables ?? {};
      const {appId} = contextInputVariables ?? {};
      return {
        'x-shopify-customer-access-token': customerAccessToken,
        'x-shopify-app-id': appId,
      };
    },
  },
  SubmitProductReview: {
    ...baseAitrillionQuerySpec,
    queryType: 'post',
    endpoint: '/api/reviews',
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('serverApiBaseUrl');
    },
    editableInputParams: {
      customerAccessToken: '',
      product_id: '',
      full_name: '',
      email: '',
      rating: '',
      review_title: '',
      review_description: '',
      location: '',
    },
    contextInputParams: {
      appId: '',
    },
    inputResolver: inputVariables => {
      const {product_id, full_name, email, rating, review_title, review_description, location} = inputVariables ?? {};
      return {
        review: {
          product_id: isNaN(product_id) ? product_id : Number(product_id),
          full_name: full_name,
          email: email,
          rating: isNaN(rating) ? rating : Number(rating),
          review_title: review_title,
          review_description: review_description,
          location: location,
        },
      };
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {customerAccessToken} = inputVariables ?? {};
      const {appId} = contextInputVariables ?? {};
      return {
        'x-shopify-customer-access-token': customerAccessToken,
        'x-shopify-app-id': appId,
      };
    },
  },
  GetCustomerLoyaltyDetails: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getcustomerloyaltydetails',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformGetRewardPoints,
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return !!customerId;
    },
  },
  ReedemPointsGenerateDiscountCode: {
    ...baseAitrillionQuerySpec,
    queryType: 'post',
    endpoint: '/loyalty/generatediscount',
    // transformer: TransformGetRewardPoints,
    isPaginated: false,
    editableInputParams: {
      customerId: '',
      cartPrice: '',
      points: '',
    },
    inputResolver: inputVariables => {
      const {customerId, cartPrice, points} = inputVariables ?? {};
      return {
        customer_id: customerId,
        cartprice: cartPrice,
        points: points,
      };
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId, cartPrice, points} = inputVariables;
      return customerId && cartPrice && points;
    },
  },
  GetUnusedDiscountCode: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getunusedrewards',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    // transformer: TransformGetUnusedDiscounts,
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return customerId;
    },
  },
  GetDiscountCodeDetails: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getdiscountdetail',
    isPaginated: false,
    editableInputParams: {
      discountCode: '',
    },
    transformer: TransformGetDiscountDetails,
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {discountCode} = inputParams;
      const queryParams = {
        discount_code: discountCode,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {discountCode} = inputVariables;
      return discountCode;
    },
  },
  GetEarnPointTab: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getloyaltyprogram',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}&tab=get_earn_points`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return customerId;
    },
  },
  GetRewardHistory: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getloyaltyprogram',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}&tab=get_reward_history`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return customerId;
    },
  },
  GetRewards: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getloyaltyprogram',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}&tab=get_rewards`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return customerId;
    },
  },
  GetEverything: {
    ...baseAitrillionQuerySpec,
    queryType: 'get',
    endpoint: '/loyalty/getloyaltyprogram',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId} = inputParams;
      const queryParams = {
        customer_id: customerId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId} = inputVariables;
      return customerId;
    },
  },
  GivePointsOnFollowActivity: {
    ...baseAitrillionQuerySpec,
    queryType: 'post',
    endpoint: '/loyalty/givepointsforfollowactivity',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
      ruleId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId, ruleId} = inputParams;
      const queryParams = {
        customer_id: customerId,
        rule_id: ruleId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId, ruleId} = inputVariables;
      return customerId && ruleId;
    },
  },
  ApplyGiftcode: {
    ...baseAitrillionQuerySpec,
    queryType: 'post',
    endpoint: '/loyalty/givepointsforfollowactivity',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
      giftCode: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId, giftCode} = inputParams;
      const queryParams = {
        customer_id: customerId,
        gift_code: giftCode,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId, giftCode} = inputVariables;
      return customerId && giftCode;
    },
  },
  ConvertPointsToNonMonetaryDiscount: {
    ...baseAitrillionQuerySpec,
    queryType: 'post',
    endpoint: '/loyalty/generaterewarddiscount',
    isPaginated: false,
    editableInputParams: {
      customerId: '',
      rewardType: '',
      discountId: '',
    },
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {customerId, rewardType, discountId} = inputParams;
      const queryParams = {
        customer_id: customerId,
        reward_type: rewardType,
        discount_id: discountId,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    contextInputParams: {
      storeApiAuthenticationKey: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {storeApiAuthenticationKey} = contextInputVariables ?? {};
      return {
        'x-api-key': storeApiAuthenticationKey,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerId, rewardType, discountId} = inputVariables;
      return customerId && rewardType && discountId;
    },
  },
};

const propertySettings: PluginPropertySettings = {
  ...rewardActionsDatasourcePropertySettings,
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'Aitrillion',
  type: 'datasource',
  name: 'Aitrillion Loyalty Rewards Integration',
  description: 'Aitrillion Loyalty Rewards Integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const AitrillionEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://app.aitrillion.com/api-front-v1',
      },
    },
    {
      type: 'codeInput',
      name: 'serverApiBaseUrl',
      props: {
        label: 'Server API Base url',
        placeholder: 'https://dev-api.apptile.io/aitrillion-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'app Id',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'storeApiAuthenticationKey',
      props: {
        label: 'Store Api Authentication Key',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<AitrillionPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, AitrillionPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: AitrillionQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    let {endpointResolver, contextInputParams, editableInputParams, endpoint} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables, contextInputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const apiBaseUrl = queryDetails.apiBaseUrlResolver(dsModelValues);

    let headers = {};
    if (queryDetails.queryHeadersResolver)
      headers = queryDetails.queryHeadersResolver(inputVariables, contextInputVariables);

    let queryRunner = AjaxQueryRunner();

    queryRunner.initClient(apiBaseUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          ...headers,
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    // const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables},
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, null, dsModelValues);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex: any) {
    const errors = ex?.response?.data?.errors || [ex?.message];
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: errors,
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'Aitrillion',
  config: {
    apiBaseUrl: 'https://app.aitrillion.com/api-front-v1',
    serverApiBaseUrl: 'https://dev-api.apptile.io/aitrillion-proxy',
    appId: '',
    storeApiAuthenticationKey: '',
    queryRunner: 'queryrunner',
    ...rewardActionsDatasourcePluginConfig,
  } as AitrillionPluginConfigType,

  // initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<AitrillionPluginConfigType>, dsModelValues: any) {
  //   const queryRunner = AjaxQueryRunner();
  //   queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
  //     const storeApiAuthenticationKey = dsModelValues?.get('storeApiAuthenticationKey') ?? {};
  //     config.headers = {...config.headers, ...{'x-api-key': storeApiAuthenticationKey}};
  //     return config;
  //   });
  //   return {
  //     modelUpdates: [
  //       {
  //         selector: [dsConfig.get('id'), 'queryRunner'],
  //         newValue: queryRunner,
  //       },
  //     ],
  //   };
  // },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return AitrillionApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      AitrillionApiRecords && AitrillionApiRecords[queryName] ? AitrillionApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IAitrillionCredentials,
  ): Partial<AitrillionPluginConfigType> | boolean {
    const {apiBaseUrl, storeApiAuthenticationKey} = credentials;
    if (!(apiBaseUrl && storeApiAuthenticationKey)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      storeApiAuthenticationKey: storeApiAuthenticationKey,
    };
  },
  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {},

  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'storeApiAuthenticationKey'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'aitrillion';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = AitrillionApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: AitrillionEditors,
});
