import _ from 'lodash';
import {
  ISearchanizeCategoryItem,
  ISearchanizeFilterItem,
  ISearchanizeFilterVariants,
  ISearchanizeProductItem,
  ISearchanizeProductVariantItem,
  ISearchanizeResponse,
} from './types';
import {ICollection, IProduct, IProductFilters, IProductVariant} from '../ShopifyV_22_10/types';

function imageDetailsToDisplayImage(src: string, altText: string) {
  return {
    id: '',
    src,
    altText,
  };
}

function searchanizeLinkToHandle(link: string): string {
  return _.last(_.split(link ?? '', '/')) ?? '';
}

function formattedPrice(val: string | any, formatter: Function | undefined): string {
  if (formatter && typeof formatter === 'function') {
    return formatter(_.toNumber(val));
  }
  return _.toNumber(val).toString();
}

function SearchanizeItemToProductVariant(
  item: ISearchanizeProductVariantItem,
  formatter: Function | undefined,
): IProductVariant {
  return {
    id: `gid://shopify/ProductVariant/${item?.variant_id}`,
    handle: searchanizeLinkToHandle(item?.link),
    sku: item?.sku,
    price: _.toNumber(item?.list_price), //strikeoff price
    salePrice: _.toNumber(item?.price), // discounted sale price != strikeoff price
    displayPrice: formattedPrice(item?.list_price, formatter),
    displaySalePrice: formattedPrice(item?.price, formatter),
    variantOptions: Object.entries(item?.options)?.map((key, val) => {
      return {name: key, value: val};
    }),
    availableForSale: item?.available,
    // search_variant_metafields_data;
    // filter_variant_metafields_data;
    featuredImage: item?.image_link,
    image: imageDetailsToDisplayImage(item?.image_link, item?.image_alt),
    totalInventory: _.toNumber(item?.quantity_total),
    _raw: item,
  };
}

function SearchanizeItemToProduct(item: ISearchanizeProductItem, formatter: Function | undefined): IProduct {
  return {
    title: item?.title,
    handle: searchanizeLinkToHandle(item?.link),
    id: `gid://shopify/Product/${item?.product_id}`,
    featuredImage: item?.image_link,
    description: item?.description,
    availableForSale: item?.quantity !== '0',
    maxPrice: _.toNumber(item?.list_price),
    minPrice: _.toNumber(item?.list_price),
    maxSalePrice: _.toNumber(item?.price),
    minSalePrice: _.toNumber(item?.price),
    displayMinPrice: formattedPrice(item?.list_price, formatter),
    displayMaxPrice: formattedPrice(item?.list_price, formatter),
    displayMinSalePrice: formattedPrice(item?.price, formatter),
    displayMaxSalePrice: formattedPrice(item?.price, formatter),
    totalInventory: _.toNumber(item?.quantity_total),
    variants: item?.shopify_variants?.map(v => SearchanizeItemToProductVariant(v, formatter)),
    images: item?.shopify_images?.map((imgSrc, idx) =>
      imageDetailsToDisplayImage(imgSrc, (item?.shopify_images_alt ?? [])[idx]),
    ),
    _raw: item,
  };
}

function SearchanizeCategoryToCollection(item: ISearchanizeCategoryItem): ICollection {
  return {
    title: item?.title,
    handle: searchanizeLinkToHandle(item?.link),
    id: `gid://shopify/Collection/${item?.category_id}`,
    featuredImage: imageDetailsToDisplayImage(item?.image_link, ''),
    description: item?.description,
    _raw: item,
  };
}

function SearchanizeBucketsToFilterValues(item: ISearchanizeFilterVariants): IProductFilters['values']['0'] {
  return {
    ...item,
    label: item?.value,
  };
}

function SearchanizeFacetsToFilters(item: ISearchanizeFilterItem): IProductFilters {
  return {
    label: item?.title,
    attribute: item?.attribute as string,
    type: item?.type,
    values: item?.buckets?.map(bucket => SearchanizeBucketsToFilterValues(bucket)),
  };
}

export const TransformSearchResults = (searchData: ISearchanizeResponse, formatter: Function | undefined) => {
  const {items} = searchData;
  let data: IProduct[] = [];
  if (_.isArray(items) && items.length > 0) {
    data = items.map(item => SearchanizeItemToProduct(item, formatter));
  }
  return {
    data,
    hasNextPage: searchData?.totalItems >= searchData?.startIndex + searchData?.itemsPerPage,
    paginationMeta: {startIndex: searchData?.startIndex + searchData?.itemsPerPage},
  };
};

export const TransformFacets = (searchData: ISearchanizeResponse) => {
  const {facets} = searchData;
  if (_.isArray(facets) && facets.length > 0) {
    data = facets.map(item => SearchanizeFacetsToFilters(item));
  }
  return {
    data,
    hasNextPage: searchData?.totalItems >= searchData?.startIndex + searchData?.itemsPerPage,
    paginationMeta: {startIndex: searchData?.startIndex + searchData?.itemsPerPage},
  };
};

export const TransformSuggestions = (searchData: ISearchanizeResponse, formatter: Function | undefined) => {
  const {items, suggestions, categories} = searchData;
  let products, collections;
  if (_.isArray(items) && items.length > 0) {
    products = items.map(item => SearchanizeItemToProduct(item, formatter));
  }
  if (_.isArray(categories) && categories.length > 0) {
    collections = categories.map(cat => SearchanizeCategoryToCollection(cat));
  }
  const data = {
    suggestions,
    products,
    collections,
  };
  return {
    data,
    hasNextPage: searchData?.totalItems >= searchData?.startIndex + searchData?.itemsPerPage,
    paginationMeta: {startIndex: searchData?.startIndex + searchData?.itemsPerPage},
  };
};
