export type ISearchanizeProductVariantItem = {
  variant_id: string;
  sku: string;
  barcode: string;
  price: string;
  list_price: string;
  taxable: string;
  options: any;
  available: string;
  search_variant_metafields_data: string[];
  filter_variant_metafields_data: string[];
  image_link: string;
  image_alt: string;
  quantity_total: string;
  link: string;
};

export type ISearchanizeProductItem = {
  product_id: string;
  title: string;
  description: string;
  link: string;
  price: string;
  list_price: string;
  quantity: string;
  product_code: string;
  image_link: string;
  discount: string;
  vendor?: string;
  add_to_cart_id: string;
  total_reviews?: string;
  reviews_average_score?: string;
  shopify_variants: ISearchanizeProductVariantItem[];
  shopify_images: string[];
  shopify_images_alt: string[];
  tags: string;
  quantity_total: string;
};

export type ISearchanizeCategoryItem = {
  category_id: string;
  title: string;
  link: string;
  image_link: string;
  description: string;
};

export type ISearchanizeFilterVariants = {
  value: number | string;
  from?: number;
  to?: number;
  left?: number;
  right?: number;
  count: number;
  selected: string;
};

export type ISearchanizeFilterItem = {
  title: string;
  attribute: string;
  type: 'select' | 'range' | 'slider';
  buckets: ISearchanizeFilterVariants[];
};

export interface ISearchanizeResponse {
  startIndex: number;
  itemsPerPage: number;
  totalItems: number;
  currentItemCount: number;
  items: ISearchanizeProductItem[];
  suggestions?: string[];
  facets?: ISearchanizeFilterItem[];
}
