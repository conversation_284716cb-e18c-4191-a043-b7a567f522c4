import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {TransformFacets, TransformSearchResults, TransformSuggestions} from './transformer';
import _ from 'lodash';

export interface SearchanizePluginConfigType extends DatasourcePluginConfig {
  apiKey: string;
  shopifyDS: string;
  queryRunner: any;
}

type IEditableParams = Record<string, any>;

type SearchanizeQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (
  data: any,
  currencyFormatter: Function | undefined,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseSearchanizeQuerySpec: Partial<SearchanizeQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiKey: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {startIndex} = paginationMeta ?? {};
    return startIndex ? {...inputVariables, startIndex} : inputVariables;
  },
};

const SearchanizeApiRecords: Record<string, Partial<SearchanizeQueryDetails>> = {
  SearchProducts: {
    ...baseSearchanizeQuerySpec,
    queryType: 'get',
    endpoint: '/getresults',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {apiKey, ...rest} = inputParams;
      const queryParams = {
        output: 'json',
        items: true,
        apiKey,
        ...rest,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformSearchResults,
    isPaginated: true,

    editableInputParams: {
      q: '',
      startIndex: 0,
      maxResults: 24,
      sortBy: 'relevance',
      sortOrder: 'asc',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {q} = inputVariables;
      return !!q;
    },
  },
  GetProductFilters: {
    ...baseSearchanizeQuerySpec,
    queryType: 'get',
    endpoint: '/getresults',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {apiKey, ...rest} = inputParams;
      const queryParams = {
        output: 'json',
        items: true,
        facets: true,
        startIndex: 0,
        maxResults: 1,
        sortBy: 'relevance',
        sortOrder: 'asc',
        apiKey,
        ...rest,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformFacets,
    isPaginated: false,

    editableInputParams: {
      q: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {q} = inputVariables;
      return !!q;
    },
  },
  GetSuggestions: {
    ...baseSearchanizeQuerySpec,
    queryType: 'get',
    endpoint: '/getresults',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {apiKey, count, ...rest} = inputParams;
      const queryParams = {
        output: 'json',
        items: true,
        categories: true,
        suggestions: true,
        maxResults: count > 0 ? count : 5,
        suggestionsMaxResults: count > 0 ? count : 5,
        categoriesMaxResults: count > 0 ? count : 5,
        apiKey,
        startIndex: 0,
        sortBy: 'relevance',
        sortOrder: 'asc',
        ...rest,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformSuggestions,
    isPaginated: false,

    editableInputParams: {
      q: '',
      count: 5,
    },
    // checkInputVariabes: (inputVariables: Record<string, any>) => {
    //   const {q} = inputVariables;
    //   return !!q;
    // },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'searchanize',
  type: 'datasource',
  name: 'Searchanize',
  description: 'Searchanize product search.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const StampedEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'Searchanize API Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyDS',
      props: {
        label: 'Shopify Datasource',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<SearchanizePluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, SearchanizePluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'searchanize',
  config: {
    apiKey: '',
    shopifyDS: `{{shopify}}`,
    queryRunner: 'queryrunner',
  } as SearchanizePluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<SearchanizePluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient('https://searchserverapi.com', undefined);
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return SearchanizeApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      SearchanizeApiRecords && SearchanizeApiRecords[queryName] ? SearchanizeApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: any): Partial<SearchanizePluginConfigType> | boolean {
    const {apiKey} = credentials;
    if (!apiKey) return false;
    return {
      apiKey,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiKey'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'searchanize';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = SearchanizeApiRecords[queryName];

    if (!queryDetails) return;

    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = yield call(queryRunner.runQuery, queryDetails.queryType, endpoint, typedDataVariables, {
          ...options,
          headers: {...queryDetails.headers},
        });
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const shopifyDSModel = dsModelValues?.get('shopifyDS');
      const {
        data,
        hasNextPage,
        paginationMeta: pageData,
      } = queryDetails.transformer(rawData, shopifyDSModel?.formatCurrency);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: StampedEditors,
});
