/* eslint-disable @typescript-eslint/no-unused-vars */
import Immutable from 'immutable';
import axios from 'axios';
import {call} from 'redux-saga/effects';

import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourcePluginConfig, IHRTechCredentials, IntegrationPlatformType} from 'apptile-core';
import { LocalStorage as localStorage } from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
// import {baseDatasourceConfig} from '../base';
// import {Platform} from 'react-native';

// import {transformGetProductData, transformListReviewsData} from './transformers';

export type HRTechConfigType = IHRTechCredentials & {
  userToken: string;
  userData: any; // Record<string, any>;

  setUserToken: any;
  setUserData: any;
  queryRunner: any;
};

type HRTechQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const HRTechApiRecords: Record<string, HRTechQueryDetails> = {
  login: {
    queryType: 'post',
    endpoint: '/users/login',
    editableInputParams: {
      email: '',
      password: '',
    },
  },
  userVerification: {
    queryType: 'get',
    endpoint: '/users/current',
  },
  getAllMessages: {
    queryType: 'get',
    endpoint: '/users/current/messages',
  },
  sendMessage: {
    queryType: 'post',
    endpoint: '/users/current/messages',
    editableInputParams: {
      message: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  setUserToken: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return model?.get('setUserToken');
    },
    actionMetadata: {
      editableInputParams: {userToken: ''},
    },
  },
  setUserData: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return model?.get('setUserData');
    },
    actionMetadata: {
      editableInputParams: {userData: ''},
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'HRTech',
  type: 'datasource',
  name: 'HRTech',
  description: 'HRTech datasource!',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const HRTechEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API URL',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'API Key',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<HRTechConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, HRTechConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value, 10),
        };
      } else {
        return value ? {...acc, [key]: value} : acc;
      }
    }
    return acc;
  }, {});
};

const datasourceModel: Record<string, any> = {
  name: 'HRTech',
  config: {
    isInitialized: false,

    apiBaseUrl: 'https://hrtech.apptile.io',
    apiKey: '',
    userToken: '',
    userData: '',

    setUserToken: '',
    setUserData: '',
    queryRunner: '',
  } as HRTechConfigType,

  *initDatasource(dsModel: any, dsConfig: PluginConfigType<any>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    const userToken = yield localStorage.getValue('@userToken');
    const userData = yield localStorage.getValue('@userData');

    queryRunner.initClient(requestUrl, config => config);

    return {
      modelUpdates: [
        {selector: [dsConfig.get('id'), 'isInitialized'], newValue: true},

        {selector: [dsConfig.get('id'), 'userToken'], newValue: userToken},
        {selector: [dsConfig.get('id'), 'userData'], newValue: userData},

        {selector: [dsConfig.get('id'), 'setUserToken'], newValue: datasourceModel.setUserToken},
        {selector: [dsConfig.get('id'), 'setUserData'], newValue: datasourceModel.setUserData},
        {selector: [dsConfig.get('id'), 'queryRunner'], newValue: queryRunner},
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return HRTechApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = HRTechApiRecords?.[queryName];
    return queryDetails?.editableInputParams ?? {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'HRTech';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): any {
    const queryDetails = HRTechApiRecords[queryName];
    const apiKey = dsConfig.config.get('apiKey');
    const userToken = dsModelValues.get('userToken');

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? yield call(queryDetails.inputResolver, typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');
    const requiresAuthorization = endpoint !== 'login';
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables, ...contextInputParam},
      {
        headers: {
          'x-organization-id': apiKey,
          ...(requiresAuthorization && userToken ? {Authorization: `Bearer ${userToken}`} : {}),
        },
      },
      options,
    );

    const rawData = queryResponse?.data ?? {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
    } as DatasourceQueryReturnValue;
  },

  async setUserToken(dispatch, config, model, selector, params, appConfig, appModel) {
    await localStorage.setValue('@userToken', params.userToken || '');
    dispatch(
      modelUpdateAction([{selector: selector.concat(['userToken']), newValue: params.userToken}], undefined, true),
    );
  },

  async setUserData(dispatch, config, model, selector, params, appConfig, appModel) {
    await localStorage.setValue('@userData', params.userData || '');
    dispatch(
      modelUpdateAction([{selector: selector.concat(['userData']), newValue: params.userData}], undefined, true),
    );
  },

  options: {propertySettings, pluginListing},

  editors: HRTechEditors,
};

export default wrapDatasourceModel(datasourceModel);
