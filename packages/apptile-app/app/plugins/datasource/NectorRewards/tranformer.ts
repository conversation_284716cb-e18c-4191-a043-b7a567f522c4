import moment from 'moment';
import {JSONMapperSchema, jsonArrayMapper, jsonObjectMapper} from '../ShopifyV_22_10/utils/utils';
import {
  IGetWaysToRedeemAPIResponse,
  INectorCouponsAPIResponse,
  INectorRedeemPointsAPIResponse,
  INectorUserApiResponse,
  IWalletTransactionAPIResponse,
  IWaysToEarnAPIResponse,
} from './types';

export const fetchNectorUserTransformer = (data: INectorUserApiResponse) => {
  if (!data) return;

  const schema: JSONMapperSchema = [
    {
      field: 'leadId',
      path: 'data.item._id',
    },
    {
      field: 'name',
      path: 'data.item.name',
    },
    {
      field: 'availablePoints',
      path: 'data.item.available',
    },
    {
      field: 'email',
      path: 'data.item.email',
    },
    {
      field: 'referral_code',
      path: 'data.item.referral_code',
    },
  ];
  return {
    data: jsonObjectMapper(schema, data),
  };
};

export const redeemPointsForCouponTranformer = (data: INectorRedeemPointsAPIResponse) => {
  if (!data) return;

  const schema: JSONMapperSchema = [
    {
      field: 'couponCode',
      path: 'data.coupon.value',
    },
    {
      field: 'couponDescription',
      path: 'data.coupon.name',
    },
    {
      field: 'couponId',
      path: 'data.coupon._id',
    },
    {
      field: 'expiresAt',
      path: 'data.coupon.expire',
    },
    {
      field: 'offerId',
      path: 'data.coupon.offer_id',
    },
  ];
  return {
    data: jsonObjectMapper(schema, data),
  };
};

export const getCouponsForNectorUserTransformer = (data: INectorCouponsAPIResponse) => {
  if (!data) return;

  const schema: JSONMapperSchema = [
    {
      field: 'couponCode',
      path: 'value',
    },
    {
      field: 'couponDescription',
      path: 'name',
    },
    {
      field: 'couponId',
      path: '_id',
    },
    {
      field: 'expiresAt',
      path: 'expire',
    },
    {
      field: 'offerId',
      path: 'offer_id',
    },
  ];
  return {
    data: jsonArrayMapper(schema, data?.data?.items),
  };
};

export const getWalletTransactionsTransformer = (data: IWalletTransactionAPIResponse) => {
  const {items} = data?.data;
  if (!items) return {data: []};
  const schema: JSONMapperSchema = [
    {
      field: 'amount',
      path: 'amount',
    },
    {
      field: 'title',
      path: 'title',
    },
    {
      field: 'operation',
      path: 'operation',
    },
    {
      field: 'type',
      path: 'type',
    },
    {
      field: 'status',
      path: 'status',
    },
    {
      field: 'createdAt',
      path: 'created_at',
    },
    {
      field: 'updatedAt',
      path: 'updated_at',
    },
  ];
  return {
    data: jsonArrayMapper(schema, items),
  };
};

export const getWaysToEarnTransformer = (data: IGetWaysToRedeemAPIResponse) => {
  if (!data) return;
  const {items} = data.data;
  if (!items) return {data: []};

  const schema: JSONMapperSchema = [
    {
      field: 'name',
      path: 'content.name',
    },
    {
      field: 'description',
      path: 'content.description',
    },
    {
      field: 'minCoins',
      path: 'reward.coin_range.min',
    },
    {
      field: 'maxCoins',
      path: 'reward.coin_range.max',
    },
  ];
  const res = jsonArrayMapper(schema, items);
  const modifiedRes = res.map(item => {
    return {...item, description: item.description.replace('{coin_amount}', item?.maxCoins?.toString())};
  });
  return {
    data: modifiedRes,
  };
};

export const getWaysToRedeemTransformer = (data: IWaysToEarnAPIResponse) => {
  const {businessoffers} = data.data;
  if (!businessoffers) return {data: []};
  const schema: JSONMapperSchema = [
    {field: 'id', path: '_id'},
    {field: 'description', path: 'description'},
    {field: 'name', path: 'name'},
    {field: 'minCoins', path: 'rule.fiat_range.min'},
    {field: 'maxCoins', path: 'rule.fiat_range.max'},
    {field: 'coinAmount', path: 'rule.coin_amount'},
  ];
  const res = jsonArrayMapper(schema, businessoffers.items);

  const modifiedRes = res.map(item => {
    return {subDescription: `Starts at ${item.minCoins} {coin_name} Coins`, ...item};
  });
  return {data: modifiedRes};
};

export const theyGetYouGetTransformer: TransformerFunction<any, TransformedTheyGetYouGet[]> = (
  data,
  paginationMeta?: any,
) => {
  // Use data as a key to show result in editor eg (data : data.transactions ....)
  if (
    !data ||
    !data.data ||
    !data.data.actioninfos ||
    !data.data.actioninfos.referral_action ||
    !data.data.actioninfos.referral_action.meta
  ) {
    return {data: []}; // Return empty array if data is missing at any level
  }

  return {
    data: {
      referredByTitle: data.data.actioninfos?.referral_action?.meta?.referred_by?.title,
      referredByBenefit: data.data.actioninfos?.referral_action?.meta?.referred_by?.text,
      referredToTitle: data.data.actioninfos?.referral_action?.meta?.referred_to?.title,
      referredToBenefit: data.data.actioninfos?.referral_action?.meta?.referred_to?.text,
    },
  };
};

export const listReferralsTransformer: TransformerFunction<any, TransformedListReferrals[]> = (
  data,
  paginationMeta?: any,
) => {
  // use data as a key to show result in editor eg (data : data.transactions ....)
  if (!data || !data.data.items) {
    return {data: []}; // Return empty array if data or items is missing
  }

  return {
    data: data.data.items.map(item => ({
      referred_to_name: item.referred_to_name,
      referral_status: item.status,
      referred_on: moment(item.created_at).format('DD-MM-YYYY h:mm A'),
    })),
  };
};
