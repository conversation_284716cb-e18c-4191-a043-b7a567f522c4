import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, INectorRewardsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  fetchNectorUserTransformer,
  getCouponsForNectorUserTransformer,
  getWalletTransactionsTransformer,
  getWaysToEarnTransformer,
  getWaysToRedeemTransformer,
  redeemPointsForCouponTranformer,
  theyGetYouGetTransformer,
  listReferralsTransformer,
} from './tranformer';
export type NectorRewardsConfigType = DatasourcePluginConfig & {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
};

type NectorRewardsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

const NectorRewardsApiRecords: Record<string, NectorRewardsQueryDetails> = {
  getNectorUser: {
    queryType: 'get',
    endpoint: '/merchant/leads',
    transformer: fetchNectorUserTransformer,
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}/${generateUUIDv4()}?customer_id=shopify-${inputParams.customerId}`;
    },
    editableInputParams: {
      customerId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
  },
  getCoupons: {
    queryType: 'get',
    endpoint: '/merchant/coupons',
    transformer: getCouponsForNectorUserTransformer,
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?lead_id=${inputParams.leadId}`;
    },
    editableInputParams: {
      leadId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
  },
  redeemPoints: {
    queryType: 'post',
    endpoint: '/merchant/offerredeems',
    transformer: redeemPointsForCouponTranformer,
    endpointResolver: endpoint => {
      return endpoint;
    },
    isPaginated: false,
    editableInputParams: {
      customer_id: '',
      offer_id: '',
      step: '',
      customerAccessToken: '',
    },
  },
  getWalletTrancations: {
    queryType: 'get',
    endpoint: '/merchant/wallettransactions',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?sort_op=${inputParams.sort_op}&sort=${inputParams.sort}&lead_id=${inputParams.leadId}`;
    },
    isPaginated: false,
    editableInputParams: {
      sort: '',
      sort_op: '',
      leadId: '',
      customerAccessToken: '',
    },
    checkInputVariables: inputVariables => {
      return !!inputVariables.leadId;
    },
    transformer: getWalletTransactionsTransformer,
  },
  getWaysToEarn: {
    queryType: 'get',
    endpoint: '/merchant/triggers',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?sort=${inputParams.sort}&sort_op=${inputParams.sort_op}`;
    },
    transformer: getWaysToEarnTransformer,
    isPaginated: false,
    editableInputParams: {
      sort: '',
      sort_op: '',
    },
  },
  getWaysToRedeem: {
    queryType: 'get',
    endpoint: '/merchant/aggreegatedoffers',
    isPaginated: false,
    transformer: getWaysToRedeemTransformer,
  },
  benifitsFromReferrals: {
    queryType: 'get',
    endpoint: '/merchant/aggreegateddetails',
    isPaginated: false,
    transformer: theyGetYouGetTransformer,
    editableInputParams: {
      customerAccessToken: '',
    },
  },
  listReferrals: {
    queryType: 'get',
    endpoint: '/merchant/referrals',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?referred_by_lead_id=${inputParams.lead_id}`;
    },
    isPaginated: false,
    transformer: listReferralsTransformer,
    editableInputParams: {
      lead_id: '',
      customerAccessToken: '',
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'nectorRewards',
  type: 'datasource',
  name: 'Nector Rewards and Referral',
  description: 'Nector Rewards, Loyalty and Referral',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const NectorRewardsEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'proxyUrl',
        placeholder: 'https://api.apptile.io/nector-rewards-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: '',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://platform.nector.io/api/v2',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<NectorRewardsConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, NectorRewardsConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }
    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            'Content-Type': queryDetails?.headerType || 'application/json',
            'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken,
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'nectorRewards',
  config: {
    proxyUrl: 'https://api.apptile.io/nector-rewards-proxy',
    ...baseDatasourceConfig,
    queryRunner: 'queryrunner',
  } as NectorRewardsConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<NectorRewardsConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('proxyUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
        'x-shopify-app-id': dsConfig.config?.get('appId') ?? '',
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return NectorRewardsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      NectorRewardsApiRecords && NectorRewardsApiRecords[queryName] ? NectorRewardsApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },
  resolveCredentialConfigs: function (
    credentials: INectorRewardsCredentials,
  ): Partial<NectorRewardsConfigType> | boolean {
    const {proxyUrl, appId} = credentials;
    if (!proxyUrl || !appId) return false;
    return {
      proxyUrl: proxyUrl,
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['proxyUrl', 'appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'nectorRewards';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = NectorRewardsApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: NectorRewardsEditors,
});

function generateUUIDv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
