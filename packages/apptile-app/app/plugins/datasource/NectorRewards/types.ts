export interface INectorUserApiResponse {
  data: {
    item: {
      _id: string;
      name: string;
      available: string;
      email: string;
    };
  };
}

export interface INectorRedeemPointsAPIResponse {
  data: {
    coupon: INectorCouponAPI;
  };
}

export interface INectorCouponsAPIResponse {
  data: {
    items: INectorCouponAPI[];
  };
}
interface INectorCouponAPI {
  expire: string;
  value: string;
  _id: string;
  offer_id: string;
  name: string;
}
export interface IWalletTransactionResponse {
  items: IWalletTransaction[];
  count: number;
}

export interface IWalletTransactionAPIResponse {
  meta: any;
  data: IWalletTransactionResponse;
}
export interface IWalletTransaction {
  _id: string;
  entity_id: string;
  lead_id: string;
  activity_id?: string;
  title: string;
  description: any;
  amount: string;
  closing_balance: string;
  consolidated_cr: string;
  consolidated_dr: string;
  hash: string;
  meta: Meta;
  operation: string;
  type: string;
  status: string;
  created_at: string;
  updated_at: string;
  version: number;
}

export interface Meta {
  lead_name: string;
  trigger_name?: string;
  trigger_slug?: string;
  unusedwalletcoinrequest_count: number;
  unusedwalletcoinrequest_sentat: any;
}

export interface IGetWaysToRedeemAPIResponse {
  data: {count: number; items: IWayToRedeem[]};
}

export interface IWayToRedeem {
  _id: string;
  entity_id: string;
  name: string;
  classification: string;
  tier: any;
  availed: number;
  used: number;
  rule_type: string;
  rule: {
    fiat_class: string;
    fiat_range: {
      max: number;
      min: number;
    };
    segment_id: any;
    coin_amount: number;
    fiat_target: string;
    minimumcart: {
      type: string;
      amount: number;
    };
    product_ids: any;
    category_ids: any;
    override_name: boolean;
    minimumcart_amount: number;
    combine_with_order_discount: boolean;
    combine_with_product_discount: boolean;
    combine_with_shipping_discount: boolean;
  };
  expire_after_day: number;
  description: string;
  redirect_link: string;
  status: string;
  created_at: string;
  updated_at: string;
  version: number;
  uploads: any[];
}

export interface IWaysToEarnAPIResponse {
  data: {businessoffers: {items: IWayToEarn[]; count: number}};
}

export interface IWayToEarn {
  _id: string;
  entity_id: string;
  name: string;
  classification: string;
  tier: any;
  availed: number;
  used: number;
  rule_type: string;
  rule: {
    fiat_class: string;
    fiat_range: {
      max: number;
      min: number;
    };
    segment_id: any;
    coin_amount: number;
    fiat_target: string;
    minimumcart: {
      type: string;
      amount: number;
    };
    product_ids: any;
    category_ids: any;
    override_name: boolean;
    minimumcart_amount: number;
    combine_with_order_discount: boolean;
    combine_with_product_discount: boolean;
    combine_with_shipping_discount: boolean;
  };
  expire_after_day: number;
  description: string;
  redirect_link: string;
  status: string;
  created_at: string;
  updated_at: string;
  version: number;
  uploads: any[];
}
