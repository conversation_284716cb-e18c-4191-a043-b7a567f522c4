export interface TStampedCustomer {
  customerId: string;
  customerEmail: string;
  customerFirstName: string;
  customerLastName: string;
  vipTierTitle: string;
  vipTierId: number;
  totalOrders: number;
  totalSpent: number;
}

export interface TStampedPoint {
  points: number;
  pointsCurrentWithName: string;
}

export interface IStampedReviewsResponse {
  page: number;
  data: TApptileGetReviewsData[];
  lang: string;
  shop: string;
  template: string;
  elementId: number;
  total: number;
  totalAll: number;
  totalAllWithNPS: number;
  rating: number;
  ratingAll: number;
  translations: {
    label_verified_buyer: string;
    label_shop_now: string;
  };
  isPro: boolean;
}

export interface TApptileGetReviewsData {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewDate: string;
  reviewVerifiedType: number;
  reviewReply: string;
  productId: number;
  productName: string;
  productSKU: string;
  productVariantName: string;
  shopProductId: number;
  productUrl: string;
  productImageUrl: string;
  productImageLargeUrl: string;
  productImageThumbnailUrl: string;
  avatar: string;
  location: string;
  countryIso: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  isRecommend: boolean;
  reviewType: number;
  widgetType: string;
  reviewOptionsList: {
    message: string;
    value: string;
  }[];
  featured: boolean;
}

export interface TApptileGetReviewsResponse {
  page: number;
  reviews: TApptileGetReviewsData[];
  lang: string;
  shop: string;
  template: string;
  elementId: number;
  total: number;
  totalAll: number;
  totalAllWithNPS: number;
  rating: number;
  ratingAll: number;
  translations: {
    label_verified_buyer: string;
    label_shop_now: string;
  };
  isPro: boolean;
}

export interface IStampedWriteReviewsResponse {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewVerifiedType: number;
  productId: number;
  productName: string;
  productUrl: string;
  productImageUrl: string;
  location: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  reviewType: number;
  reviewOptionsList: any[];
  featured: boolean;
}

export interface TApptileWriteReviewsResponse {
  id: number;
  author: string;
  reviewTitle: string;
  reviewMessage: string;
  reviewRating: number;
  reviewVerifiedType: number;
  productId: number;
  productName: string;
  productUrl: string;
  productImageUrl: string;
  location: string;
  reviewVotesUp: number;
  reviewVotesDown: number;
  dateCreated: string;
  reviewType: number;
  reviewOptionsList: any[];
  featured: boolean;
}

export interface IStampedCreateProductReviewBadgeResponse {
  productId: string;
  rating: number;
  count: number;
  countQuestions: number;
  c: boolean;
  badge: string;
  badgeqna: string;
}
