import _ from 'lodash';
import {
  IStampedCreateProductReviewBadgeResponse,
  IStampedReviewsResponse,
  IStampedWriteReviewsResponse,
  TApptileCreateProductReviewBadgeResponse,
  TApptileGetReviewsResponse,
  TApptileWriteReviewsResponse,
} from './types';

export const TransformGetReviews = (reviewData: IStampedReviewsResponse) => {
  const {data, ...rest} = reviewData;
  const result = {
    reviews:
      data &&
      data.length !== 0 &&
      data.map(o =>
        _.pick(o, [
          'id',
          'author',
          'reviewTitle',
          'reviewMessage',
          'reviewRating',
          'reviewDate',
          'reviewUserPhotos',
          'reviewUserVideos',
          'reviewVerifiedType',
          'reviewReply',
          'reviewReplyDate',
          'productId',
          'productName',
          'productSKU',
          'productUrl',
          'productImageUrl',
          'productImageLargeUrl',
          'productImageThumbnailUrl',
          'productDescription',
          'avatar',
          'location',
          'reviewVotesUp',
          'reviewVotesDown',
          'userReference',
          'dateCreated',
          'dateReplied',
          'reviewType',
          'widgetType',
          'reviewOptionsList',
        ]),
      ),
    ...rest,
  } as TApptileGetReviewsResponse;

  return {
    data: result,
    hasNextPage: reviewData.data.length !== 0,
    paginationMeta: {after: reviewData.page},
  };
};

export const TransformGetReviewsV1 = (reviewData: IStampedReviewsResponse) => {
  const {data} = reviewData;
  const result = data.map(o =>
    _.pick(o, [
      'id',
      'author',
      'reviewTitle',
      'reviewMessage',
      'reviewRating',
      'reviewDate',
      'reviewUserPhotos',
      'reviewUserVideos',
      'reviewVerifiedType',
      'reviewReply',
      'reviewReplyDate',
      'productId',
      'productName',
      'productSKU',
      'productUrl',
      'productImageUrl',
      'productImageLargeUrl',
      'productImageThumbnailUrl',
      'productDescription',
      'avatar',
      'location',
      'reviewVotesUp',
      'reviewVotesDown',
      'userReference',
      'dateCreated',
      'dateReplied',
      'reviewType',
      'widgetType',
      'reviewOptionsList',
    ]),
  );

  return {
    data: result,
    hasNextPage: reviewData.data.length !== 0,
    paginationMeta: {after: reviewData.page},
  };
};

export const TransformCreateReviews = (reviewData: IStampedWriteReviewsResponse) => {
  const result = _.pick(reviewData, ['id']) as TApptileWriteReviewsResponse;

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};

export const TransformCreateProductReviewBadge = (reviewData: Array<IStampedCreateProductReviewBadgeResponse>) => {
  const result =
    reviewData &&
    reviewData.length !== 0 &&
    (reviewData.map(o =>
      _.pick(o, ['productId', 'rating', 'count', 'countQuestions', 'c', 'badge', 'badgeqna']),
    ) as any);

  return {
    data: result,
    hasNextPage: false,
    paginationMeta: {},
  };
};
