import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IStampedCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformCreateProductReviewBadge,
  TransformCreateReviews,
  TransformGetReviews,
  TransformGetReviewsV1,
} from './transformer';

export interface StampedReviewsPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  publicKey: string;
  privateKey: string;
  storeHash: string;
  storeUrl: string;
  queryRunner: any;
  headers: Record<string, any>;
}

type IEditableParams = Record<string, any>;

type StampedReviewsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseStampedQuerySpec: Partial<StampedReviewsQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
    publicKey: '',
    privateKey: '',
    storeHash: '',
    storeUrl: '',
    headers: {},
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};

const StampedReviewsApiRecords: Record<string, Partial<StampedQueryDetails>> = {
  GetCustomerReviews: {
    ...baseStampedQuerySpec,
    queryType: 'get',
    endpoint: '/widget/reviews',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {publicKey, storeUrl, after, ...rest} = inputParams;
      const queryParams = {
        storeUrl: storeUrl,
        apiKey: publicKey,
        ...rest,
      };

      //calulate the next page and set it to the query params
      getNextPage ? (queryParams.page = after ? after + 1 : 1) : null;

      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformGetReviews,
    isPaginated: true,

    editableInputParams: {
      productId: '',
      sortReviews: '',
      minRating: 0,
      take: 2,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productId} = inputVariables;
      return !!productId;
    },
  },

  GetCustomerReviewsV1: {
    ...baseStampedQuerySpec,
    queryType: 'get',
    endpoint: '/widget/reviews',
    endpointResolver: (endpoint, inputParams, getNextPage) => {
      const {publicKey, storeUrl, after, ...rest} = inputParams;
      const queryParams = {
        storeUrl: storeUrl,
        apiKey: publicKey,
        ...rest,
      };

      //calulate the next page and set it to the query params
      getNextPage ? (queryParams.page = after ? after + 1 : 1) : null;

      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformGetReviewsV1,
    isPaginated: true,

    editableInputParams: {
      productId: '',
      sortReviews: '',
      minRating: 0,
      take: 2,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productId} = inputVariables;
      return !!productId;
    },
  },

  CreateCustomerReviews: {
    ...baseStampedQuerySpec,
    queryType: 'post',
    endpoint: '/reviews3',
    endpointResolver: (endpoint, inputParams) => {
      const {publicKey, storeHash} = inputParams;
      const queryParams = {
        sId: storeHash,
        apiKey: publicKey,
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    transformer: TransformCreateReviews,
    inputResolver: (inputVariables, inputParams) => {
      const {
        productId,
        author,
        email,
        location,
        reviewRating,
        reviewTitle,
        reviewMessage,
        reviewRecommendProduct,
        productName,
        productSKU,
        productImageUrl,
        productUrl,
        reviewSource,
      } = inputVariables;

      return {
        productId,
        author,
        email,
        location,
        reviewRating,
        reviewTitle,
        reviewMessage,
        reviewRecommendProduct,
        productName,
        productSKU,
        productImageUrl,
        productUrl,
        reviewSource,
      };
    },
    editableInputParams: {
      productId: '',
      author: '',
      email: '',
      location: '',
      reviewRating: '',
      reviewTitle: '',
      reviewMessage: '',
      reviewRecommendProduct: '',
      productName: '',
      productSKU: '',
      productImageUrl: '',
      productUrl: '',
      reviewSource: '',
    },
  },

  CreateProductReviewBadge: {
    ...baseStampedQuerySpec,
    queryType: 'post',
    endpoint: '/widget/badges',
    endpointResolver: (endpoint, inputParams) => {
      const queryParams = {
        isIncludeBreakdown: 'true',
        isincludehtml: 'true',
      };
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },

    transformer: TransformCreateProductReviewBadge,
    inputResolver: (inputVariables, inputParams) => {
      const {productIds} = inputVariables;

      const {publicKey, storeUrl} = inputParams;

      return {
        productIds: productIds.map((id: string) => {
          return {
            productId: id,
          };
        }),
        apiKey: publicKey,
        storeUrl,
      };
    },
    editableInputParams: {
      productIds: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {productIds} = inputVariables;
      return !!productIds;
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'stampedReviews',
  type: 'datasource',
  name: 'Stamped Reviews Integration',
  description: 'Stamped Reviews Integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const StampedEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api-v3.Stamped.io',
      },
    },
    {
      type: 'codeInput',
      name: 'publicKey',
      props: {
        label: 'Stamped Public Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'privateKey',
      props: {
        label: 'Stamped Private Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'storeHash',
      props: {
        label: 'Stamped Store Hash',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'storeUrl',
      props: {
        label: 'Stamped Store URL',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<StampedReviewsPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, StampedReviewsPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'stampedReviews',
  config: {
    apiBaseUrl: 'https://api-v3.Stamped.io',
    publicKey: '',
    privateKey: '',
    storeHash: '',
    storeUrl: '',
    queryRunner: 'queryrunner',
  } as StampedReviewsPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<StampedReviewsPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      const authHeaders = dsModelValues?.get('authHeaders') ?? {};
      config.headers = {...config.headers, ...authHeaders};
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return StampedReviewsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      StampedReviewsApiRecords && StampedReviewsApiRecords[queryName] ? StampedReviewsApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IStampedCredentials,
  ): Partial<StampedReviewsPluginConfigType> | boolean {
    const {apiBaseUrl, publicKey, privateKey, storeHash, storeUrl} = credentials;
    if (!(apiBaseUrl && publicKey && privateKey && storeHash && storeUrl)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      publicKey: publicKey,
      privateKey: privateKey,
      storeHash: storeHash,
      storeUrl: storeUrl,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'publicKey', 'privateKey', 'storeHash', 'storeUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'stampedReviews';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = StampedReviewsApiRecords[queryName];

    if (!queryDetails) return;

    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = yield call(queryRunner.runQuery, queryDetails.queryType, endpoint, typedDataVariables, {
          ...options,
          headers: {...queryDetails.headers},
        });
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: StampedEditors,
});
