import {<PERSON><PERSON><PERSON><PERSON>, AppModelRecord, GetRegisteredPluginInfo, modelUpdateAction} from 'apptile-core';
import {CartProductVariantQuantityChangeParam} from '../ShopifyV_22_10/actions/checkoutAction';
import {TransformGetProductsPaginatedQuery} from '../ShopifyV_22_10/transformers/productTransformer';
import {IProductDetail, IProductVariant} from '../ShopifyV_22_10/types';
import {processShopifyGraphqlQueryResponse} from '../utils';
import {PackageProtectionConfig} from './types';

class RedoActions {
  private getShopifyInfo(model: AppModelRecord, appConfig: any, appModel: any) {
    const shopifyDS = model?.get('shopifyDS');
    const queryRunner = shopifyDS?.queryRunner;
    const shopifyDatasourceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
    const queries = shopifyDatasourceConfig?.plugin?.getQueries();
    const shopifyDatasourceModel = appModel?.getModelValue(['shopify']);
    const shopifyPluginConfig = appConfig.getPlugin('shopify');

    const shopifyRemoveLineItemAction = shopifyDS.removeCartLineItem;
    const shopifyIncreaseCartLineQuantityAction = shopifyDS.increaseCartLineItemQuantity;

    return {
      shopifyDS,
      shopifyDatasourceModel,
      shopifyPluginConfig,
      queryRunner,
      queries,
      shopifyRemoveLineItemAction,
      shopifyIncreaseCartLineQuantityAction,
    };
  }
  private findClosestVariant(
    products: IProductDetail[],
    cartTotal: number,
    packageProtectionConfig: PackageProtectionConfig[],
    coverage: string,
  ) {
    let return_product: IProductDetail | null = null;
    let package_protection_product: IProductDetail | null = null;
    let return_and_package_protection_product: IProductDetail | null = null;
    const parsedCoverage = parseFloat(coverage);

    let protectionPercentage = 0;
    for (const config of packageProtectionConfig) {
      const min = parseFloat(config.min);
      const max = config.max === 'infinite' ? Infinity : parseFloat(config.max);
      const percentage = parseFloat(config.percentage);

      if (cartTotal >= min && cartTotal <= max) {
        protectionPercentage = percentage;
        break;
      }
    }

    products.forEach((product: IProductDetail) => {
      if (product.productType === 'return,package_protection') {
        let adjustedTotal = 0;
        let closestPriceDifference = Infinity;
        const protectionCost = (cartTotal * protectionPercentage) / 100;
        adjustedTotal += protectionCost + parsedCoverage;

        product.variants.forEach((variant: IProductVariant) => {
          const priceDifference = Math.abs(variant.price - adjustedTotal);
          if (priceDifference < closestPriceDifference) {
            closestPriceDifference = priceDifference;
            return_and_package_protection_product = {...product, variants: [variant]};
          }
        });
      } else if (product.productType === 'package_protection') {
        let adjustedTotal = 0;
        let closestPriceDifference = Infinity;
        const protectionCost = (cartTotal * protectionPercentage) / 100;
        adjustedTotal += protectionCost;

        product.variants.forEach((variant: IProductVariant) => {
          const priceDifference = Math.abs(variant.price - adjustedTotal);
          if (priceDifference < closestPriceDifference) {
            closestPriceDifference = priceDifference;
            package_protection_product = {...product, variants: [variant]};
          }
        });
      } else {
        if (product.variants.length > 1) {
          let adjustedTotal = 0;
          let closestPriceDifference = Infinity;
          const protectionCost = (cartTotal * protectionPercentage) / 100;
          adjustedTotal += protectionCost;

          product.variants.forEach((variant: IProductVariant) => {
            const priceDifference = Math.abs(variant.price - adjustedTotal);
            if (priceDifference < closestPriceDifference) {
              closestPriceDifference = priceDifference;
              return_product = {...product, variants: [variant]};
            }
          });
        } else {
          return_product = product;
        }
      }
    });

    return {
      return_product,
      package_protection_product,
      return_and_package_protection_product,
    };
  }
  getRedoProducts: ActionHandler = async (dispatch, config, model, selector, params, appConfig, appModel) => {
    const packageProtectionConfig = model.get('packageProtectionConfig');
    const coverage = model.get('coverage');
    const cartTotal = params.cartTotal;
    const {query, first, productFilters} = params;

    const {queries, queryRunner} = this.getShopifyInfo(model, appConfig, appModel);
    const searchProductsWithFilters = queries?.SearchProductsWithFilters;

    try {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const queryResponse = await queryRunner.runQuery(
        searchProductsWithFilters.queryType,
        searchProductsWithFilters.gqlTag,
        {
          query,
          first,
          productFilters,
          productMetafields: [],
          variantMetafields: [],
        },
      );
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        {},
        null,
      );

      const products = this.findClosestVariant(transformedData, cartTotal, packageProtectionConfig, coverage);

      const modelUpdates = [
        {
          selector: selector.concat(['products']),
          newValue: products,
        },
      ];
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    } catch (error) {
      console.log('Error in syncing redo products: ', error);
      const errorModelUpdates = [
        {
          selector: selector.concat(['errors']),
          newValue: error,
        },
      ];
      dispatch(modelUpdateAction(errorModelUpdates, undefined, true));
      toast.show('Error in syncing redo products', {
        type: 'error',
        placement: 'bottom',
        duration: 1500,
        style: {marginBottom: 40},
      });
    } finally {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    }
  };
  adjustCartWithCombinedProducts: ActionHandler = async (
    dispatch,
    config,
    model,
    selector,
    params,
    appConfig,
    appModel,
  ) => {
    const {
      shopifyDatasourceModel,
      shopifyPluginConfig,
      shopifyIncreaseCartLineQuantityAction,
      shopifyRemoveLineItemAction,
    } = this.getShopifyInfo(model, appConfig, appModel);
    const products = model?.get('products');
    try {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const payload1: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.return_product?.variants[0]?.id,
        quantity: 0,
        syncWithShopify: false,
        sellingPlanId: '',
        itemPrice: products.return_product?.variants[0]?.price,
      };
      const payload2: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.package_protection_product?.variants[0]?.id,
        quantity: 0,
        syncWithShopify: false,
        sellingPlanId: '',
        itemPrice: products.package_protection_product?.variants[0]?.price,
      };
      await shopifyRemoveLineItemAction(dispatch, shopifyPluginConfig, shopifyDatasourceModel, ['shopify'], payload1);
      await shopifyRemoveLineItemAction(dispatch, shopifyPluginConfig, shopifyDatasourceModel, ['shopify'], payload2);

      const addPayload: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.return_and_package_protection_product.variants[0].id,
        quantity: 1,
        syncWithShopify: false,
        sellingPlanId: '',
        itemPrice: products.return_and_package_protection_product.variants[0].price,
      };
      await shopifyIncreaseCartLineQuantityAction(
        dispatch,
        shopifyPluginConfig,
        shopifyDatasourceModel,
        ['shopify'],
        addPayload,
      );
    } catch (error) {
      console.log('Error in adjusting cart (REDO)', error);
      toast.show('Error in syncing redo products', {
        type: 'error',
        placement: 'bottom',
        duration: 1500,
        style: {marginBottom: 40},
      });
    } finally {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    }
  };
  removeBothAddPackage: ActionHandler = async (dispatch, config, model, selector, params, appConfig, appModel) => {
    const {
      shopifyDatasourceModel,
      shopifyPluginConfig,
      shopifyRemoveLineItemAction,
      shopifyIncreaseCartLineQuantityAction,
    } = this.getShopifyInfo(model, appConfig, appModel);
    const products = model?.get('products');
    try {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const removePayload: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.return_and_package_protection_product?.variants[0]?.id,
        quantity: 1,
        syncWithShopify: false,
        sellingPlanId: '',
      };
      await shopifyRemoveLineItemAction(
        dispatch,
        shopifyPluginConfig,
        shopifyDatasourceModel,
        ['shopify'],
        removePayload,
      );

      const addPayload: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.package_protection_product?.variants[0]?.id,
        quantity: 1,
        syncWithShopify: false,
        sellingPlanId: '',
        itemPrice: products.package_protection_product?.variants[0]?.price,
      };
      await shopifyIncreaseCartLineQuantityAction(
        dispatch,
        shopifyPluginConfig,
        shopifyDatasourceModel,
        ['shopify'],
        addPayload,
      );
    } catch (error) {
      console.log('Error in adjusting cart (REDO)', error);
      toast.show('Error in syncing redo products', {
        type: 'error',
        placement: 'bottom',
        duration: 1500,
        style: {marginBottom: 40},
      });
    } finally {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    }
  };
  removeBothAddReturn: ActionHandler = async (dispatch, config, model, selector, params, appConfig, appModel) => {
    const {
      shopifyDatasourceModel,
      shopifyPluginConfig,
      shopifyRemoveLineItemAction,
      shopifyIncreaseCartLineQuantityAction,
    } = this.getShopifyInfo(model, appConfig, appModel);
    const products = model?.get('products');
    const shopifyDS = model?.get('shopifyDS');
    try {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: true,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
      const currentProduct = shopifyDS?.currentCart?.lines?.find(
        (e: any) => e.variant?.product?.id === products?.return_and_package_protection_product?.id,
      );
      if (currentProduct) {
        const removePayload: CartProductVariantQuantityChangeParam = {
          merchandiseId: currentProduct?.variant?.id,
          quantity: 0,
          syncWithShopify: false,
          sellingPlanId: '',
        };
        await shopifyRemoveLineItemAction(
          dispatch,
          shopifyPluginConfig,
          shopifyDatasourceModel,
          ['shopify'],
          removePayload,
        );
      }

      const addPayload: CartProductVariantQuantityChangeParam = {
        merchandiseId: products.return_product?.variants[0]?.id,
        quantity: 1,
        syncWithShopify: false,
        sellingPlanId: '',
        itemPrice: products.return_product?.variants[0]?.price,
      };
      await shopifyIncreaseCartLineQuantityAction(
        dispatch,
        shopifyPluginConfig,
        shopifyDatasourceModel,
        ['shopify'],
        addPayload,
      );
    } catch (error) {
      console.log('Error in adjusting cart (REDO)', error);
      toast.show('Error in syncing redo products', {
        type: 'error',
        placement: 'bottom',
        duration: 1500,
        style: {marginBottom: 40},
      });
    } finally {
      const loadingModelUpdates = [
        {
          selector: selector.concat(['syncingRedo']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));
    }
  };
}

const redoActions = new RedoActions();
export default redoActions;
