import {DatasourcePluginConfig } from '../datasourceTypes';

export interface RedoPluginConfigType extends DatasourcePluginConfig {
  packageProtectionConfig: string;
  coverage: string;
  shopifyDS: string;
  syncingRedo: boolean;
  products: Record<string, any>;
  errors: Record<string, any>;
  getRedoProducts: string;
  adjustCartWithCombinedProducts: string;
  removeBothAddPackage: string;
  removeBothAddReturn: string;
}

export interface PackageProtectionConfig {
  min: string;
  max: string;
  percentage: string;
}