import { PluginEditorsConfig } from '@/root/app/common/EditorControlTypes';
import { AppPageTriggerOptions, PluginConfigType, PluginListingSettings, PluginPropertySettings, TriggerActionIdentifier } from 'apptile-core';
import { DatasourceQueryDetail, DatasourceQueryReturnValue } from '../../query';
import { baseDatasourceConfig } from '../base';
import { IntegrationPlatformType } from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import { RedoPluginConfigType } from './types';
import redoActions from './actions';

const redoApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  getRedoProducts: {
    type: TriggerActionIdentifier,
    getValue: (model, renderedValue, selector) => {
      return redoActions.getRedoProducts;
    },
    actionMetadata: {
      editableInputParams: {
        cartTotal: '',
        query: '',
        first: '',
        productFilters: ''
      },
    },
  },
  adjustCartWithCombinedProducts: {
    type: TriggerActionIdentifier,
    getValue: (model, renderedValue, selector) => {
      return redoActions.adjustCartWithCombinedProducts;
    },
  },
  removeBothAddPackage: {
    type: TriggerActionIdentifier,  
    getValue: (model, renderedValue, selector) => {
      return redoActions.removeBothAddPackage;
    }
  },
  removeBothAddReturn: {
    type: TriggerActionIdentifier,
    getValue: (model, renderedValue, selector) => {
      return redoActions.removeBothAddReturn;
    }
  }
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'redo',
  type: 'datasource',
  name: 'Redo',
  description: 'Redo integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const RedoEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'packageProtectionConfig',
      props: {
        label: 'Package Protection Config',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'coverage',
      props: {
        label: 'Coverage Price',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'redo',
  config: {
    ...baseDatasourceConfig,
    shopifyDS: '{{shopify}}',
    packageProtectionConfig: '',
    coverage: '',
    products: {},
    syncingRedo: false,
    errors: {},
    getRedoProducts: 'action',
    adjustCartWithCombinedProducts: 'action',
    removeBothAddPackage: 'action',
    removeBothAddReturn: 'action',
  } as RedoPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<RedoPluginConfigType>,
    dsModelValues: any,
  ) => {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return redoApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      redoApiRecords && redoApiRecords[queryName]
        ? redoApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'redo';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    return yield {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [],
      hasError: true,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: RedoEditors,
});