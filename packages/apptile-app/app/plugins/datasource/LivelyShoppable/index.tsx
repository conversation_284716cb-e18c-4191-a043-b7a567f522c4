import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, ILivelyCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';

export type LivelyPluginConfigType = DatasourcePluginConfig & ILivelyCredentials;

export const livelyApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'livelyShoppable',
  type: 'datasource',
  name: 'Lively Shoppable Integration',
  description: 'Lively Widgets integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const livelyEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'brandId',
      props: {
        label: 'Brand Id',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'livelyShoppable',
  config: {
    ...baseDatasourceConfig,
    brandId: '',
  } as LivelyPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<LivelyPluginConfigType>, dsModelValues: any) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return livelyApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = livelyApiRecords && livelyApiRecords[queryName] ? livelyApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: ILivelyCredentials): Partial<LivelyPluginConfigType> | boolean {
    const {brandId} = credentials;
    if (!brandId) return false;
    return {
      brandId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['brandId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'livelyShoppable';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: livelyEditors,
});
