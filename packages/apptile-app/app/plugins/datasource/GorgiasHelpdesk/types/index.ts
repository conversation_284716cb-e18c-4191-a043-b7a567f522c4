export interface IGorgiasGetTicketsResponse {
  data: IGorgiasTicket[];
  meta: IMetaObject;
  object: string;
  uri: string;
}

interface IMetaObject {
  next_cursor: string | null;
  prev_cursor: string | null;
}

export interface IGorgiasTicket {
  assignee_team: string | null;
  assignee_user: string | null;
  channel: string;
  closed_datetime: string;
  created_datetime: string;
  customer: IGorgiasCustomer;
  excerpt: string;
  external_id: string | null;
  from_agent: boolean;
  id: number;
  integrations: any;
  is_unread: boolean;
  language: string;
  last_message_datetime: string;
  last_received_message_datetime: string;
  messages_count: number;
  meta: any;
  opened_datetime: string | null;
  priority: string;
  snooze_datetime: string | null;
  status: string;
  subject: string;
  tags: any[];
  updated_datetime: string | null;
  uri: string;
  via: string;
}

interface IGorgiasCustomer {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  meta: any;
  name: string | null;
}

export interface IGorgiasTicketMessage {
  actions: string | null;
  attachments: IGorgiasAttachment[];
  body_html: string;
  body_text: string;
  channel: string;
  created_datetime: string;
  external_id: string;
  failed_datetime: string;
  from_agent: boolean;
  integration_id: number;
  last_sending_error: any;
  message_id: boolean;
  receiver: IGorgiasReceiver;
  rule_id: number;
  sender: IGorgiasSender;
  sent_datetime: string;
  source: IGorgiasSource;
  stripped_html: string;
  stripped_text: string;
  subject: string;
  ticket_id: number;
  via: string;
  uri: string;
  id: number;
}

interface IGorgiasAttachment {
  url: string;
  name: string;
  size: number;
  content_type: string;
  public: boolean;
  extra: string;
}

interface IGorgiasReceiver {
  id: number;
  email: string;
  name: string;
  firstname: string;
  lastname: string;
}

interface IGorgiasSender {
  id: number;
  email: string;
  name: string;
  firstname: string;
  lastname: string;
  meta: string;
}

interface IGorgiasSource {
  type: string;
  to: IGorgiasDetails;
  cc: IGorgiasDetails;
  bcc: IGorgiasDetails;
  from: IGorgiasDetails;
}

interface IGorgiasDetails {
  name: string;
  address: string;
}
