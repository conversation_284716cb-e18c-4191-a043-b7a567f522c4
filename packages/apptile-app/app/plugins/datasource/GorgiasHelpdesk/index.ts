import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {DatasourcePluginConfig, IGorgiasCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {TransformQueryData, TransformListMessageQueryData} from './transformers';

export interface GorgiasHelpdeskPluginConfigType extends DatasourcePluginConfig {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
}

type GorgiasHelpdeskQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: any) => string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
};
export type TransformerFunction = (data: any, email?: string) => any;

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<GorgiasHelpdeskPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, GorgiasHelpdeskPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const gorgiasHelpdeskApiRecords: Record<string, GorgiasHelpdeskQueryDetails> = {
  createTicket: {
    queryType: 'post',
    endpoint: '/tickets',
    editableInputParams: {
      subject: '',
      email: '',
      messageText: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    inputResolver: inputVariables => {
      const {subject, email, messageText, customerAccessToken} = inputVariables ?? {};
      const params = {
        subject: subject,
        messages: [
          {
            sender: {
              email: email,
            },
            channel: 'chat',
            via: 'api',
            from_agent: false,
            body_text: messageText,
          },
        ],
        channel: 'api',
      };
      return {params, customerAccessToken};
    },
  },
  listTickets: {
    queryType: 'get',
    endpoint: '/tickets',
    editableInputParams: {
      limit: '',
      orderBy: 'created_datetime%3Adesc',
      customerAccessToken: '',
    },
    isPaginated: true,
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {limit, orderBy = 'created_datetime%3Adesc'} = inputParams;
      const {nextPageCursor = ''} = paginationMeta ?? {};
      const resolvedEndpoint = `${endpoint}?limit=${limit}&order_by=${orderBy}&cursor=${nextPageCursor}`;
      return resolvedEndpoint;
    },
    transformer: TransformQueryData,
  },
  deleteTicket: {
    queryType: 'delete',
    endpoint: '/tickets',
    editableInputParams: {
      ticketId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    endpointResolver: (endpoint, inputParams) => {
      const {ticketId} = inputParams;
      const resolvedEndpoint = `${endpoint}/${ticketId}`;
      return resolvedEndpoint;
    },
  },
  listMessagesOfATicket: {
    queryType: 'get',
    endpoint: '/messages',
    editableInputParams: {
      limit: '',
      orderBy: 'created_datetime:asc',
      ticketId: '',
      email: '',
      customerAccessToken: '',
    },
    isPaginated: true,
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {nextPageCursor = ''} = paginationMeta ?? {};
      const {limit, orderBy = 'created_datetime:desc', ticketId} = inputParams;
      const resolvedEndpoint = `${endpoint}?limit=${limit}&order_by=${orderBy}&ticket_id=${ticketId}&cursor=${nextPageCursor}`;
      return resolvedEndpoint;
    },
    transformer: TransformListMessageQueryData,
  },
  sendMessageInTicket: {
    queryType: 'post',
    endpoint: '/tickets',
    editableInputParams: {
      email: '',
      messageText: '',
      ticketId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    inputResolver: inputVariables => {
      const {email, messageText, ticketId, customerAccessToken} = inputVariables ?? {};
      const params = {
        sender: {
          email: email,
        },
        body_text: messageText,
        channel: 'api',
        from_agent: false,
        via: 'api',
      };
      return {params, ticketId, customerAccessToken};
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {ticketId} = inputVariables;
      const resolvedEndpoint = `${endpoint}/${ticketId}/messages`;
      return resolvedEndpoint;
    },
  },
  deleteMessageInTicket: {
    queryType: 'delete',
    endpoint: '/tickets',
    editableInputParams: {
      ticketId: '',
      messageId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    endpointResolver: (endpoint, inputParams) => {
      const {messageId, ticketId} = inputParams;
      const resolvedEndpoint = `${endpoint}/${ticketId}/messages/${messageId}`;
      return resolvedEndpoint;
    },
  },
  updateMessageInTicket: {
    queryType: 'put',
    endpoint: '/tickets',
    editableInputParams: {
      ticketId: '',
      messageId: '',
      messageText: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    inputResolver: inputVariables => {
      const {messageText} = inputVariables ?? {};
      const paramObj = {
        body_text: messageText,
        via: 'api',
      };
      return paramObj;
    },
    endpointResolver: (endpoint, inputParams) => {
      const {messageId, ticketId} = inputParams;
      const resolvedEndpoint = `${endpoint}/${ticketId}/messages/${messageId}`;
      return resolvedEndpoint;
    },
  },
};

const propertySettings: PluginPropertySettings = {};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'gorgiasHelpdesk',
  type: 'datasource',
  name: 'Gorgias Help desk Integration',
  description: 'Gorgias help desk integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const gorgiasHelpdeskEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'proxyUrl',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'appId',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'gorgiasHelpdesk',
  config: {
    queryRunner: 'queryrunner',
    proxyUrl: 'https://api.apptile.io/gorgias-proxy/api',
    appId: '',
  } as GorgiasHelpdeskPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('proxyUrl'), config => {
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return gorgiasHelpdeskApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      gorgiasHelpdeskApiRecords && gorgiasHelpdeskApiRecords[queryName] ? gorgiasHelpdeskApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IGorgiasCredentials): Partial<GorgiasPluginConfigType> | boolean {
    const {apiKey, username} = credentials;
    if (!username || !apiKey) return false;
    return {
      apiKey: apiKey,
      username: username,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiKey', 'username'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'gorgias';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = gorgiasHelpdeskApiRecords[queryName];
      if (!queryDetails) return;

      let {transformer, endpoint, endpointResolver} = queryDetails ?? {};
      const {paginationMeta} = options ?? {};

      let contextInputParam;
      if (queryDetails && queryDetails.contextInputParams) {
        const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
        contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
      }
      const typedInputs = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      const typedInputVariables = queryDetails.inputResolver ? queryDetails.inputResolver(typedInputs) : typedInputs;
      const {email = ''} = typedInputVariables;

      if (endpointResolver) {
        endpoint =
          endpointResolver &&
          endpointResolver(
            endpoint,
            {
              ...typedInputVariables,
            },
            paginationMeta,
          );
      }

      const appId = dsConfig.config.get('appId') ?? '';
      const queryRunner = dsModelValues.get('queryRunner');
      const queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint,
        {
          ...typedInputVariables.params,
          ...contextInputParam,
        },
        {
          headers: {
            'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken ?? '',
            'X-Shopify-App-Id': appId,
          },
        },
        options,
      );

      const {limit = 10} = typedInputVariables;

      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = [];
      let queryHasNextPage, paginationDetails;
      if (transformer) {
        transformedData = transformer(rawData, email);
        const {after: currentPage = 1} = paginationMeta ?? {};
        queryHasNextPage = !!rawData.meta?.next_cursor;
        paginationDetails = {after: currentPage ? currentPage + 1 : 1, nextPageCursor: rawData.meta?.next_cursor};
      } else {
        transformedData = rawData.data;
      }
      return yield {
        rawData,
        data: transformedData,
        hasNextPage: queryHasNextPage,
        paginationMeta: paginationDetails,
      };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: gorgiasHelpdeskEditors,
});
