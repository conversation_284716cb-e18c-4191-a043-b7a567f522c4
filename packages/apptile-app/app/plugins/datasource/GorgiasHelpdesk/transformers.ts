import _ from 'lodash';
import moment from 'moment';
import get from 'lodash/get';

import {IGorgiasGetTicketsResponse, IGorgiasTicket, IGorgiasTicketMessage} from './types';

export type TransformerFunction = (data: any) => {
  data: any;
  rawData?: any;
  hasError?: boolean;
  errors?: [];
  hasNextPage?: boolean;
  paginationMeta?: any;
};

export const TransformData = (data: IGorgiasTicket): any => {
  const {
    assignee_team,
    assignee_user,
    closed_datetime,
    created_datetime,
    customer,
    excerpt,
    id,
    last_message_datetime,
    last_received_message_datetime,
    messages_count,
    status,
    subject,
    tags,
    updated_datetime,
    uri,
  } = data;

  const hasNewMessage = moment(last_message_datetime) > moment(last_received_message_datetime);

  return {
    assigneeTeam: assignee_team,
    assigneeUser: assignee_user,
    closedDatetime: closed_datetime,
    createdDatetime: created_datetime,
    customer,
    id,
    lastMessage: excerpt,
    messagesCount: messages_count,
    status,
    subject,
    tags,
    updatedDatetime: updated_datetime,
    uri,
    hasNewMessage,
  };
};

export const TransformListMessageData = (data: IGorgiasTicketMessage, email: string): any => {
  const {
    attachments,
    body_html,
    body_text,
    created_datetime,
    last_sending_error,
    message_id,
    receiver,
    sender,
    sent_datetime,
    source,
    subject,
    ticket_id,
    uri,
    id,
  } = data;
  const isRightSideMessage = email === get(sender, 'email', '');

  return {
    attachments,
    createdDatetime: created_datetime,
    html: body_html,
    id,
    messagedId: message_id,
    lastSendingError: last_sending_error,
    receiver,
    sender,
    sentDateTime: sent_datetime,
    source,
    subject,
    text: body_text,
    ticketId: ticket_id,
    uri,
    isRightSideMessage,
  };
};

export const TransformQueryData = (data: IGorgiasGetTicketsResponse) => {
  const {data: queryData = []} = data;
  return queryData.length > 0 ? queryData.map((tickets: IGorgiasTicket) => TransformData(tickets)) : [];
};

export const TransformListMessageQueryData = (data: IGorgiasGetTicketsResponse, email: string) => {
  const {data: queryData = []} = data;
  return queryData.length > 0
    ? queryData.map((message: IGorgiasTicketMessage) => TransformListMessageData(message, email))
    : [];
};
