import {PluginEditorsConfig, TriggerActionIdentifier} from 'apptile-core';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {IApptileCartDiscountsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {ApptilePinCodeCheckerPluginConfigType, ApptilePinCodeCheckerQueryDetails} from './types';
import {makeInputParamsResolver} from '../RestApi/utils';
import apptilePinCodeCheckerActions from './actions';

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseQuerySpec: Partial<ApptilePinCodeCheckerQueryDetails> = {
  isPaginated: false,
  contextInputParams: {},
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return {};
  },
  transformer: (data: any) => {
    return {data, hasNext: false, paginationMeta: {}};
  },
};

export const apptilePinCodeCheckerApiRecords: Record<string, ApptilePinCodeCheckerQueryDetails> = {
  GetPinCodesList: {
    ...baseQuerySpec,
    queryType: 'get',
    isPaginated: false,
    endpoint: '',
    editableInputParams: {
      pincodesJsonUrl: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      return inputVariables.pincodesJsonUrl;
    },
  },
};

const propertySettings: PluginPropertySettings = {
  validatePinCode: {
    type: TriggerActionIdentifier,
    getValue: (model, renderedValue, selector) => {
      return apptilePinCodeCheckerActions.validatePinCode;
    },
    actionMetadata: {
      editableInputParams: {
        pincodesJsonUrl: '',
        pinCode: '',
        successMessage: '',
        errorMessage: '',
        enableToasts: '',
      },
    },
  },
  validatingPinCode: {
    getValue: (model, renderedValue, selector) => {
      return _.get(model, selector, false);
    },
  },
  isValidPinCode: {
    getValue: (model, renderedValue, selector) => {
      return _.get(model, selector, false);
    },
  },
  pinCode: {
    getValue: (model, renderedValue, selector) => {
      return _.get(model, selector, '');
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'apptilePinCodeChecker',
  type: 'datasource',
  name: 'Apptile Pin Code Checker',
  description: 'Apptile Pin Code Checker integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptilePinCodeCheckerEditors: PluginEditorsConfig<any> = {
  basic: [],
};

const executeQuery = async function (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: ApptilePinCodeCheckerQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) {
  try {
    let {endpointResolver, contextInputParams, editableInputParams, endpoint} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    endpoint = endpointResolver! && endpointResolver(endpoint, inputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const queryRunner = dsModelValues.get('queryRunner');

    const queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, {
      ...typedDataVariables,
    });

    // @ts-ignore
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};

    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNext, paginationMeta: pageData} = queryDetails.transformer(rawData, typedInputVariables);

      transformedData = data;
      queryHasNextPage = hasNext;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (error) {
    logger.error(error);
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'apptilePinCodeChecker',
  config: {
    ...baseDatasourceConfig,
    queryRunner: 'queryRunner',
    validatePinCode: 'action',
    validatingPinCode: false,
    isValidPinCode: false,
    pinCode: '',
    enableToasts: true,
  } as ApptilePinCodeCheckerPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<ApptilePinCodeCheckerPluginConfigType>,
    dsModelValues: any,
  ) => {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient('', config => {
      config.headers = {
        ...config.headers,
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return apptilePinCodeCheckerApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      apptilePinCodeCheckerApiRecords && apptilePinCodeCheckerApiRecords[queryName]
        ? apptilePinCodeCheckerApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IApptileCartDiscountsCredentials,
  ): Partial<ApptilePinCodeCheckerPluginConfigType> | boolean {
    return {};
  },
  resolveClearCredentialConfigs: function (): string[] {
    return [];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'apptilePinCodeChecker';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ) {
    const queryDetails = apptilePinCodeCheckerApiRecords[queryName];
    if (!queryDetails) return;
    // @ts-ignore
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptilePinCodeCheckerEditors,
});
