import {DatasourcePluginConfig} from '../datasourceTypes';
import {DatasourceQueryDetail} from '../../query/index';

export type IEditableParams = Record<string, any>;

export type TransformerFunction<P, Q> = (
  data: P,
  paginationMeta?: any,
) => {data: Q; hasNext?: boolean; paginationMeta?: any};

export type ApptilePinCodeCheckerQueryDetails = DatasourceQueryDetail & {
  queryType: 'get';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction<unknown, unknown>;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

export interface ApptilePinCodeCheckerPluginConfigType extends DatasourcePluginConfig {}
