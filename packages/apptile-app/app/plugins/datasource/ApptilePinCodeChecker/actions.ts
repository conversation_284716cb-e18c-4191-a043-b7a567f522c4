import {ActionHandler, modelUpdateAction} from 'apptile-core';

class ApptilePinCodeCheckerActions {
  validatePinCode: ActionHandler = async (dispatch, config, model, selector, params) => {
    try {
      dispatch(
        modelUpdateAction(
          [
            {
              selector: selector.concat(['validatingPinCode']),
              newValue: true,
            },
          ],
          undefined,
          true,
        ),
      );

      // @ts-ignore
      const queryRunner = model.get('queryRunner');

      const result = await queryRunner.runQuery('get', params.pincodesJsonUrl);

      const isValidPinCode = result?.data?.valid_pin_codes?.find((pinCode: string) => pinCode === params.pinCode);

      const enableToasts =
        params.enableToasts !== undefined && params.enableToasts !== ''
          ? params.enableToasts
          : model.get('enableToasts') ?? true;

      if (enableToasts) {
        if (!isValidPinCode) {
          toast.show(params.errorMessage, {type: 'error'});
        } else {
          toast.show(params.successMessage, {type: 'success'});
        }
      }

      dispatch(
        modelUpdateAction(
          [
            {
              selector: selector.concat(['validatingPinCode']),
              newValue: false,
            },
            {
              selector: selector.concat(['isValidPinCode']),
              newValue: !!isValidPinCode,
            },
            {
              selector: selector.concat(['isInvalidValidPinCode']),
              newValue: !isValidPinCode,
            },
            {
              selector: selector.concat(['pinCode']),
              newValue: params.pinCode,
            },
          ],
          undefined,
          true,
        ),
      );
    } catch (error) {
      console.log('error', error);
      dispatch(
        modelUpdateAction(
          [
            {
              selector: selector.concat(['validatingPinCode']),
              newValue: false,
            },
            {
              selector: selector.concat(['errors']),
              newValue: [error],
            },
          ],
          undefined,
          true,
        ),
      );
    }
  };
}

const apptilePinCodeCheckerActions = new ApptilePinCodeCheckerActions();
export default apptilePinCodeCheckerActions;
