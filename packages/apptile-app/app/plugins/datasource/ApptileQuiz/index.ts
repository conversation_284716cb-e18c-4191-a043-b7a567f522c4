import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail} from '../../query';
import {DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import quizActions from './actions/quizActions';
import {SelectedAnswers} from './types';

export interface ApptileQuizPluginConfigType {
  apiBaseUrl: string;
  apiAccessKey: string;
  selectedAnswers: SelectedAnswers;
  queryRunner: any;
  setAnswers: any;
  curateResults: any;
  curatedResults?: string[];
  removeSetAnswers: any;
  resultsCurated: boolean;
}

type ApptileQuizQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<ApptileQuizPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ApptileQuizPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const apptileQuizApiRecords: Record<string, ApptileQuizQueryDetails> = {
  PostAnswers: {
    queryType: 'post',
    endpoint: '/quiz',
    endpointResolver: (endpoint, inputVariables) => {
      return endpoint;
    },
    editableInputParams: {
      customerEmail: '',
      selectedAnswers: '', //json with question id and answer
    },
    inputResolver: inputVariables => {
      const {selectedAnswers, customerEmail} = inputVariables;

      return {selectedAnswers, customerEmail};
    },
  },
};

const propertySettings: PluginPropertySettings = {
  setAnswers: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return quizActions.setAnswers;
    },
    actionMetadata: {
      editableInputParams: {
        quizId: '',
        questionId: '',
        question: '',
        answer: '',
        multiSelect: '',
      },
    },
  },
  curateResults: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return quizActions.curateResults;
    },
    actionMetadata: {
      editableInputParams: {
        resultsMapping: '',
        quizId: '',
      },
    },
  },
  removeSetAnswers: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return quizActions.removeSetAnswers;
    },
  },
  setResultsCurated: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return quizActions.setResultsCurated;
    },
    actionMetadata: {
      editableInputParams: {
        resultCurationStatus: '',
      },
    },
  },
};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'apptileQuiz',
  type: 'datasource',
  name: 'ApptileQuiz Integration',
  description: 'ApptileQuiz store front integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const apptileQuizEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://webhook.site/0a11ffa7-e175-4a0e-a3ae-6def372fd115',
      },
    },
    {
      type: 'codeInput',
      name: 'apiAccessKey',
      props: {
        label: 'API Key',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'ApptileQuiz',
  config: {
    apiBaseUrl: '',
    apiAccessKey: '',
    queryRunner: 'queryrunner',
    selectedAnswers: {} as any,
    setAnswers: 'action',
    curateResults: 'action',
    curatedResults: [''],
    resultsCurated: false,
    removeSetAnswers: 'action',
    setResultsCurated: 'action',
  } as ApptileQuizPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
        },
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'selectedAnswers'],
          newValue: {},
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return apptileQuizApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      apptileQuizApiRecords && apptileQuizApiRecords[queryName] ? apptileQuizApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = apptileQuizApiRecords[queryName];

    if (!queryDetails) return;

    let {endpointResolver, endpoint} = queryDetails ?? {};

    const {getNextPage, paginationMeta} = options;

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      // inputResolver:: To handle nested input in graphql
    }
    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const queryRunner = dsModelValues.get('queryRunner');

    endpoint = endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables);

    let queryResponse;
    try {
      queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint,
        {...typedDataVariables},
        {
          headers: {
            'content-type': 'application/json',
          },
        },
      );
    } catch (error) {
      logger.error(error);
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {
        data,
        hasNextPage,
        paginationMeta: pageData,
      } = queryDetails.transformer(rawData, queryDetails.transformers);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }
    // const data = transformedData;
    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: apptileQuizEditors,
});
