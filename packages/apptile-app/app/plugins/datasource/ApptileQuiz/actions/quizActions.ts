import {ActionHandler} from '../../../triggerAction';
import {PluginConfig} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {Selector} from 'apptile-core';
import _ from 'lodash';
import {Question} from '../types';

export interface QuizActionInteface {
  setAnswers: ActionHandler;
}

class QuizActions implements QuizActionInteface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    key: string,
  ) {
    const modelUpdates = [];
    const lineItemsSelector = selector.concat([key]);
    modelUpdates.push({
      selector: lineItemsSelector,
      newValue: lineItems,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  setAnswers = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    let currentSelectedAnswers = model.get('selectedAnswers') || {};

    const {quizId, questionId, question, answer, multiSelect} = params;

    let requiredQuiz: Question[] = currentSelectedAnswers[quizId];
    let specificQuestion: Question | undefined = _.find(requiredQuiz, ['id', questionId]);

    if (multiSelect) {
      if (!requiredQuiz) {
        currentSelectedAnswers[quizId] = [{id: questionId, question, answer: [answer]}];
      } else {
        if (specificQuestion) {
          //if the answer array includes the string then remove. else push it in the answer array
          if (typeof answer === 'string' && typeof specificQuestion.answer === 'object') {
            _.includes(specificQuestion.answer, answer)
              ? _.pull(specificQuestion.answer, answer)
              : specificQuestion.answer.push(answer);
          } else {
            specificQuestion.answer = answer;
          }
          //now replacing the previous question from requiredQuiz with specific question
          requiredQuiz = _.unionBy([specificQuestion], requiredQuiz, 'id');
        } else {
          specificQuestion = {id: questionId, question, answer: [answer]};
          requiredQuiz.push(specificQuestion);
        }
        currentSelectedAnswers[quizId] = requiredQuiz;

        //removing the whole object from answers list if the answer array is empty
        if (_.isEmpty(specificQuestion.answer)) {
          currentSelectedAnswers[quizId] = requiredQuiz.filter((q: Question) => {
            if (q.id !== questionId) {
              return q;
            }
          });
        }
      }
    } else {
      //For selection
      currentSelectedAnswers[quizId] = _.unionBy([{id: questionId, question, answer: answer}], requiredQuiz, 'id');
      if (specificQuestion) {
        if (specificQuestion.answer === answer) {
          //For deselection process
          currentSelectedAnswers[quizId] = requiredQuiz.filter((q: Question) => {
            if (q.id !== questionId) {
              return q;
            }
          });
        }
      }
    }

    await this.applyChanges(dispatch, config, model, selector, currentSelectedAnswers, 'selectedAnswers');
  };

  removeSetAnswers = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    await this.applyChanges(dispatch, config, model, selector, {}, 'selectedAnswers');
  };

  curateResults = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    await this.applyChanges(dispatch, config, model, selector, false, 'resultsCurated');
    const {resultsMapping, quizId} = params;
    const selectedAnswers = model.get('selectedAnswers') || {};
    const currentSelectedAnswers: Question[] = selectedAnswers[quizId];

    const answersMap = currentSelectedAnswers.map((a: Question) => a.answer);
    const answersFlatMap = _.flattenDeep(answersMap);

    const resultMappingKeys = Object.keys(resultsMapping);

    const answerResultIntersection = _.intersection(resultMappingKeys, answersFlatMap);

    const resultsArray = answerResultIntersection.map((ar: any) => resultsMapping[ar]);

    const resultsFlatMap = _.flattenDeep(resultsArray);

    await this.applyChanges(dispatch, config, model, selector, resultsFlatMap, 'curatedResults');
    await this.applyChanges(dispatch, config, model, selector, true, 'resultsCurated');
  };

  setResultsCurated = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const {resultCurationStatus} = params;

    await this.applyChanges(dispatch, config, model, selector, resultCurationStatus, 'resultsCurated');
  };
}

const quizActions = new QuizActions();
export default quizActions;
