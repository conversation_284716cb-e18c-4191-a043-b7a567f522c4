import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {DatasourcePluginConfig, IJudgeMeCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {baseDatasourceConfig} from '../base';
import {Platform} from 'react-native';

import {transformGetProductData, transformListReviewsData} from './transformers';

export type JudgeMeConfigType = DatasourcePluginConfig &
  IJudgeMeCredentials & {
    queryRunner: any;
  };

type JudgeMeQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const JudgeMeApiRecords: Record<string, JudgeMeQueryDetails> = {
  getProduct: {
    queryType: 'get',
    endpoint: '/products',
    endpointResolver: (endpoint: string, inputVariables: any) =>
      `${endpoint}/${inputVariables.judgeMeProductId ?? -1}?handle=${inputVariables.handle}`, // * -1 in url is JudgeMe Product Id
    editableInputParams: {
      handle: '',
    },
    transformer: transformGetProductData,
  },
  listProductReviews: {
    queryType: 'get',
    endpoint: '/reviews',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {after = 1} = paginationMeta ?? {};
      return `${endpoint}?product_id=${inputParams.judgeMeProductId}&per_page=${inputParams.size ?? 10}&page=${after}${
        inputParams.rating ? `&rating=${inputParams.rating}` : ''
      }`;
    },
    editableInputParams: {
      judgeMeProductId: '',
      size: '',
      rating: '',
    },
    transformer: transformListReviewsData,
    isPaginated: true,
  },
  getProductReviewCount: {
    queryType: 'get',
    endpoint: '/reviews/count',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?product_id=${inputParams.judgeMeProductId}${
        inputParams.rating ? `&rating=${inputParams.rating}` : ''
      }`;
    },
    editableInputParams: {
      judgeMeProductId: '',
      rating: '',
    },
  },
  postProductReview: {
    queryType: 'post',
    endpoint: '/reviews',
    editableInputParams: {
      username: '',
      email: '',
      rating: '',
      body: '',
      productId: '',
      title: '',
    },
    inputResolver: (inputVariables: any) => {
      const {username, email, rating, body, productId, title} = inputVariables;

      return {
        platform: 'shopify',
        name: username,
        email,
        rating,
        body,
        id: productId,
        title,
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'judgeMe',
  type: 'datasource',
  name: 'Judge Me',
  description: 'Boost Sales with Judge.me Product Reviews!',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const JudgeMeEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'shopDomain',
      props: {
        label: 'Shop domain',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiToken',
      props: {
        label: 'API Key',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'Proxy Url',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<JudgeMeConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, JudgeMeConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'judgeMe',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://judge.me/api/v1',
    proxyUrl: 'https://api.apptile.io/judge-me-proxy/',
    shopDomain: '',
    apiToken: '',
    queryRunner: 'queryrunner',
  } as JudgeMeConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<JudgeMeConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = Platform.OS === 'web' ? dsConfig.config.get('proxyUrl') : dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return JudgeMeApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = JudgeMeApiRecords && JudgeMeApiRecords[queryName] ? JudgeMeApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'judgeMe';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = JudgeMeApiRecords[queryName];
    const shopDomain = dsConfig.config.get('shopDomain');
    const apiToken = dsConfig.config.get('apiToken');
    const apiUrl = dsConfig.config.get('apiBaseUrl');

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? yield call(queryDetails.inputResolver, typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }
    endpoint = endpoint.includes('?')
      ? `${endpoint}&api_token=${apiToken}&shop_domain=${shopDomain}`
      : `${endpoint}?api_token=${apiToken}&shop_domain=${shopDomain}`;

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      Platform.OS === 'web' ? '' : endpoint,
      {...typedDataVariables, ...contextInputParam},
      Platform.OS === 'web' && options
        ? {
            ...options,
            headers: {
              'x-base-url': apiUrl + endpoint,
            },
          }
        : options,
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },

  resolveCredentialConfigs: function (credentials: IJudgeMeCredentials): Partial<JudgeMeConfigType> | boolean {
    const {apiBaseUrl, shopDomain, apiToken} = credentials;
    if (!apiBaseUrl || !shopDomain || !apiToken) return false;
    return {
      apiBaseUrl,
      shopDomain,
      apiToken,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'shopDomain', 'apiToken'];
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: JudgeMeEditors,
});
