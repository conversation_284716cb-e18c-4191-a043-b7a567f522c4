import _ from 'lodash';

interface Product {
  product: {
    title: string;
    handle: string;
    path: string;
    excluded: boolean;
    vendor: string;
    in_store: boolean;
    product_type: string;
    description: string;
    tags: string[];
    image_url: string;
    medium_image_url: string;
    small_image_url: string;
    id: number;
    external_id: number;
    mpns: string[];
    barcodes: string[];
    skus: string[];
    lowest_price: number;
    highest_price: number;
  };
}

interface ListReviews {
  current_page: number;
  per_page: number;
  reviews: Review[];
}

interface Review {
  id: number;
  title: string;
  body: string;
  rating: number;
  product_external_id: number;
  product_title: string;
  product_handle: string;
  reviewer: Reviewer;
  source: string;
  curated: string;
  hidden: boolean;
  verified: string;
  featured: boolean;
  created_at: Date;
  updated_at: Date;
  ip_address: string;
  has_published_pictures: boolean;
  has_published_videos: boolean;
  pictures: Picture[];
}

interface Picture {
  hidden: boolean;
  urls: Urls;
}

interface Urls {
  small: string;
  compact: string;
  huge: string;
  original: string;
}

interface Reviewer {
  id: number;
  email: string;
  name: string;
  phone: string;
  tags: string[];
  accepts_marketing: boolean;
  unsubscribed_at: Date;
  external_id: number;
}

interface ReviewCount {
  count: number;
}

export const transformGetProductData = (data: Product) => {
  return {data: _.pick(data.product, ['id', 'external_id', 'handle'])};
};

export const transformListReviewsData = (data: ListReviews) => {
  const {reviews, current_page} = data;
  const hasNextPage = !(reviews.length === 0);
  const paginationMeta = {after: current_page + 1};

  return {
    data: reviews,
    hasNextPage,
    paginationMeta,
  };
};

export const transformGetReviewCount = (data: ReviewCount) => {
  const {count} = data;
  return {
    data: {
      count,
    },
  };
};
