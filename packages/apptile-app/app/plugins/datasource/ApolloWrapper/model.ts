import {ApolloClient, createHttpLink, DocumentNode, InMemoryCache} from '@apollo/client';
import {ContextSetter, setContext} from '@apollo/client/link/context';
import CacheManager from './cacheManager';

export type ApolloQueryConfig = {};

export default function apolloQueryRunner() {
  let apolloClient: ApolloClient<any> = null;
  let cacheManager = new CacheManager();

  return {
    initClient: async function (apiUrl: string, contextFn: ContextSetter) {
      const httpLink = createHttpLink({
        uri: apiUrl,
      });

      const cache = new InMemoryCache({addTypename: true});
      cacheManager.initApolloCache(cache);

      if (contextFn) {
        const contextLink = setContext(contextFn);
        apolloClient = new ApolloClient({
          link: contextLink.concat(httpLink),
          cache,
        });
      } else {
        apolloClient = new ApolloClient({
          link: httpLink,
          cache,
        });
      }
    },
    runQuery: async function (
      queryType: 'query' | 'mutation',
      gqlTag: DocumentNode,
      inputVariables: any,
      options?: any,
    ) {
      let query = null;
      cacheManager.checkCacheEviction(apolloClient);
      switch (queryType) {
        case 'mutation':
          return apolloClient.mutate({
            mutation: gqlTag,
            variables: inputVariables,
            context: options?.headers ? options.headers : null,
          });
        case 'query':
        default:
          return apolloClient.query({
            query: gqlTag,
            variables: inputVariables,
            context: options?.headers ? options.headers : null,
            fetchPolicy: options?.cachePolicy ? options?.cachePolicy : 'no-cache',
          });
      }
    },
  };
}
