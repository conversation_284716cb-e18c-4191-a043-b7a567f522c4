import {ApolloCache, ApolloClient} from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AsyncStorageWrapper, CachePersistor, LocalStorageWrapper, PersistentStorage} from 'apollo3-cache-persist';
import {Platform} from 'react-native';

const CACHE_PERSIST_TTL = 1000 * 60 * 20; // 20 minutes
const SHOPIFY_DS_CACHE_SIZE = 2 * 1024 * 1024; // 2MB
const APPTILE_CACHE_PERSIST_KEY = 'graphql-cache';

export default class CacheManager {
  lastPurge: any = null;
  persistor: CachePersistor<any> | null = null;

  constructor() {
    this.lastPurge = Date.now();
    this.persistor = null;
  }

  checkCacheEviction(apolloClient: ApolloClient<any>) {
    try {
      if (!this.lastPurge || this.lastPurge < Date.now() - CACHE_PERSIST_TTL) {
        this.lastPurge = Date.now();
        logger.info(`Cache Purged as PERSIST_TTL ${CACHE_PERSIST_TTL} exceeded.`);
        apolloClient.cache.reset();
        this.persistor?.purge();
      }
    } catch (error) {
      logger.error(`[CHECK_CACHE_EVICTION] failed due to ${error}`);
    }
  }

  initApolloCache(cache: ApolloCache<any>) {
    try {
      const storage = Platform.select<PersistentStorage<string>>({
        native: new AsyncStorageWrapper(AsyncStorage),
        default: new LocalStorageWrapper(window.localStorage),
      });
      // Check storage availabilty and reset on client initiate
      storage.removeItem(APPTILE_CACHE_PERSIST_KEY);
      this.persistor = new CachePersistor({
        cache,
        storage: storage,
        maxSize: SHOPIFY_DS_CACHE_SIZE,
        key: APPTILE_CACHE_PERSIST_KEY,
      });
    } catch (error) {
      logger.error(`[LOCAL_STORAGE_UNAVAILABLE] due to ${error} InMemoryCache will be used as fallback.`);
    }
  }
}
