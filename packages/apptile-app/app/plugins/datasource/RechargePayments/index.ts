import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IRechargePaymentsCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformerFunction,
  TransformChargeList,
  TransformSubscriptionList,
  TransformChargeData,
  TransformSubscriptionData,
  TransformChargeAction,
  TransformSubscriptionAction,
  TransformRetentionStartegies,
  TransformPlans,
} from './transformer';

export interface RechargePaymentsPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  rechargeVersion: string;
  appId: string;
  queryRunner: any;
}

type IEditableParams = Record<string, any>;

type RechargePaymentsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseRechargeQuerySpec: Partial<RechargePaymentsQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
    rechargeVersion: '',
    appId: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return {};
  },
  transformer: (data: any) => {
    return data;
  },
};

const rechargePaymentsApiRecords: Record<string, RechargePaymentsQueryDetails> = {
  getAllCharges: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/charges/',
    editableInputParams: {
      customerAccessToken: '',
      scheduledAtMin: '',
      scheduledAtMax: '',
      sortByField: 'scheduled_at',
      reverse: false,
      customerId: '',
      limit: 10,
    },
    isPaginated: true,
    paginationMeta: {
      page: 1,
    },
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {page} = paginationMeta;
      return {...inputVariables, page};
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {scheduledAtMin, scheduledAtMax, sortByField, reverse, customerId, limit, page} = inputVariables ?? {};
      const sort_by = sortByField ? `${sortByField}-${reverse ? 'desc' : 'asc'}` : undefined;

      const queryParams = _.omitBy(
        {
          scheduled_at_min: scheduledAtMin,
          scheduled_at_max: scheduledAtMax,
          customer_id: customerId,
          limit,
          sort_by,
        },
        _.isNil,
      );
      queryParams.page = page || 1;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformChargeList,
  },
  getAllSubscriptions: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/subscriptions/',
    editableInputParams: {
      customerAccessToken: '',
      status: '',
      limit: 10,
    },
    isPaginated: true,
    paginationMeta: {
      page: 1,
    },
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {page} = paginationMeta;
      return {...inputVariables, page};
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {status, page} = inputVariables ?? {};
      const resolvedEndpoint =
        status === 'all'
          ? `${endpoint}${jsonToQueryString({page: page || 1})}`
          : `${endpoint}${jsonToQueryString({status, page: page || 1})}`;
      return resolvedEndpoint;
    },
    transformer: TransformSubscriptionList,
  },
  getSubscription: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/subscriptions/',
    editableInputParams: {
      subscriptionId: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId} = inputVariables ?? {};
      return `${endpoint}${subscriptionId}/`;
    },
    transformer: TransformSubscriptionData,
  },
  getCharge: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/charges/',
    editableInputParams: {
      chargeId: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {chargeId} = inputVariables ?? {};
      return `${endpoint}${chargeId}/`;
    },
    transformer: TransformChargeData,
  },
  cancelSubscription: {
    ...baseRechargeQuerySpec,
    queryType: 'post',
    endpoint: '/subscriptions/',
    editableInputParams: {
      subscriptionId: '',
      cancellationReason: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId} = inputVariables ?? {};
      return `${endpoint}${subscriptionId}/cancel`;
    },
    inputResolver: inputVariables => {
      const {cancellationReason} = inputVariables;
      return {cancellation_reason: cancellationReason};
    },
    transformer: TransformSubscriptionAction,
  },
  activateSubscription: {
    ...baseRechargeQuerySpec,
    queryType: 'post',
    endpoint: '/subscriptions/',
    editableInputParams: {
      subscriptionId: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId} = inputVariables ?? {};
      return `${endpoint}${subscriptionId}/activate`;
    },
    transformer: TransformSubscriptionAction,
  },
  skipSubscriptionCharge: {
    ...baseRechargeQuerySpec,
    queryType: 'post',
    endpoint: '/charges/',
    editableInputParams: {
      chargeId: '',
      purchaseItemIds: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {chargeId} = inputVariables ?? {};
      return `${endpoint}${chargeId}/skip`;
    },
    inputResolver: inputVariables => {
      const {purchaseItemIds} = inputVariables;
      return {purchase_item_ids: purchaseItemIds};
    },
    transformer: TransformChargeAction,
  },
  unSkipSubscriptionCharge: {
    ...baseRechargeQuerySpec,
    queryType: 'post',
    endpoint: '/charges/',
    editableInputParams: {
      chargeId: '',
      purchaseItemIds: '',
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {chargeId} = inputVariables ?? {};
      return `${endpoint}${chargeId}/unskip`;
    },
    inputResolver: inputVariables => {
      const {purchaseItemIds} = inputVariables;
      return {purchase_item_ids: purchaseItemIds};
    },
    transformer: TransformChargeAction,
  },
  getAllRetentionStrategies: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/retention_strategies',
    editableInputParams: {
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      return `${endpoint}`;
    },
    transformer: TransformRetentionStartegies,
  },
  getPlans: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/plans',
    editableInputParams: {
      customerAccessToken: '',
      page: 1,
      limit: 50,
      productId: '',
      type: 'subscription',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {page, limit, productId, type} = inputVariables;
      const queryParams = _.omitBy(
        {
          page,
          limit,
          productId,
          type,
        },
        _.isNil,
      );
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformPlans,
  },
  getPlansPublicApi: {
    ...baseRechargeQuerySpec,
    queryType: 'get',
    endpoint: '/public-api/plans',
    editableInputParams: {
      page: 1,
      limit: 50,
      productId: '',
      type: 'onetime',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {page, limit, productId, type} = inputVariables;
      const queryParams = _.omitBy(
        {
          page,
          limit,
          productId,
          type,
        },
        _.isNil,
      );
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformPlans,
  },
};
const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'rechargePayments',
  type: 'datasource',
  name: 'Recharge Payments',
  description: 'Recharge Payments integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const rechargePaymentsEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'rechargeVersion',
      props: {
        label: 'Recharge Version',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'Payment Recharge',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io',
    appId: '',
    rechargeVersion: '2021-11',
    queryRunner: 'queryRunner',
  } as RechargePaymentsPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<RechargePaymentsPluginConfigType>,
    dsModelValues: any,
  ) => {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'X-Shopify-App-Id': dsConfig.config?.get('appId') ?? '',
          'Content-Type': 'application/json',
        },
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return rechargePaymentsApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      rechargePaymentsApiRecords && rechargePaymentsApiRecords[queryName]
        ? rechargePaymentsApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IRechargePaymentsCredentials,
  ): Partial<RechargePaymentsPluginConfigType> | boolean {
    const {proxyUrl, apiVersion, appId} = credentials;
    if (!proxyUrl || !apiVersion || !appId) return false;
    return {
      apiBaseUrl: proxyUrl,
      appId: appId,
      rechargeVersion: apiVersion,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'appId', 'rechargeVersion'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'rechargePayments';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = rechargePaymentsApiRecords[queryName];
      if (!queryDetails) return;
      let {endpointResolver, endpoint} = queryDetails ?? {};

      let typedInputVariables, typedDataVariables;
      if (queryDetails && queryDetails.editableInputParams) {
        typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      }

      const {getNextPage, paginationMeta} = options;
      logger.info('isPaginated', queryDetails?.isPaginated, getNextPage);
      logger.info('isPaginated', paginationMeta || queryDetails.paginationMeta);
      if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
        typedInputVariables = queryDetails.paginationResolver(
          typedInputVariables,
          paginationMeta || queryDetails.paginationMeta,
        );
      }

      endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables);

      typedDataVariables = queryDetails.inputResolver
        ? queryDetails.inputResolver(typedInputVariables)
        : typedInputVariables;

      const queryRunner = dsModelValues.get('queryRunner');
      const queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint,
        {...typedDataVariables},
        {
          headers: {
            'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken ?? '',
          },
        },
      );

      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = rawData;
      let queryHasNextPage, paginationDetails;
      if (queryDetails && queryDetails.transformer) {
        const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, typedInputVariables);

        transformedData = data;
        queryHasNextPage = hasNextPage;
        paginationDetails = pageData;
      }

      return yield {
        rawData,
        data: transformedData,
        hasNextPage: queryHasNextPage,
        paginationMeta: paginationDetails,
        errors: [],
        hasError: false,
      };
    } catch (error) {
      logger.error(error);
      return yield {
        rawData: {},
        data: {},
        hasNextPage: false,
        paginationMeta: {},
        errors: [error],
        hasError: false,
      };
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: rechargePaymentsEditors,
});
