import _ from 'lodash';
import {
  IApptileCharge,
  IApptileSubscription,
  IApptileChargeAction,
  IApptileSubscriptionAction,
  IApptileRetentionStrategy,
} from './types';

export type TransformerFunction = (
  data: any,
  paginationMeta?: any,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

// //Transformations related to subscriptions
// export const TransformDeliveries = (data: any) => {
//   let {deliveries} = data;
//   deliveries = deliveries.map(e => {
//     return {...e.orders[0], date: e.date};
//   });
//   return deliveries;
// };
// //Transformations related to subscriptions
// export const TransformCharges = (data: any) => {
//   const rawCharges = data?.charges;
//   const charges = [];
//   if (!rawCharges) return {};
//   rawCharges.forEach(e => {
//     const charge = {...e};
//     delete charge.line_items;
//     e.line_items.forEach(el => {
//       charge.line_item = el;
//       charges.push(charge);
//     });
//   });
//   logger.info(charges);
//   return charges;
// };
// export const TransformCharge = (data: any) => {
//   const rawCharge = data?.charge;
//   const charge = {...rawCharge};
//   if (!rawCharge) return {};
//   delete charge.line_items;
//   charge.line_items = {};
//   rawCharge.line_items.forEach(e => {
//     charge.line_items[e.purchase_item_id] = e;
//   });
//   return charge;
// };

export const TransformCharge = (charge: any): IApptileCharge => {
  const {line_items, ...restCharge} = charge;
  const chargeItem = _.pick(restCharge, ['id', 'status', 'scheduled_at', 'type', 'total_price', 'currency']);

  return {
    ...chargeItem,
    line_items:
      line_items && line_items.length > 0
        ? line_items.map((lineItem: any) => {
            return _.pick(lineItem, [
              'purchase_item_id',
              'handle',
              'images',
              'quantity',
              'sku',
              'title',
              'variant_title',
              'total_price',
              'unit_price',
            ]);
          })
        : [],
  };
};

export const TransformSubscription = (subscription: any): IApptileSubscription => {
  return _.pick(subscription, [
    'id',
    'address_id',
    'customer_id',
    'cancelled_at',
    'charge_interval_frequency',
    'expire_after_specific_number_of_charges',
    'has_queued_charges',
    'is_prepaid',
    'is_skippable',
    'is_swappable',
    'max_retries_reached',
    'next_charge_scheduled_at',
    'order_interval_frequency',
    'order_interval_unit',
    'presentment_currency',
    'price',
    'product_title',
    'quantity',
    'sku',
    'status',
    'variant_title',
  ]);
};

export const TransformChargeList = (
  data: any,
  inputVariables: any,
): {data: Array<IApptileCharge>; hasNextPage?: boolean; paginationMeta?: any} => {
  const chargesResponse = _.get(data, 'charges', []);

  const charges: Array<IApptileCharge> = [];
  chargesResponse.forEach((element: IApptileCharge) => {
    const charge = _.merge({}, element);
    element.line_items.forEach(lineItem => {
      charge.line_items = [lineItem];
      charges.push(TransformCharge(charge));
    });
  });

  const {page} = inputVariables;
  return {
    data: charges,
    hasNextPage: !!data.next_cursor,
    paginationMeta: {page: parseInt(page || '1', 10) + 1},
  };
};

export const TransformSubscriptionList = (
  data: any,
  inputVariables: any,
): {data: Array<IApptileSubscription>; hasNextPage?: boolean; paginationMeta?: any} => {
  const subscriptionResponse = _.get(data, 'subscriptions', []);
  const subscriptions = subscriptionResponse.map((subscription: any) => TransformSubscription(subscription));
  const {page} = inputVariables;
  return {
    data: subscriptions,
    hasNextPage: !!data.next_cursor,
    paginationMeta: {page: parseInt(page || '1', 10) + 1},
  };
};

export const TransformChargeData = (data: any): {data: IApptileCharge} => {
  return {data: {...TransformCharge(data.charge), shipping_address: data.charge.shipping_address}};
};

export const TransformSubscriptionData = (data: any): {data: IApptileSubscription} => {
  return {data: TransformSubscription(data.subscription)};
};

export const TransformChargeAction = (data: any): {data: IApptileChargeAction} => {
  return {
    data: {id: data?.charge.id, address_id: data?.charge.address_id, customer_id: data?.charge?.customer?.id},
  };
};

export const TransformRetentionStartegies = (data: any): {data: IApptileRetentionStrategy[]} => {
  return {data: data.retention_strategies};
};

export const TransformSubscriptionAction = (data: any): {data: IApptileSubscriptionAction} => {
  return {data: _.pick(data.subscription, ['id', 'address_id', 'customer_id'])};
};

export const TransformPlans = (data: any) => {
  const plans = data.plans.map((e: any) => {
    return (e = {
      id: e?.id,
      discount_amount: e?.discount_amount,
      discount_type: e?.discount_type,
      external_plan_id: e?.external_plan_id,
      external_plan_name: e?.external_plan_name,
      sort: e?.sort_order,
      type: e?.type,
    });
  });
  return {data: plans};
};
