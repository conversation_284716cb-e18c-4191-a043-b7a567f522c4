export interface IRechargeGetCharges {
  next_cursor: string;
  previous_cursor: any;
  charges: Array<{
    id: number;
    address_id: number;
    analytics_data: {
      utm_params: any[];
    };
    billing_address: {
      address1: string;
      address2?: string;
      city: string;
      company: any;
      country_code: string;
      first_name: string;
      last_name: string;
      phone: any;
      province: string;
      zip: string;
    };
    client_details: {
      browser_ip: any;
      user_agent: any;
    };
    created_at: string;
    currency: string;
    customer: {
      id: number;
      email: string;
      external_customer_id: {
        ecommerce: string;
      };
      hash: string;
    };
    discounts: any[];
    error: any;
    error_type: any;
    external_order_id: {
      ecommerce: any;
    };
    external_transaction_id: {
      payment_processor: any;
    };
    has_uncommitted_changes: boolean;
    line_items: Array<{
      purchase_item_id: number;
      external_product_id: {
        ecommerce: string;
      };
      external_variant_id: {
        ecommerce: string;
      };
      grams: number;
      handle: any;
      images: {
        large: string;
        medium: string;
        original: string;
        small: string;
      };
      properties: any[];
      purchase_item_type: string;
      quantity: number;
      sku: string;
      tax_due: string;
      tax_lines: any[];
      taxable: boolean;
      taxable_amount: string;
      title: string;
      total_price: string;
      unit_price: string;
      unit_price_includes_tax: boolean;
      variant_title: string;
    }>;
    note: any;
    order_attributes: any[];
    orders_count: number;
    payment_processor: string;
    processed_at: any;
    retry_date: any;
    scheduled_at: string;
    shipping_address: {
      address1: string;
      address2?: string;
      city: string;
      company: any;
      country_code: string;
      first_name: string;
      last_name: string;
      phone: any;
      province: string;
      zip: string;
    };
    shipping_lines: Array<{
      code: string;
      price: string;
      source: string;
      tax_lines: any[];
      taxable: boolean;
      title: string;
    }>;
    status: string;
    subtotal_price: string;
    tags: string;
    tax_lines: any[];
    taxable: boolean;
    total_discounts: string;
    total_duties?: string;
    total_line_items_price: string;
    total_price: string;
    total_refunds: string;
    total_tax: string;
    total_weight_grams: number;
    type: string;
    updated_at: string;
  }>;
}

export interface IRechargeGetSubscriptions {
  next_cursor: any;
  previous_cursor: any;
  subscriptions: Array<{
    id: number;
    address_id: number;
    customer_id: number;
    analytics_data: {
      utm_params: any[];
    };
    cancellation_reason: string;
    cancellation_reason_comments: any;
    cancelled_at: string;
    charge_interval_frequency: number;
    created_at: string;
    expire_after_specific_number_of_charges: any;
    external_product_id: {
      ecommerce: string;
    };
    external_variant_id: {
      ecommerce: string;
    };
    has_queued_charges: boolean;
    is_prepaid: boolean;
    is_skippable: boolean;
    is_swappable: boolean;
    max_retries_reached: boolean;
    next_charge_scheduled_at: any;
    order_day_of_month: any;
    order_day_of_week: any;
    order_interval_frequency: number;
    order_interval_unit: string;
    presentment_currency: string;
    price: string;
    product_title: string;
    properties: any[];
    quantity: number;
    sku: string;
    sku_override: boolean;
    status: string;
    updated_at: string;
    variant_title: string;
  }>;
}

// ============= Apptile Types =============

export interface IApptileCharge {
  id: number;
  status: string;
  scheduled_at: string;
  type: string;
  total_price: string;
  currency: string;
  shipping_address?: {
    address1: string;
    address2: string;
    city: string;
    company: string;
    country_code: string;
    first_name: string;
    last_name: string;
    phone: string;
    province: string;
    zip: string;
  };
  line_items: Array<{
    purchase_item_id: number;
    handle: any;
    images: {
      large: string;
      medium: string;
      original: string;
      small: string;
    };
    quantity: number;
    sku: string;
    title: string;
    total_price: string;
    unit_price: string;
    unit_price_includes_tax: boolean;
    variant_title: string;
  }>;
}

export interface IApptileSubscription {
  id: number;
  address_id: number;
  customer_id: number;
  cancelled_at: string;
  charge_interval_frequency: number;
  expire_after_specific_number_of_charges: any;
  has_queued_charges: boolean;
  is_prepaid: boolean;
  is_skippable: boolean;
  is_swappable: boolean;
  max_retries_reached: boolean;
  next_charge_scheduled_at: any;
  order_interval_frequency: number;
  order_interval_unit: string;
  presentment_currency: string;
  price: string;
  product_title: string;
  quantity: number;
  sku: string;
  status: string;
  variant_title: string;
}

export interface IApptileChargeAction {
  id: number;
  address_id: number;
  customer_id: number;
}

export interface IApptileRetentionStrategy {
  id: number;
  cancellation_flow_type: string;
  incentive_type: string;
  discount_code: string;
  prevention_text: string;
  reason: string;
  created_at: string;
  updated_at: string;
}
export interface IApptileSubscriptionAction {
  id: number;
  address_id: number;
  customer_id: number;
}
