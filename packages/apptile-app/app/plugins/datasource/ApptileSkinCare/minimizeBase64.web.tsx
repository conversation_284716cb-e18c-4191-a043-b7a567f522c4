import imageCompression from 'browser-image-compression';

export const minimizeBase64 = async (value: string) => {
  const file = await imageCompression.getFilefromDataUrl(value, 'base64');
  const compressedFile = await imageCompression(file, {
    maxSizeMB: 10,
    useWebWorker: true,
    maxWidthOrHeight: 1080,
    fileType: 'image/png',
  });
  const base64String = await imageCompression.getDataUrlFromFile(compressedFile);
  return base64String;
};
