import {call, put} from 'redux-saga/effects';
import {modelUpdateAction} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import _ from 'lodash';

import {ApptileSkinCareApiRecords} from '../';

export function* initSkinCareGenerator(dsConfig: PluginConfigType<any>, queryRunner: any) {
  const getConfigQueryDetails = ApptileSkinCareApiRecords['GetConfig'];

  // ! Check will it update in every model update

  try {
    const queryResponse = yield call(
      queryRunner.runQuery,
      getConfigQueryDetails.queryType,
      getConfigQueryDetails.endpoint,
      {},
    );

    const suggestionConfig = _.get(queryResponse, ['data', 'config'], {});

    if (!_.isEmpty(suggestionConfig)) {
      yield put(
        modelUpdateAction([
          {
            selector: [dsConfig.get('id'), 'suggestionConfig'],
            newValue: suggestionConfig,
          },
        ]),
      );
    }
  } catch (err) {
    logger.error('Skincare suggestion config fetch failed');
  }
}
