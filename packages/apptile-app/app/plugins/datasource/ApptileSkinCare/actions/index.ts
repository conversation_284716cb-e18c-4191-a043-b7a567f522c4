import {LocalStorage, PluginModelType, modelUpdateAction, navigateToScreen} from 'apptile-core';
import {Selector} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import _ from 'lodash';
import moment from 'moment';

export interface RecommendProductsByTagsActionParams {
  configObj: object;
  metric: string;
  score: number;
  navigateTo: string;
}

export async function recoomendProductsByTags(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: RecommendProductsByTagsActionParams,
) {
  const {configObj, metric, score, navigateTo} = params;
  let queryString = '';
  // console.log(configObj);
  // Find the specified metric in the rule set
  const metricRules = configObj.config.ruleSet[metric.toLowerCase()];
  if (!metricRules) {
    dispatch(navigateToScreen(navigateTo, {query: 'tag:""', metric: metric, score: score}));
    logger.error(`Error: Metric "${metric}" not found.`);
  } else {
    // Determine the range based on the score
    let range;
    if (score >= 80 && score <= 100) {
      range = 'good';
    } else if (score >= 50 && score < 80) {
      range = 'average';
    } else if (score >= 0 && score < 50) {
      range = 'bad';
    }

    // Select expressions based on the determined range or default range if not found
    const selectedRange = metricRules.range[range] || metricRules.default;
    const expressions = selectedRange.expressions;

    // Construct the query string
    for (const expression of expressions) {
      queryString += `${expression.type}:${expression.value} ${selectedRange.expMethod} `;
    }

    // Remove the trailing 'AND' or 'OR' from the end of the query string
    queryString = queryString.trim(); // Remove leading/trailing whitespace
    queryString = queryString.slice(0, -(selectedRange.expMethod.length + 1)); // Remove method and extra space

    if (queryString) dispatch(navigateToScreen(navigateTo, {query: queryString, metric: metric, score: score}));
  }
}

export const ALLOWED_SCANS_PER_USER: number = 2;
export const SCANS_LIMIT_RESET_INTERVAL: 'day' | 'week' | 'month' = 'day';
const getIdFromShopifyGid = (gid: string) => String(gid.split('/').pop());

export async function checkScanLimitExhausted(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: Record<string, any>,
) {
  dispatch(
    modelUpdateAction([
      {
        selector: selector.concat(['loading']),
        newValue: true,
      },
    ]),
  );

  const userShopifyGid = getIdFromShopifyGid(params.userId);
  const lastFaceScanTimeStamp = await LocalStorage.getValue(`lastFaceScanTimeStamp-${userShopifyGid}`);
  const faceScanCount = Number((await LocalStorage.getValue(`faceScanCount-${userShopifyGid}`)) || 0);

  if (!_.isEmpty(lastFaceScanTimeStamp)) {
    const lastFaceScanTimeStampMoment = moment(lastFaceScanTimeStamp).local();

    if (
      lastFaceScanTimeStampMoment.isSame(moment().local(), SCANS_LIMIT_RESET_INTERVAL) &&
      faceScanCount >= ALLOWED_SCANS_PER_USER
    ) {
      return dispatch(
        modelUpdateAction([
          {
            selector: selector.concat(['limitExhausted']),
            newValue: true,
          },
          {
            selector: selector.concat(['loading']),
            newValue: false,
          },
        ]),
      );
    }
  }
  return dispatch(
    modelUpdateAction([
      {
        selector: selector.concat(['limitExhausted']),
        newValue: false,
      },
      {
        selector: selector.concat(['loading']),
        newValue: false,
      },
    ]),
  );
}

export async function updateScanLimitData(
  dispatch,
  config: PluginConfigType<any>,
  model: PluginModelType,
  selector: Selector,
  params: Record<string, any>,
) {
  const userShopifyGid = getIdFromShopifyGid(params.userId);
  const [lastFaceScanTimeStamp, faceScanCount] = await Promise.all([
    LocalStorage.getValue(`lastFaceScanTimeStamp-${userShopifyGid}`),
    LocalStorage.getValue(`faceScanCount-${userShopifyGid}`),
  ]);

  const newFaceScanCount = getUpdatedFaceScanCount(
    lastFaceScanTimeStamp as string | null,
    faceScanCount as string | null,
  );

  await Promise.all([
    LocalStorage.setValue(`lastFaceScanTimeStamp-${userShopifyGid}`, moment().local().toISOString()),
    LocalStorage.setValue(`faceScanCount-${userShopifyGid}`, newFaceScanCount.toString()),
  ]);

  checkScanLimitExhausted(dispatch, config, model, selector, params);
}

function getUpdatedFaceScanCount(lastFaceScanTimeStamp: string | null, faceScanCount: string | null) {
  if (!lastFaceScanTimeStamp) return 1;

  const lastFaceScanTimeStampMoment = moment(lastFaceScanTimeStamp).local();
  return lastFaceScanTimeStampMoment.isSame(moment().local(), SCANS_LIMIT_RESET_INTERVAL)
    ? Number(faceScanCount) + 1
    : 1;
}
