export type TransformerFunction = (data: any) => {
  data: any;
  rawData?: any;
  hasError?: boolean;
  errors?: [];
  hasNextPage?: boolean;
  paginationMeta?: any;
};

export type IApptileSkinCareListResultSets = {
  totalItems: number;
  items: {
    id: string;
    subjectId: string;
    batchId: string;
    imageId: string;
    imageUrl: string;
    userId: string;
    profileId: string;
    isProcessed: boolean;
    rawResults: RawResult[];
    smoothedResults: SmoothedResult[];
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
  }[];
  totalPages: number;
  currentPage: number;
};

type RawResult = {
  /** Format: uuid */
  id: string;
  /** @description algorithm result data */
  result: {
    [key: string]: unknown | undefined;
  };
  /** @description algorithm result error status */
  is_ok: boolean;
  /**
   * Format: uuid
   * @description algorithm result image id
   */
  image_id: string;
  /** @description algorithm result algo version */
  algorithm_version_id: number;
  /** Format: date-time */
  creation_time: string;
  /** Format: uuid */
  application_id: string;
  application_name: string;
  application_description: string;
  /** Format: uuid */
  application_run_id?: string | null;
  image_type: string;
  algorithm_family_tech_name: string;
};

type SmoothedResult = {
  algorithm_name: string;
  algorithm_tech_name: string;
  value: number;
};

export const TransformListResultSets = (data: IApptileSkinCareListResultSets) => {
  return {
    data: data.items,
  };
};
