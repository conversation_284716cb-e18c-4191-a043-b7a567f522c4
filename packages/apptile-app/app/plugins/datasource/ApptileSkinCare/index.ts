import Immutable from 'immutable';
import {call, select, spawn} from 'redux-saga/effects';
import {PluginConfigType, TriggerActionIdentifier} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  GetRegisteredConfig,
  selectPluginConfig,
} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {DatasourcePluginConfig, IApptileSkinCareCredentials, IntegrationPlatformType} from '../datasourceTypes';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {TransformListResultSets} from './transformers';
import {minimizeBase64} from './minimizeBase64';
// import {initSkinCareGenerator} from './generator';
import {checkScanLimitExhausted, recoomendProductsByTags, updateScanLimitData} from './actions';

export type ApptileSkinCareConfigType = DatasourcePluginConfig &
  IApptileSkinCareCredentials & {
    queryRunner: any;
    loading: boolean;
    suggestionConfig: any;
    limitExhausted: boolean;
    recommendByTags: any;
    checkScanLimitExhausted: any;
    updateScanLimitData: any;
  };

type ApptileSkinCareQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: TransformerFunction;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

export type TransformerFunction = (data: any) => any;

export const ApptileSkinCareApiRecords: Record<string, ApptileSkinCareQueryDetails> = {
  UploadImage: {
    queryType: 'post',
    endpoint: '/v1/analyze/faceSkinMetrics/:userId/upload',
    endpointResolver: inputParams =>
      `/v1/analyze/faceSkinMetrics/upload?userId=${inputParams.userId}&userName=${inputParams.userName}`,
    editableInputParams: {
      userId: '',
      userName: '',
      b64data: '',
    },
    inputResolver: async inputVariables => {
      const {b64data} = inputVariables;
      // const compressedBase64 = await minimizeBase64(b64data);
      return {
        b64data: b64data,
      };
    },
  },
  ListResultSets: {
    queryType: 'get',
    endpoint: '/v1/analyze/faceSkinMetrics/:userId',
    endpointResolver: (inputParams, paginationMeta) => {
      const {nextPageCursor = 0} = paginationMeta ?? {};
      return `/v1/analyze/faceSkinMetrics/${inputParams?.userId}?size=${inputParams?.limit}&page=${nextPageCursor}`;
    },
    editableInputParams: {
      userId: '',
      limit: 10,
    },
    isPaginated: true,
    transformer: TransformListResultSets,
  },
  GetResult: {
    queryType: 'get',
    endpoint: '/v1/analyze/faceSkinMetrics/:userId/resultSet/:resultSetId/smoothed',
    endpointResolver: inputParams =>
      `/v1/analyze/faceSkinMetrics/${inputParams?.userId}/resultSet/${inputParams?.resultSetId}/smoothed`,
    editableInputParams: {
      userId: '',
      resultSetId: '',
    },
  },
  GetConfig: {
    queryType: 'get',
    endpoint: '/v1/store/config',
  },
};

const propertySettings: PluginPropertySettings = {
  recommendByTags: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return recoomendProductsByTags;
    },
    actionMetadata: {
      editableInputParams: {
        configObj: '',
        metric: '',
        score: '',
        navigateTo: '',
      },
    },
  },
  checkScanLimitExhausted: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkScanLimitExhausted;
    },
    actionMetadata: {
      editableInputParams: {
        userId: '',
      },
    },
  },
  updateScanLimitData: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return updateScanLimitData;
    },
    actionMetadata: {
      editableInputParams: {
        userId: '',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'apptileSkinCare',
  type: 'datasource',
  name: 'Apptile skin care',
  description: 'Skin analysis using Haut AI',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptileSkinCareEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiAccessKey',
      props: {
        label: 'API Key',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<ApptileSkinCareConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ApptileSkinCareConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'apptileSkinCare',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: '',
    apiAccessKey: '',
    limitExhausted: false,
    loading: false,
    queryRunner: 'queryrunner',
    suggestionConfig: '',
    recommendByTags: 'actions',
    checkScanLimitExhausted: 'actions',
    updateScanLimitData: 'actions',
  } as ApptileSkinCareConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ApptileSkinCareConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      const accessToken = dsConfig.config.get('apiAccessKey');
      if (accessToken) {
        config.headers['X-API-KEY'] = `${accessToken}`;
      }
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ApptileSkinCareApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      ApptileSkinCareApiRecords && ApptileSkinCareApiRecords[queryName] ? ApptileSkinCareApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = ApptileSkinCareApiRecords[queryName];

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? yield call(queryDetails.inputResolver, typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) endpoint = queryDetails.endpointResolver(typedInputVariables, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables, ...contextInputParam},
      options,
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data} = queryDetails.transformer(rawData);
      const {after: currentPage = 1} = paginationMeta ?? {};

      transformedData = data;
      queryHasNextPage = !(rawData.totalPages === rawData.currentPage + 1);

      if (queryHasNextPage)
        paginationDetails = {after: currentPage ? currentPage + 1 : 1, nextPageCursor: rawData.currentPage + 1};
    }

    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'apptileSkinCare';
  },

  resolveCredentialConfigs: function (
    credentials: IApptileSkinCareCredentials,
  ): Partial<ApptileSkinCareConfigType> | boolean {
    const {apiBaseUrl, apiAccessKey} = credentials;
    if (!apiBaseUrl || !apiAccessKey) return false;
    return {
      apiBaseUrl,
      apiAccessKey,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'apiAccessKey'];
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptileSkinCareEditors,
});
