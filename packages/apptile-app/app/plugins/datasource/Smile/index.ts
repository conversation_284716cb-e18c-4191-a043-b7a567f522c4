import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, ISmileCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {PluginConfigType, PluginModelType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings} from 'apptile-core';

export type SmileConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  appId: string;
  apiBaseUrl: string;
};

type SmileQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

const SmileApiRecords: Record<string, SmileQueryDetails> = {
  fetchCustomer: {
    queryType: 'get',
    endpoint: '/api/customer',
    editableInputParams: {
      email: '',
    },
    endpointResolver: (endpoint, inputParams) => {
      let email = '';
      if (inputParams?.email) email = inputParams.email;
      return `${endpoint}?email=${email}`;
    },
  },
  fetchCustomerTransactions: {
    queryType: 'get',
    endpoint: '/api/points_transactions',
    editableInputParams: {
      customerId: '',
    },
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}?customer_id=${inputParams?.customerId}`;
    },
  },
  fetchWaysToRedeem: {
    queryType: 'get',
    endpoint: '/api/points_products',
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'smile',
  type: 'datasource',
  name: 'Smile',
  description: 'Reward Tracker for Smile.io',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const SmileEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://api.apptile.io/smile',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'app Id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<SmileConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, SmileConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'smile',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io/smile-proxy',
    appId: '',
    queryRunner: 'queryrunner',
  } as SmileConfigType,

  resolveCredentialConfigs: function (credentials: ISmileCredentials): Partial<SmileConfigType> | boolean {
    const {apiBaseUrl, appId} = credentials;
    return {
      appId,
      apiBaseUrl: apiBaseUrl ? apiBaseUrl : 'https://api.apptile.io/smile-proxy',
    };
  },

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<SmileConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    let apiBaseUrl = dsModelValues.get('apiBaseUrl');

    if (apiBaseUrl.endsWith('/')) {
      apiBaseUrl = apiBaseUrl.slice(0, -1);
    }

    const appId = dsConfig.config?.get('appId');

    queryRunner.initClient(apiBaseUrl, config => {
      config.headers = {
        'x-shopify-app-id': appId,
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return SmileApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = SmileApiRecords && SmileApiRecords[queryName] ? SmileApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'smile';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = SmileApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: SmileEditors,
});
