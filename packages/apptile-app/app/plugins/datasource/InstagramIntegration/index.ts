import { call } from 'redux-saga/effects';
import { PluginPropertySettings, PluginListingSettings, AppPageTriggerOptions } from 'apptile-core';
import { DatasourceQueryDetail, DatasourceQueryReturnValue } from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import { baseDatasourceConfig } from '../base';
import wrapDatasourceModel from '../wrapDatasourceModel';
import { PluginConfigType } from 'apptile-core';
import { DatasourcePluginConfig } from '../datasourceTypes';

export type InstagramIntegrationConfigType = DatasourcePluginConfig & {
  queryRunner: any;
};

type InstagramMediaQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, appId: string, orgId: string, limit: number | undefined) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const tranformCarouselData = (data: any) => {
  const transformedCarousels = [] as any;

  data.forEach((d: any) => {
    if (d.media_type === 'CAROUSEL_ALBUM') {
      d.children.data.forEach((c: any) => {
        transformedCarousels.push({...d, ...c})
      })
    }
  });

  return transformedCarousels;
}

const InstagramMediaApiRecords: Record<string, InstagramMediaQueryDetails> = {
  getPosts: {
    queryType: 'get',
    endpoint: '/api/instagram/media/posts',
    endpointResolver: (endpoint:string,appId:string,orgId:string,limit:number|undefined) =>
      `${endpoint}?orgId=${orgId}&appId=${appId}${limit ? '&limit=' + limit : ''}`,
    editableInputParams: {
      limit: 0
    },
  },
  getReels: {
    queryType: 'get',
    endpoint: '/api/instagram/media/reels',
    endpointResolver: (endpoint:string,appId:string,orgId:string,limit:number|undefined) =>
      `${endpoint}?orgId=${orgId}&appId=${appId}${limit ? '&limit=' + limit : ''}`,
    editableInputParams: {
      limit: 0
    },
  },
  getCarousel: {
    queryType: 'get',
    endpoint: '/api/instagram/media/carousel',
    endpointResolver: (endpoint:string,appId:string,orgId:string,limit:number|undefined) =>
      `${endpoint}?orgId=${orgId}&appId=${appId}${limit ? '&limit=' + limit : ''}`,
    editableInputParams: {
      limit: 0
    },
    transformer: tranformCarouselData,
  }
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'instagramMedia',
  type: 'datasource',
  name: 'Instagram Media',
  description: 'Keep your users up to date with your latest Instagram Posts',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const instagramMediaEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'appId',
      defultValue: '',
      props: {
        label: 'App ID',
        placeholder: 'Apptile App Id',
      },
    },
    {
      type: 'codeInput',
      name: 'orgId',
      defultValue: '',
      props: {
        label: 'Org ID',
        placeholder: 'Organization Id',
      },
    },
    {
      type: 'codeInput',
      name: 'apptileServerBaseUrl',
      defultValue: '',
      props: {
        label: 'Apptile Server Base Url',
        placeholder: 'https://api.apptile.local',
      },
    },
  ]
}

export default wrapDatasourceModel({
  name: 'instagramMedia',
  config: {
    ...baseDatasourceConfig,
  },
  
  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<InstagramIntegrationConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    
    const apptileServerBaseUrl = dsModelValues.get('apptileServerBaseUrl');
    const requestUrl = `${apptileServerBaseUrl}/apptile-instagram-connector`;

    queryRunner.initClient(requestUrl, config => {
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return InstagramMediaApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = InstagramMediaApiRecords && InstagramMediaApiRecords[queryName] ? InstagramMediaApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = InstagramMediaApiRecords[queryName];

    const appId = dsModelValues.get('appId');
    const orgId = dsModelValues.get('orgId');
    
    const limit = parseInt(inputVariables.limit) || undefined

    let endpoint = queryDetails.endpointResolver?.(queryDetails.endpoint, appId, orgId, limit);

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint
    );
    const data = queryResponse.data;

    let transformedData;
    if (queryDetails.transformer) {
      transformedData = queryDetails.transformer(data);
    } else {
      transformedData = data;
    }
    
    return yield { rawData: data, data: transformedData, transformedHasError: false, transformedError: [], queryHasNextPage: false, paginationDetails: {} }
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: instagramMediaEditors,
});
