import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from '../../../common/datatypes/types';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IJoyLoyaltyCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';

import {TransformedListGeneratedCupons} from './transformers';
export type JoyLoyaltyConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  appId: string;
  apiBaseUrl: string;
  localProxyUrl: string;
  proxyUrl: string;
  joyloyaltyappid: string;
  secretkey: string;
  joyLoyaltyCustomerId: string;
  points: string;
};

// Following snippet initiates the base for API calls
type JoyLoyaltyQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

// Following API Record snippet helps initializing all the API calls required, eg : chooseVoucher,listGeneratedCoupons,generateCouponCode
const JoyLoyaltyApiRecords: Record<string, JoyLoyaltyQueryDetails> = {
  // To get the customerId, Points and other customer related details.
  getCustomerDetail: {
    queryType: 'get',
    endpoint: '/customers?email={email}',
    endpointResolver: (endpoint, inputParams) => {
      // return endpoint.replace('{email}', inputParams?.customerEmail);
      return endpoint;
    },
    editableInputParams: {
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  // To get the list of ways to earn points per customer.
  waysToEarn: {
    queryType: 'get',
    endpoint: '/earning',
    endpointResolver: (endpoint, inputParams) => {
      // return endpoint.replace('{email}', inputParams?.customerEmail);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
      return endpoint;
    },
    editableInputParams: {},
    isPaginated: false,
  },
  // To get the list of trasactions for points.
  pointsHistory: {
    queryType: 'get',
    endpoint: '/activities?shopifyCustomerId={shopifyCustomerId}',
    endpointResolver: (endpoint, inputParams) => {
      // return endpoint.replace('{shopifyCustomerId}', inputParams?.shopifyCustomerId);
      return endpoint;
    },
    editableInputParams: {
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  // To get the list of ways to earn points per customer.
  waysToEarnByCustomer: {
    queryType: 'get',
    endpoint: '/earning-programs-by-customer?customerId={customerId}',
    endpointResolver: (endpoint, inputParams) => {
      // return endpoint.replace('{customerId}', inputParams?.customerId);
      return endpoint;
    },
    editableInputParams: {
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  // To redeem a coupon through points per customer.
  redeemCoupon: {
    queryType: 'post',
    endpoint: '/redeem',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    inputResolver: inputVariables => {
      return {
        programId: inputVariables.programId,
        redeemPoint: inputVariables.redeemPoint,
      };
    },
    editableInputParams: {
      programId: '',
      redeemPoint: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  awardSocialInteractionPoints: {
    queryType: 'post',
    endpoint: '/earn/interact-social',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    inputResolver: inputVariables => {
      return {
        shopifyCustomerId: inputVariables.shopifyCustomerId,
        earningId: inputVariables.earningId,
      };
    },
    editableInputParams: {
      shopifyCustomerId: '',
      earningId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  // To get a list of coupon available to redeem through points.
  listCoupons: {
    queryType: 'get',
    endpoint: '/spending',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    editableInputParams: {},
    isPaginated: false,
  },
  spendingofSpecificProgram: {
    queryType: 'get',
    endpoint: '/spending/{spendingProgramId}',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{spendingProgramId}', inputParams?.spendingProgramId);
    },
    editableInputParams: {
      spendingProgramId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  // To get redeemed coupouns List for the customer.
  rewards: {
    queryType: 'get',
    endpoint: '/rewards?customerId={customerId}',
    endpointResolver: (endpoint, inputParams) => {
      // return endpoint.replace('{customerId}', inputParams?.customerId);
      return endpoint;
    },
    isPaginated: false,
    editableInputParams: {
      shopifyCustomerAccessToken: '',
      before: '',
      after: '',
    },
  },
  specificRewardDetails: {
    queryType: 'get',
    endpoint: '/rewards/{customerRewardId}',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{customerRewardId}', inputParams?.customerRewardId);
    },
    editableInputParams: {
      customerRewardId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  listVipTiers: {
    queryType: 'get',
    endpoint: '/tiers',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint;
    },
    editableInputParams: {
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
};

// This function is used to show the datasource in the right panel
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'joyLoyalty',
  type: 'datasource',
  name: 'Joy Loyalty',
  description: 'Reward Tracker for JoyLoyalty',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

// This function is used to set the global inputs for the datasource to function.
export const JoyLoyaltyEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'proxyUrl',
        placeholder: 'https://api.apptile.io/joy-loyalty-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://dev-api.joy.so/rest_api/v1',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'app Id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<JoyLoyaltyConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, JoyLoyaltyConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

// This function is used to execute the query, and send headers.
export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        // Following code is used to send inpur variables of query in the header.
        {
          headers: {
            'x-shopify-customer-access-token': inputVariables?.shopifyCustomerAccessToken || null,
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

// This function is used to wrap the datasource model and list it in the datasouces
export default wrapDatasourceModel({
  name: 'joyLoyalty',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://dev-api.joy.so/rest_api/v1',
    localProxyUrl: 'https://api.apptile.local/joy-loyalty-proxy',
    proxyUrl: 'https://api.apptile.io/joy-loyalty-proxy',
    appId: '',
    joyLoyaltyCustomerId: '',
    points: '',
    queryRunner: 'queryrunner',
  } as JoyLoyaltyConfigType,

  resolveCredentialConfigs: function (credentials: IJoyLoyaltyCredentials): Partial<JoyLoyaltyConfigType> | boolean {
    const {apiBaseUrl, joyLoyaltyAppId, secretKey, appId} = credentials;
    if (!(apiBaseUrl && joyLoyaltyAppId && secretKey)) return false;

    return {
      appId: appId,
      apiBaseUrl: apiBaseUrl,
      joyloyaltyappid: joyLoyaltyAppId,
      secretkey: secretKey,
    };
  },

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<JoyLoyaltyConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    // const apiBaseUrl = dsModelValues.get('apiBaseUrl'); // Get apiBaseUrl from config
    let proxyUrl = dsModelValues.get('proxyUrl'); // Get proxyUrl from config

    if (proxyUrl.endsWith('/')) {
      proxyUrl = proxyUrl.slice(0, -1);
    }

    const appId = dsConfig.config?.get('appId'); // Use appId from config

    queryRunner.initClient(proxyUrl, config => {
      config.headers = {
        'x-shopify-app-id': appId ?? '',
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return JoyLoyaltyApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      JoyLoyaltyApiRecords && JoyLoyaltyApiRecords[queryName] ? JoyLoyaltyApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'joyLoyalty';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = JoyLoyaltyApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: JoyLoyaltyEditors,
});
