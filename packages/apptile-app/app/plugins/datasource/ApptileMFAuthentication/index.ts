import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {DatasourcePluginConfig, IApptileMFAuthCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import authActions from './actions/authActions';

export const ApptileMFAuthenticationDsName = 'apptileMFAuthentication';
export interface ApptileMFAuthPluginConfigType extends DatasourcePluginConfig {
  apiBasePath: string;
  sourceEntityId: string;
  sourcePlatform: string;
  queryRunner: any;
  phone: string;
  email: string;
  maskedEmail: string;
  SignIn: string;
  SignUp: string;
  OtpVerify: string;
  PasswordVerify: string;
}

type ApptileMFAuthQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: Record<string, any>;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseApptileMFAuthQuerySpec: Partial<ApptileMFAuthQueryDetails> = {
  isPaginated: false,
  contextInputParams: {},
  transformer: data => {
    return {data, hasNextPage: false, paginationMeta: {}};
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};

export const ApptileMFAuthenticationApiRecords: Record<string, Partial<ApptileMFAuthQueryDetails>> = {
  SignIn: {
    ...baseApptileMFAuthQuerySpec,
    queryType: 'post',
    endpoint: '/api/v1/sign-in/phone',
    endpointResolver: endpoint => {
      return endpoint;
    },

    inputResolver: (inputVariables, inputParams) => {
      const {sourcePlatform, sourceEntityId} = inputParams;
      const {phone} = inputVariables;
      return {
        phone: phone,
        sourcePlatform: sourcePlatform,
        sourceEntityId: sourceEntityId,
      };
    },
    editableInputParams: {
      phone: '',
    },
    contextInputParams: {
      sourcePlatform: '',
      sourceEntityId: '',
    },
  },
  SignUp: {
    ...baseApptileMFAuthQuerySpec,
    queryType: 'post',
    endpoint: '/api/v1/sign-in/register',
    endpointResolver: endpoint => {
      return endpoint;
    },
    inputResolver: (inputVariables, inputParams) => {
      const {phone, sourcePlatform, sourceEntityId} = inputParams;
      const {email} = inputVariables;
      return {
        phone: phone,
        email: email,
        sourcePlatform: sourcePlatform,
        sourceEntityId: sourceEntityId,
      };
    },
    editableInputParams: {
      email: '',
    },
    contextInputParams: {
      phone: '',
      sourcePlatform: '',
      sourceEntityId: '',
    },
  },
  OtpVerify: {
    ...baseApptileMFAuthQuerySpec,
    queryType: 'post',
    endpoint: '/api/v1/sign-in/otp/verify',
    endpointResolver: endpoint => {
      return endpoint;
    },

    inputResolver: (inputVariables, inputParams) => {
      const {sourcePlatform, sourceEntityId, phone} = inputParams;
      const {otp} = inputVariables;
      return {
        phone: phone,
        otp: otp,
        sourcePlatform: sourcePlatform,
        sourceEntityId: sourceEntityId,
      };
    },
    editableInputParams: {
      otp: '',
    },
    contextInputParams: {
      sourcePlatform: '',
      sourceEntityId: '',
      phone: '',
    },
  },
  PasswordVerify: {
    ...baseApptileMFAuthQuerySpec,
    queryType: 'post',
    endpoint: '/api/v1/sign-in/password/verify',
    endpointResolver: endpoint => {
      return endpoint;
    },

    inputResolver: (inputVariables, inputParams) => {
      const {phone, email, sourcePlatform, sourceEntityId} = inputParams;
      const {password} = inputVariables;
      return {
        phone: phone,
        email: email,
        password: password,
        sourcePlatform: sourcePlatform,
        sourceEntityId: sourceEntityId,
      };
    },
    editableInputParams: {
      password: '',
    },
    contextInputParams: {
      sourcePlatform: '',
      sourceEntityId: '',
      phone: '',
      email: '',
    },
  },
};

const propertySettings: PluginPropertySettings = {
  SignIn: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.SignIn;
    },
    actionMetadata: {
      editableInputParams: {
        phone: '',
      },
    },
  },
  SignUp: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.SignUp;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
      },
    },
  },
  OtpVerify: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.OtpVerify;
    },
    actionMetadata: {
      editableInputParams: {
        otp: '',
      },
    },
  },
  PasswordVerify: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return authActions.PasswordVerify;
    },
    actionMetadata: {
      editableInputParams: {
        password: '',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: ApptileMFAuthenticationDsName,
  type: 'datasource',
  name: 'Apptile MF Authentication Integration',
  description: 'Apptile MF Authentication Integration.',
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptileMFAuthEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBasePath',
      props: {
        label: 'API Base Path',
        placeholder: 'https://dev-api.apptile.io/auth-manager',
      },
    },
    {
      type: 'codeInput',
      name: 'sourceEntityId',
      props: {
        label: 'Source EntityId',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'sourcePlatform',
      props: {
        label: 'Source Platform',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<ApptileMFAuthPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ApptileMFAuthPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export async function executeQuery(
  dsModel: PluginModelType,
  dsConfig: PluginConfigType<any>,
  dsModelValues: PluginModelType,
  queryName: string,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) {
  const queryDetails = ApptileMFAuthenticationApiRecords[queryName];

  if (!queryDetails) return;

  const {getNextPage, paginationMeta} = options ?? {};

  let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
  const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
  const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

  let typedInputVariables, typedDataVariables;
  if (queryDetails && queryDetails.editableInputParams) {
    typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
  }
  if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
    typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
  }

  typedDataVariables = queryDetails.inputResolver
    ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
    : typedInputVariables;

  endpoint = endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, getNextPage);

  const queryRunner = dsModelValues.get('queryRunner');
  const queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, typedDataVariables, {
    ...options,
    headers: {...queryDetails.headers},
  });

  const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
  let transformedData = rawData;
  let queryHasNextPage, paginationDetails;
  if (queryDetails && queryDetails.transformer) {
    const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

    transformedData = data;
    queryHasNextPage = hasNextPage;
    paginationDetails = pageData;
  }

  return {
    rawData,
    data: transformedData,
    hasNextPage: queryHasNextPage,
    paginationMeta: paginationDetails,
    errors: [],
    hasError: false,
  };
  // } catch (error: any) {
  //   const tError = error?.toJSON() || error;
  //   return {
  //     rawData: null,
  //     data: null,
  //     hasNextPage: false,
  //     paginationMeta: false,
  //     errors: [tError],
  //     hasError: false,
  //   };
  // }
}

export default wrapDatasourceModel({
  name: ApptileMFAuthenticationDsName,
  config: {
    apiBasePath: 'https://dev-api.apptile.io/auth-manager',
    sourceEntityId: '',
    sourcePlatform: 'SHOPIFY',
    queryRunner: 'queryrunner',
    phone: '',
    email: '',
    maskedEmail: '',
    SignIn: '',
    SignUp: '',
    OtpVerify: '',
    PasswordVerify: '',
  } as ApptileMFAuthPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<ApptileMFAuthPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBasePath'), config => {
      // added request timeout
      config.timeout = 40000;
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, any> {
    return _.filter(
      _.map(propertySettings, (action, label) => {
        return action.type === TriggerActionIdentifier ? label : '';
      }),
    );
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = propertySettings && propertySettings[queryName] ? propertySettings[queryName] : null;
    return queryDetails?.actionMetadata?.editableInputParams ? queryDetails?.actionMetadata?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IApptileMFAuthCredentials,
  ): Partial<ApptileMFAuthPluginConfigType> | boolean {
    const {proxyUrl, sourceEntityId} = credentials;
    if (!(proxyUrl && sourceEntityId)) return false;
    return {
      apiBasePath: proxyUrl,
      sourceEntityId: sourceEntityId,
      sourcePlatform: 'SHOPIFY',
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBasePath', 'sourceEntityId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return ApptileMFAuthenticationDsName;
  },

  runQuery: function* (
    dsModel: PluginModelType,
    dsConfig: PluginConfigType<any>,
    dsModelValues: PluginModelType,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryName, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptileMFAuthEditors,
});
