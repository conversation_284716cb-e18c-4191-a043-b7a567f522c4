import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {DatasourcePluginConfig, IOmegaEstimateCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';

export interface OmegaEstimatePluginConfigType
  extends DatasourcePluginConfig,
    IRewardActionsDatasourcePluginConfigType {
  apiBaseUrl: string;
  shopUrl: string;
  // serverApiBaseUrl: string;
  // storeApiAuthenticationKey: string;
  // appId: string;
  apiKey: string;
  queryRunner: any;
  headers: Record<string, any>;
}

type IEditableParams = Record<string, any>;

type OmegaEstimateQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any, contextInputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => string;
  apiBaseUrlResolver: (dsModel: any) => string;
  editableInputParams: IEditableParams;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseOmegaEstimateQuerySpec: Partial<OmegaEstimateQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    // apiBaseUrl: '',
    // storeApiAuthenticationKey: '',
  },
  endpointResolver: (endpoint, inputParams, getNextPage) => {
    return endpoint;
  },
  apiBaseUrlResolver: (dsModel: any) => {
    return dsModel.get('apiBaseUrl');
  },
  transformer: data => {
    return {data, hasNextPage: false, paginationMeta: {}};
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
  inputResolver: (inputVariables: any) => {
    return inputVariables;
  },
  queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
    return {};
  },
};

export const OmegaEstimateApiRecords: Record<string, Partial<OmegaEstimateQueryDetails>> = {
  FetchShippingMethods: {
    ...baseOmegaEstimateQuerySpec,
    queryType: 'get',
    endpoint: '/s/api/public-endpoint/shop-metafield',
    editableInputParams: {},
    contextInputParams: {
      shopUrl: '',
      apiKey: '',
    },
    transformer: (data: any) => {
      const shippingMethods = data.data.app?.shippingMethods ?? [];
      return {data: shippingMethods};
    },
    endpointResolver: (endpoint, inputVariables, contextInputVariables) => {
      const {shopUrl, apiKey} = contextInputVariables;
      const resolvedEndpoint = `${endpoint}?shop=${encodeURIComponent(shopUrl)}&key=${encodeURIComponent(apiKey)}`;
      return resolvedEndpoint;
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'OmegaEstimate',
  type: 'datasource',
  name: 'OmegaEstimate',
  description: 'OmegaEstimate Delivery Estimate Integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const OmegaEstimateEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://estimated.omegatheme.com',
      },
    },
    {
      type: 'codeInput',
      name: 'shopUrl',
      props: {
        label: 'Shopify Shop Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiKey',
      props: {
        label: 'API KEY',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<OmegaEstimatePluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, OmegaEstimatePluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: OmegaEstimateQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    let {endpointResolver, contextInputParams, editableInputParams, endpoint} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables, contextInputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const apiBaseUrl = queryDetails.apiBaseUrlResolver(dsModelValues);

    let headers = {};
    if (queryDetails.queryHeadersResolver)
      headers = queryDetails.queryHeadersResolver(inputVariables, contextInputVariables);

    let queryRunner = AjaxQueryRunner();

    queryRunner.initClient(apiBaseUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          ...headers,
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    // const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables},
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, null, dsModelValues);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex: any) {
    const errors = ex?.response?.data?.errors || [ex?.message];
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: errors,
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'OmegaEstimate',
  config: {
    apiBaseUrl: 'https://estimated.omegatheme.com',
    queryRunner: 'queryrunner',
  } as OmegaEstimatePluginConfigType,

  // initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<OmegaEstimatePluginConfigType>, dsModelValues: any) {
  //   const queryRunner = AjaxQueryRunner();
  //   queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
  //     const storeApiAuthenticationKey = dsModelValues?.get('storeApiAuthenticationKey') ?? {};
  //     config.headers = {...config.headers, ...{'x-api-key': storeApiAuthenticationKey}};
  //     return config;
  //   });
  //   return {
  //     modelUpdates: [
  //       {
  //         selector: [dsConfig.get('id'), 'queryRunner'],
  //         newValue: queryRunner,
  //       },
  //     ],
  //   };
  // },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return OmegaEstimateApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      OmegaEstimateApiRecords && OmegaEstimateApiRecords[queryName] ? OmegaEstimateApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IOmegaEstimateCredentials,
  ): Partial<OmegaEstimatePluginConfigType> | boolean {
    const {apiBaseUrl} = credentials;
    if (!apiBaseUrl) return false;
    return {
      apiBaseUrl: apiBaseUrl,
    };
  },
  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {},

  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'omegaEstimate';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = OmegaEstimateApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: OmegaEstimateEditors,
});
