import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {AxiosResponse} from 'axios';
import {modelUpdateAction} from 'apptile-core';
import {navigateToScreen} from 'apptile-core';
import _ from 'lodash';
import {Platform} from 'react-native';

export interface ShopFloCheckoutActionPayload {
  storeDomain: string;
  storeFrontAccessToken: string;
  customerAccessToken: string;
  currentCheckout: any;
  discounts: any[];
}

export interface ShopFloCheckoutActionPayloadV2 {
  storeDomain: string;
  storeFrontAccessToken: string;
  customerAccessToken: string;
  currentCart: any;
  discounts: any[];
}

export const initCheckoutWithToken = async (
  dispatch: any,
  config: PluginConfig,
  model: any,
  selector: Selector,
  params: any,
) => {
  const payload = params as ShopFloCheckoutActionPayload;
  const checkoutScreen = model?.get('shopfloCheckoutScreenId');
  const appId = model?.get('appId');

  const queryRunner = model?.get('queryRunner');
  const {storeFrontAccessToken, customerAccessToken, currentCheckout, storeDomain, discounts} = payload;
  const checkoutLines = currentCheckout?.lines;
  if (_.isArray(checkoutLines) && checkoutLines.length) {
    const orderTags = {tags: ['apptile-mobile-app', 'apptile-' + Platform.OS]};

    const tokenResponse: AxiosResponse<any, any> = await queryRunner.runQuery(
      'post',
      '/',
      {
        checkoutId: currentCheckout.checkoutId,
        discounts: discounts,
        ...orderTags,
      },
      {
        headers: {
          'x-shopify-app-id': appId ?? '',
          'x-shopify-customer-access-token': customerAccessToken ?? '',
          'x-shopify-storefront-access-token': storeFrontAccessToken ?? '',
          'x-shopify-store-name': storeDomain ?? '',
        },
      },
    );
    if (tokenResponse.data?.error) {
    } else {
      const tokenId = tokenResponse?.data?.response?.token_id;
      const checkoutUrl = tokenResponse?.data?.response?.checkout_url;
      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['tokenId']),
        newValue: tokenId,
      });
      modelUpdates.push({
        selector: selector.concat(['checkoutUrl']),
        newValue: checkoutUrl,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
      dispatch(navigateToScreen(checkoutScreen, {sfToken: tokenId}));
    }
  } else return;
};

export const initCheckoutWithTokenV2 = async (
  dispatch: any,
  config: PluginConfig,
  model: any,
  selector: Selector,
  params: any,
) => {
  const payload = params as ShopFloCheckoutActionPayloadV2;
  const checkoutScreen = model?.get('shopfloCheckoutScreenId');
  const appId = model?.get('appId');

  const queryRunner = model?.get('queryRunner');
  const {storeFrontAccessToken, customerAccessToken, currentCart, storeDomain, discounts} = payload;
  const checkoutLines = currentCart?.lines;
  if (_.isArray(checkoutLines) && checkoutLines.length) {
    const orderTags = {tags: ['apptile-mobile-app', 'apptile-' + Platform.OS]};

    const tokenResponse: AxiosResponse<any, any> = await queryRunner.runQuery(
      'post',
      '/v2/',
      {
        cartId: currentCart.id,
        discounts: discounts,
        ...orderTags,
      },
      {
        headers: {
          'x-shopify-app-id': appId ?? '',
          'x-shopify-customer-access-token': customerAccessToken ?? '',
          'x-shopify-storefront-access-token': storeFrontAccessToken ?? '',
          'x-shopify-store-name': storeDomain ?? '',
        },
      },
    );
    if (tokenResponse.data?.error) {
    } else {
      const tokenId = tokenResponse?.data?.response?.token_id;
      const checkoutUrl = tokenResponse?.data?.response?.checkout_url;
      const modelUpdates: ModelChange[] = [];
      modelUpdates.push({
        selector: selector.concat(['tokenId']),
        newValue: tokenId,
      });
      modelUpdates.push({
        selector: selector.concat(['checkoutUrl']),
        newValue: checkoutUrl,
      });
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
      dispatch(navigateToScreen(checkoutScreen, {sfToken: tokenId}));
    }
  } else return;
};
