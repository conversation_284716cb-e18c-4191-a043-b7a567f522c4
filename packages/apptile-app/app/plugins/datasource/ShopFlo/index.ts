import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IShopFloCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {initCheckoutWithToken, initCheckoutWithTokenV2} from './actions/ShopFloActions';

export type ShopFloPluginConfigType = DatasourcePluginConfig &
  Partial<IShopFloCredentials> & {
    shopfloCheckoutScreenId: string;
  };

export const ShopFloApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  initiateCheckout: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return initCheckoutWithToken;
    },
    actionMetadata: {
      editableInputParams: {
        storeDomain: '',
        storeFrontAccessToken: '',
        customerAccessToken: '',
        currentCheckout: {},
        discounts: {},
      },
    },
  },
  initiateCheckoutV2: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return initCheckoutWithTokenV2;
    },
    actionMetadata: {
      editableInputParams: {
        storeDomain: '',
        storeFrontAccessToken: '',
        customerAccessToken: '',
        currentCart: {},
        discounts: {},
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'ShopFlo',
  type: 'datasource',
  name: 'ShopFlo Integration',
  description: 'ShopFlo Checkout integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const ShopFloEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
      },
    },
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'Proxy URL',
      },
    },
    {
      type: 'screenSelector',
      name: 'shopfloCheckoutScreenId',
      props: {
        label: 'ShopFlo Checkout Screen',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'ShopFlo',
  config: {
    ...baseDatasourceConfig,
    appId: '',
    proxyUrl: '',
    tokenId: '',
    checkoutUrl: '',
    shopfloCheckoutScreenId: '',
    queryRunner: 'queryRunner',
    initiateCheckout: 'action',
    initiateCheckoutV2: 'action',
  } as ShopFloPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopFloPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('proxyUrl'));
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ShopFloApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = ShopFloApiRecords && ShopFloApiRecords[queryName] ? ShopFloApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'shopFlo';
  },

  resolveCredentialConfigs: function (
    credentials: ShopFloPluginConfigType,
  ): Partial<ShopFloPluginConfigType> | boolean {
    const {merchantId} = credentials;
    if (!merchantId) return false;
    return {
      merchantId,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['merchantId'];
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: ShopFloEditors,
});
