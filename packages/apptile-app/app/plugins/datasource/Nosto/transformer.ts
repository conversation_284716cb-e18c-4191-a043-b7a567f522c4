import _ from 'lodash';
import {INostoProductsResponse, INostoRandomRecommendations} from './types';

export const nostoProductTransformer = (product: INostoProductsResponse) => {
  //TODO: Write proper transformer when required.
  return product;
};

export const nostoSearchTransformer = (data: any) => {
  console.log("Products Data", data);
  const {products: productData} = data?.search ?? {};

  const products = productData.hits;
  const {from = 0, size, total} = productData;
  const hasNextPage = from + size < total ? true : false;

  // Get Handle From Url

  products.forEach((product: any) => {
    const productSplit = product.url.split('/');
    product['handle'] = productSplit[productSplit.length - 1];
  });

  let filters = productData.facets.reduce((result, facet) => {
    if (facet.name !== undefined) {
      const type = facet.type;
      if (type === 'terms') result[facet.name] = {field: facet.field, fieldKeys: facet.data, type};
      // else result[facet.name] = {field: facet.field, min: facet.min, max: facet.max, type};
    }
    return result;
  }, {});

  return {
    data: {products, filters},
    hasNextPage: hasNextPage,
    paginationMeta: from,
  };
};

export const nostoRandomRecommendations = (data: INostoRandomRecommendations) => {
  return {
    data: data.recos.random.primary,
    hasNextPage: false,
    paginationMeta: 0,
  };
};

export const productPageRecommendationsTransformer = (data: any) => {
  const transformedProducts = _.first(data?.updateSession?.pages?.forProductPage)?.primary?.map(
    (product: INostoProductsResponse) => nostoProductTransformer(product),
  );
  const resultId = _.first(data?.updateSession?.pages?.forProductPage)?.resultId;

  return {
    data: {products: transformedProducts, resultId},
    hasNextPage: false,
    paginationMeta: null,
  };
};
export const searchPageRecommendationsTransformer = (data: any) => {
  const transformedProducts = _.first(data?.updateSession?.pages?.forSearchPage)?.primary?.map(
    (product: INostoProductsResponse) => nostoProductTransformer(product),
  );
  const resultId = _.first(data?.updateSession?.pages?.forSearchPage)?.resultId;

  return {
    data: {products: transformedProducts, resultId},
    hasNextPage: false,
    paginationMeta: null,
  };
};

export const cartPageRecommendationsTransformer = (data: any) => {
  const transformedProducts = _.first(data?.updateSession?.pages?.forCartPage)?.primary?.map(
    (product: INostoProductsResponse) => nostoProductTransformer(product),
  );
  const resultId = _.first(data?.updateSession?.pages?.forCartPage)?.resultId;

  return {
    data: {products: transformedProducts, resultId},
    hasNextPage: false,
    paginationMeta: null,
  };
};
