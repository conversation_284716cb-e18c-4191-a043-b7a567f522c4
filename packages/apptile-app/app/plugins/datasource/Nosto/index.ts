import wrapDatasourceModel from '../wrapDatasourceModel';
import {PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import apolloQueryRunner from '../ApolloWrapper/model';
import {AppPageTriggerOptions, GetRegisteredConfig, PluginListingSettings} from 'apptile-core';
import {FetchRandomRecommendations, SearchGQL} from './queries/search';
import {call, put, select, spawn} from 'redux-saga/effects';
import {
  cartPageRecommendationsTransformer,
  nostoRandomRecommendations,
  nostoSearchTransformer,
  productPageRecommendationsTransformer,
  searchPageRecommendationsTransformer,
} from './transformer';
import nostoActions from './actions/nostoActions';
import {Buffer} from 'buffer';
import {
  cartPageRecommendations,
  createCustomerSession,
  orderConversionTrackerMutation,
  productPageRecommendations,
  searchPageRecommendations,
} from './queries/recommendations';
import {selectPluginConfig} from 'apptile-core';
import {LocalStorage} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import {DatasourcePluginConfig, INostoCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';

export enum nostoQueryTypeEnum {
  SEARCH = 'SEARCH',
  RECOMMENDATIONS = 'RECOMMENDATIONS',
}

export type NostoPluginConfigType = DatasourcePluginConfig &
  INostoCredentials & {
    recommendationsQueryRunner: any;
    searchQueryRunner: any;
    selectedFilters: any;
    setFilters: any;
  };

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'nosto',
  type: 'datasource',
  name: 'Nosto',
  description: 'Nosto Product and Search Filter',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

const propertySettings: PluginPropertySettings = {
  setFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return nostoActions.setFilters;
    },
    actionMetadata: {
      editableInputParams: {filterName: '', value: ''},
    },
  },
};

const NostoApiRecords = {
  Search: {
    queryType: 'query',
    nostoQueryType: nostoQueryTypeEnum.SEARCH,
    gqlTag: SearchGQL,
    transformer: nostoSearchTransformer,
    editableInputParams: {
      accountId: '',
      query: '',
      size: '',
      from: '',
      sortBy: '',
      sortOrder: '',
      filter: '{{[]}}',
      collectionId: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const size = inputVariables.products.size;

      inputVariables.products.from = Number.parseInt(paginationMeta + size);

      return {...inputVariables, from: Number.parseInt(paginationMeta + size)};
    },
    inputResolver: (inputVariables: any) => {
      const {sortBy, sortOrder, filter} = inputVariables;

      inputVariables.size = Number.parseInt(inputVariables.size);
      inputVariables.from = Number.parseInt(inputVariables.from ?? 0);

      const transformedInput = {
        ...inputVariables,
        products: {
          size: inputVariables.size,
          from: inputVariables.from,
          facets: ['*'],
          categoryId: inputVariables.collectionId,
        },
      };

      if (sortBy && sortOrder && sortBy.trim() !== '' && sortOrder.trim() !== '')
        transformedInput.products.sort = [{field: sortBy, order: sortOrder}];

      if (filter && Array.isArray(filter) && filter.length > 0) transformedInput.products.filter = filter;

      return transformedInput;
    },
  },

  CreateNewCustomerSession: {
    queryType: 'mutation',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: createCustomerSession,
  },

  ProductPageRecommendations: {
    queryType: 'mutation',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: productPageRecommendations,
    editableInputParams: {
      productId: '',
      customerId: '',
      slotId: '',
      ref: '', //It is the previous slot/result id.
    },
    transformer: productPageRecommendationsTransformer,
  },

  CartPageRecommendations: {
    queryType: 'mutation',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: cartPageRecommendations,
    editableInputParams: {
      customerId: '',
      targetUrl: '',
      slotId: '',
      cartItems: '',
    },
    inputResolver: (inputVariables: any) => {
      const {cartItems} = inputVariables;
      return {
        ...inputVariables,
        cartItems,
      };
    },
    transformer: cartPageRecommendationsTransformer,
  },

  SearchPageRecommendations: {
    queryType: 'mutation',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: searchPageRecommendations,
    editableInputParams: {
      searchTerm: '',
      customerId: '',
      slotId: '',
    },
    transformer: searchPageRecommendationsTransformer,
  },

  RandomRecommendations: {
    queryType: 'query',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: FetchRandomRecommendations,
    transformer: nostoRandomRecommendations,
    editableInputParams: {
      minProducts: 1,
      maxProducts: 10,
    },
    isPaginated: false,
  },

  OrderConversionTracker: {
    queryType: 'mutation',
    nostoQueryType: nostoQueryTypeEnum.RECOMMENDATIONS,
    gqlTag: orderConversionTrackerMutation,
    // transformer: nostoRandomRecommendations,
    editableInputParams: {
      customerId: '',
      customerInfo: '',
      orderNumber: '',
      orderStatus: '',
      ref: '',
      purchasedItems: '',
      paymentProvider: '',
    },
    isPaginated: false,
  },
};

export const NostoEditorConfig: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'accountId',
      defultValue: '',
      props: {
        label: 'Nosto Account Id',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiToken',
      defultValue: '',
      props: {
        label: 'Nosto api token',
        placeholder: '',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'Nosto',
  config: {
    apiBaseUrl: 'https://api.nosto.com/v1/graphql',
    searchBaseUrl: 'https://search.nosto.com/v1/graphql',
    searchQueryRunner: 'queryrunner',
    recommendationsQueryRunner: 'queryrunner',
    apiToken: '',
    accountId: '',
    setFilters: 'action',
    selectedFilters: '{{[]}}',
  } as NostoPluginConfigType,
  initDatasource: function* (dsModel: any, dsConfig: any, dsModelValues: any) {},

  resolveCredentialConfigs: function (credentials: INostoCredentials): Partial<NostoPluginConfigType> | boolean {
    const {apiBaseUrl, searchBaseUrl, apiToken, accountId} = credentials;
    if (!(apiBaseUrl && searchBaseUrl && apiToken && accountId)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      searchBaseUrl: searchBaseUrl,
      apiToken: apiToken,
      accountId: accountId,
    };
  },
  onPluginUpdate: function* (
    state: any,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    if (pageLoad) {
      // Spawn Two Appllo Query Runner because Search and Recommendations URL is different
      const searchQueryRunner = apolloQueryRunner();
      const recommendationsQueryRunner = apolloQueryRunner();
      const searchUrl = dsConfig.config?.get('searchBaseUrl');
      const baseUrl = dsConfig.config?.get('apiBaseUrl');
      const apiToken = dsConfig.config?.get('apiToken');
      //!!Context Function is passed Empty for now because of unsure hgeaders
      searchQueryRunner.initClient(searchUrl, () => ({}));
      recommendationsQueryRunner.initClient(baseUrl, () => ({
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Basic ' + Buffer.from(':' + apiToken).toString('base64'),
        },
      }));

      yield spawn(function* () {
        const queryDetails = NostoApiRecords.CreateNewCustomerSession;
        try {
          const nostoCustomerLocalStorageKey = 'NOSTO_CUSTOMER_ID';
          const existingCustomerId: string = yield call(LocalStorage.getValue, nostoCustomerLocalStorageKey);
          if (!existingCustomerId) {
            var {data: rawData, errors} = yield call(
              recommendationsQueryRunner.runQuery,
              queryDetails.queryType,
              queryDetails.gqlTag,
              {},
              {errorPolicy: 'all'},
            );
            LocalStorage.setValue(nostoCustomerLocalStorageKey, rawData?.newSession);
            yield put(
              modelUpdateAction([
                {
                  selector: [dsConfig.get('id'), 'nostoCustomerId'],
                  newValue: rawData?.newSession,
                },
              ]),
            );
          }
          if (existingCustomerId) {
            yield put(
              modelUpdateAction([
                {
                  selector: [dsConfig.get('id'), 'nostoCustomerId'],
                  newValue: existingCustomerId,
                },
              ]),
            );
          }
        } catch (error) {
          console.error(error);
        }
      });

      return {
        modelUpdates: [
          {
            selector: [dsConfig.get('id'), 'searchQueryRunner'],
            newValue: searchQueryRunner,
          },
          {
            selector: [dsConfig.get('id'), 'recommendationsQueryRunner'],
            newValue: recommendationsQueryRunner,
          },
        ],
      };
    }
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = NostoApiRecords[queryName];
    const {getNextPage, paginationMeta} = options ?? {};

    if (!queryDetails) return;

    const searchQueryRunner = dsModelValues.get('searchQueryRunner');
    const recommendationsQueryRunner = dsModelValues.get('recommendationsQueryRunner');
    let queryRunner;
    if (queryDetails.nostoQueryType === nostoQueryTypeEnum.RECOMMENDATIONS) queryRunner = recommendationsQueryRunner;
    if (queryDetails.nostoQueryType === nostoQueryTypeEnum.SEARCH) queryRunner = searchQueryRunner;

    let queryTag = '';

    if (queryDetails && queryDetails.inputResolver) {
      inputVariables = queryDetails.inputResolver(inputVariables);
    }

    if (typeof queryDetails.gqlTag === 'string' || typeof queryDetails.gqlTag === 'object') {
      queryTag = queryDetails.gqlTag;
    } else if (typeof queryDetails.gqlTag === 'function') {
      queryTag = queryDetails.gqlTag(inputVariables);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      inputVariables = queryDetails.paginationResolver(inputVariables, paginationMeta);
    }

    var {data: rawData, errors} = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      queryTag,
      {...inputVariables},
      {errorPolicy: 'all'},
    );

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);
      var transformedData = data;
      var queryHasNextPage = hasNextPage;
      var paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return NostoApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = NostoApiRecords && NostoApiRecords[queryName] ? NostoApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'nosto';
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: NostoEditorConfig,
});
