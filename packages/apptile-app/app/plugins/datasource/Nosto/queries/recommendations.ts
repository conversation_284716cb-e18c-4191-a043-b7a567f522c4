import gql from 'graphql-tag';
import {OrderPageConversionTrackerInput} from '../types';

export const createCustomerSession = gql`
  mutation {
    newSession(referer: "https://app.apptile.io")
  }
`;

const NOSTO_PRODUCT = `
    productId
    name
    ratingValue
    reviewCount
    imageUrl
    thumbUrl
    description
    availability
    price
    listPrice
    url
`;

export const productPageRecommendations = gql`
  mutation ($productId: String!, $customerId: String!, $slotId: [String!]!, $ref: String) {
    updateSession(by: BY_CID, id: $customerId, params: {event: {type: VIEWED_PRODUCT, target: $productId, ref: $ref}}) {
      pages {
        forProductPage(
          params: {isPreview: false, imageVersion: VERSION_8_400_400, slotIds: $slotId}
          product: $productId
        ) {
          divId
          resultId
          primary {
            ${NOSTO_PRODUCT}
          }
        }
      }
    }
  }
`;

export const cartPageRecommendations = gql`
  mutation updateSessionMutation(
    $customerId: String!
    $targetUrl: String!
    $slotId: [String!]!
    $cartItems: [InputItem]
  ) {
    updateSession(
      by: BY_CID
      id: $customerId
      params: {event: {type: VIEWED_PAGE, target: $targetUrl}, cart: {items: $cartItems}}
    ) {
      pages {
        forCartPage(params: {isPreview: false, imageVersion: VERSION_8_400_400, slotIds: $slotId}, value: 100) {
          divId
          resultId
          primary {
            ${NOSTO_PRODUCT}
          }
        }
      }
    }
  }
`;

export const searchPageRecommendations = gql`
  mutation ($customerId: String!, $searchTerm: String!, $slotId: [String!]!) {
    updateSession(by: BY_CID, id: $customerId, params: {event: {type: SEARCHED_FOR, target: $searchTerm}}) {
      pages {
        forSearchPage(
          params: {isPreview: false, imageVersion: VERSION_8_400_400, slotIds: $slotId}
          term: $searchTerm
        ) {
          divId
          resultId
          primary {
            ${NOSTO_PRODUCT}
          }
        }
      }
    }
  }
`;

export const orderConversionTrackerMutation = gql`
  mutation (
    $customerId: String!
    $orderNumber: String!
    $orderStatus: String!
    $paymentProvider: String
    $ref: String
    $purchasedItems: [InputItem!]!
    $customerInfo: InputCustomerInfoEntity
  ) {
    placeOrder(
      by: BY_CID
      id: $customerId
      params: {
        customer: $customerInfo
        order: {
          number: $orderNumber
          orderStatus: $orderStatus
          paymentProvider: $paymentProvider
          ref: $ref
          purchasedItems: $purchasedItems
        }
      }
    ) {
      id
    }
  }
`;
