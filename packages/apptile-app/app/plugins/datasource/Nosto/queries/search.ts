import gql from 'graphql-tag';

export const SearchGQL = gql`
  query Search($accountId: String!, $query: String!, $products: InputSearchProducts) {
    search(accountId: $accountId, query: $query, products: $products) {
      products {
        total
        from
        size
        hits {
          productId
          name
          ratingValue
          reviewCount
          imageUrl
          thumbUrl
          description
          availability
          price
          priceText
          priceCurrencyCode
          url
          customFields {
            key
            value
          }
          variantId
          realVariantIds
        }
        facets {
          ... on SearchTermsFacet {
            id
            field
            type
            name
            data {
              value
              count
              selected
            }
          }
        }
      }
    }
  }
`;

export const FetchTopListRecommendations = gql`
  query {
    recos(preview: false, image: VERSION_7_200_200) {
      toplist(hours: 168, sort: BUYS, params: {minProducts: 1, maxProducts: 10}) {
        primary {
          name
          productId
        }
      }
    }
  }
`;

//To fetch random recommendations
export const FetchRandomRecommendations = gql`
  query ($minProducts: Int!, $maxProducts: Int!) {
    recos(preview: false, image: VERSION_7_200_200) {
      random(params: {minProducts: $minProducts, maxProducts: $maxProducts}) {
        primary {
          name
          productId
        }
      }
    }
  }
`;

//to add recommendations to a product page, the productIds parameter would be a single-item array containing the product identifiers of the product that is being viewed.
export const FetchRelatedRecommendations = gql`
  query ($productId: String!) {
    recos(preview: false, image: VERSION_7_200_200) {
      related(relationship: VIEWED_TOGETHER, productIds: [$productId], params: {minProducts: 1, maxProducts: 10}) {
        primary {
          name
          productId
        }
      }
    }
  }
`;

//use this to add recommendations to a search page, the term parameter would be the entire search term as queries by the user.
export const FetchSearchRecommendations = gql`
  query ($searchTerm: String!) {
    recos(preview: false, image: VERSION_7_200_200) {
      search(term: $searchTerm, params: {minProducts: 1, maxProducts: 10}) {
        primary {
          name
          productId
        }
      }
    }
  }
`;

// ... on SearchStatsFacet {
//   id
//   field
//   type
//   name
//   min
//   max
// }
