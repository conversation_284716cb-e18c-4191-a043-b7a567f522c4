import {modelUpdateAction} from 'apptile-core';

class NostoActions {
  setFilters = async (dispatch: any, config: any, model: any, selector: any, params: any) => {
    const {filterName, value} = params;
    const selectedFilters = model.get('selectedFilters');

    const existingFilter = selectedFilters.find((filter: any) => filter.field === filterName);

    console.log(existingFilter, 'selectedFilters');
    if (existingFilter) existingFilter.value.push(value);
    else selectedFilters.push({field: filterName, value: [value]});

    console.log(selectedFilters, 'selectedFilters');

    const selectedFilterModelUpdates = [
      {
        selector: selector.concat(['selectedFilters']),
        newValue: [...selectedFilters],
      },
    ];

    dispatch(modelUpdateAction(selectedFilterModelUpdates, undefined, true));
  };

  removeFilters = async (dispatch: any, config: any, model: any, selector: any, params: any) => {
    const {filterName, value} = params;
    const selectedFilters = model.get('selectedFilters');

    const existingFilter = selectedFilters.find((filter: any) => filter.field === filterName);

    existingFilter.value = existingFilter.value.filter((filterValue: String) => filterValue === value);

    const filteredModelUpdates = [
      {
        selector: selector.concat(['selectedFilters']),
        newValue: [...selectedFilters],
      },
    ];

    dispatch(modelUpdateAction(filteredModelUpdates, undefined, true));
  };
}

const nostoActions = new NostoActions();
export default nostoActions;
