export interface INostoRandomRecommendations {
  recos: {
    random: {
      primary: Array<{
        name: string;
        productId: string;
      }>;
    };
  };
}

export interface INostoProductsResponse {
  productId: string;
  name: string;
  ratingValue: number;
  reviewCount: number;
  imageUrl: string;
  thumbUrl: string;
  description: string;
  availability: string;
  price: number; // Current selling price
  listPrice: number; // Price before discount
  url: string;
}

export interface INostoProducts {
  productId: string;
  name: string;
  ratingValue: number;
  reviewCount: number;
  imageUrl: string;
  thumbUrl: string;
  description: string;
  availability: string;
  price: number;
  listPrice: number;
  url: string;
}
