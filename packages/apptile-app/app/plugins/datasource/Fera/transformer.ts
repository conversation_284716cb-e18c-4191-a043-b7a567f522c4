import _ from 'lodash';

export type TransformerFunction = (
  data: any,
  paginationMeta?: any,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

interface IReview {
  id?: string;
  review_of?: string;
  is_verified?: boolean | null;
  shopify_product_id?: string | null;
  fera_product_id?: string | null;
  created_at?: string;
  rating?: number | null;
  heading?: string | null;
  body?: string | null;
  channel_code?: string;
  store_reply?: {} | null;
  counts?: {};
  product?: Partial<IProduct> | null;
  customer?: Partial<ICustomer> | null;
  media?: [];
}

interface IRating {
  review_of: string;
  average: number;
  count: number;
  verified_count: number;
  counts: [];
}

interface IProductRating {
  review_of: string;
  average: number;
  count: number;
  shopify_product_id?: string;
  fera_product_id?: string;
}

interface IProduct {
  id?: string;
  shopify_product_id?: string;
  name?: string;
  url?: string;
  handle?: string;
  thumbnail_image_url?: string;
  image_url?: string;
  rating?: Partial<IRating>;
  counts?: [];
  tags?: string[];
  variants?: [];
  price?: number;
  formatted_price?: string;
}

interface ICustomer {
  id?: string | null;
  shopify_customer_id?: string;
  display_name?: string;
  name?: string;
  display_location?: string | null;
  generated_display_name?: string;
  email?: string;
  is_anonymous?: boolean;
  avatar_url?: string;
  is_verified?: boolean;
  media?: any[];
}

const FeraReview = (data: any) => {
  return {
    id: data?.id,
    review_of: data?.subject,
    is_verified: data?.is_verified,
    shopify_product_id: data?.external_product_id,
    fera_product_id: data?.product_id,
    created_at: data?.created_at,
    rating: data?.rating,
    heading: data?.heading,
    body: data?.body,
    channel_code: data?.channel_code,
    store_reply: data?.store_reply,
    counts: data.counts,
    product: data?.product ? productObj(data?.product) : null,
    customer: data?.customer ? customerObj(data?.customer) : null,
    media: data?.media,
  };
};

const storeRating = (rating: any) => {
  return {
    review_of: rating?.subject,
    average: rating?.average,
    count: rating?.count,
    verified_count: rating?.verified_count,
    counts: rating?.counts,
  };
};

const ratingObj = (rating: any) => {
  return {
    average: rating?.average,
    count: rating?.count,
    counts: rating?.counts,
  };
};

const productObj = (product: any) => {
  return {
    id: product?.id,
    shopify_product_id: product?.external_id,
    name: product?.name,
    url: product?.url,
    handle: product?.handle,
    thumbnail_image_url: product?.thumbnail_url,
    image_url: product?.image_url,
    rating: ratingObj(product?.rating),
    counts: product?.counts,
    tags: product?.tags,
    variants: product?.variants,
    price: product?.price,
    formatted_price: product?.formatted_price,
  };
};

const customerObj = (customer: any) => {
  return {
    id: customer?.id,
    shopify_customer_id: customer?.external_id,
    display_name: customer?.display_name,
    name: customer?.name,
    display_location: customer?.display_location,
    generated_display_name: customer?.generated_display_name,
    email: customer?.email,
    is_anonymous: customer?.is_anonymous,
    avatar_url: customer?.avatar_url,
    is_verified: customer?.is_verified,
    media: [],
  };
};

export const TransformReviews = (response: any) => {
  const {data, meta} = response;

  let reviews: IReview[] = [];
  if (_.isArray(data) && data.length > 0) {
    reviews = data.map(review => FeraReview(review));
  }

  const totalPages = Math.ceil(meta?.total_count / reviews?.length);
  const totalReviews = meta?.total_count;
  const hasNextPage = totalPages !== totalReviews;
  const paginationMeta = {
    totalPages: totalPages,
    totalReviews: totalReviews,
    offset: meta?.offset,
  };
  return {data: reviews, hasNextPage, paginationMeta};
};

export const TransformStoreRating = (data: any) => {
  let response: IRating | {} = {};
  if (data) {
    response = storeRating(data);
  }
  return {data: response};
};

export const TransformProductReviews = (response: any) => {
  const {data, meta} = response;

  let reviews: IReview[] = [];
  if (_.isArray(data) && data.length > 0) {
    reviews = data.map(review => FeraReview(review));
  }
  const totalPages = Math.ceil(meta?.total_count / reviews?.length);
  const totalReviews = meta?.total_count;
  const hasNextPage = totalPages !== totalReviews;
  const paginationMeta = {
    totalPages: totalPages,
    totalReviews: totalReviews,
    offset: meta?.offset,
  };
  return {data: reviews, hasNextPage, paginationMeta};
};

export const TransformProductRating = (data: any) => {
  let response: IProductRating | {} = {};
  if (data) {
    response = {
      review_of: data?.subject,
      average: data?.average,
      count: data?.count,
      shopify_product_id: data?.external_product_id,
      fera_product_id: data?.product_id,
    };
  }
  return {data: response};
};

export const TransformSubmitReview = (data: any) => {
  const response = {
    id: data?.id,
    subject: data?.subject,
    is_verified: data?.is_verified,
    shopify_product_id: data?.external_product_id,
    fera_product_id: data?.product_id,
    shopify_customer_id: data?.external_customer_id,
    fera_customer_id: data?.customer_id,
    rating: data?.rating,
    heading: data?.heading,
    body: data?.body,
    is_anonymous: data?.is_anonymous,
    store_reply: data?.store_reply,
    state: data?.state,
    counts: data?.counts,
    original_source: data?.original_source,
    product: productObj(data?.product),
    customer: customerObj(data?.customer),
  };

  return {data: response};
};
