import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType, PluginModelType, TriggerActionIdentifier} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {TransformProductRating, TransformProductReviews, TransformReviews, TransformStoreRating, TransformSubmitReview} from './transformer';

export interface FeraPluginConfigType extends DatasourcePluginConfig {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
  apiVersion: string;
  apiBaseUrl: string;
}

type IEditableParams = Record<string, any>;

type FeraQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseFeraQuerySpec: Partial<FeraQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    proxyUrl: '',
    appId: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return inputVariables;
  },
  transformer: (data: any) => {
    return data;
  },
};

const feraApiRecords: Record<string, FeraQueryDetails> = {
  getAllReviews: {
    ...baseFeraQuerySpec,
    queryType: 'get',
    endpoint: '/reviews',
    editableInputParams: {
      sort_by: '',
      include_aggregate_rating: '',
      limit: '',
      subject: 'both or product or store',
    },
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const {sort_by, include_aggregate_rating, limit, subject} = inputVariables;
      let newOffset;
      if (paginationMeta) {
        newOffset = Number.parseInt(inputVariables.limit) + Number.parseInt(paginationMeta?.offset);
      } else {
        newOffset = 0;
      }
      const parseLimit = Number.parseInt(limit);
      return `${endpoint}?limit=${parseLimit}&sort_by=${sort_by}&offset=${newOffset}&include_aggregate_rating=${include_aggregate_rating}&subject=${subject}`;
    },
    transformer: TransformReviews,
    isPaginated: true,
  },
  getProductReviews: {
    ...baseFeraQuerySpec,
    queryType: 'get',
    endpoint: '/products',
    editableInputParams: {
      productId: '',
      include_aggregate_rating: '',
      limit: '',
      sort_by: '',
    },
    endpointResolver: (endpoint, inputVariables,paginationMeta) => {
      const { productId, include_aggregate_rating, limit,sort_by} = inputVariables;
      const parseLimit = Number.parseInt(limit);
      let newOffset;
      if (paginationMeta) {
        newOffset = Number.parseInt(inputVariables.limit) + Number.parseInt(paginationMeta?.offset);
      } else {
        newOffset = 0;
      }
      const resolvedEndpoint = `${endpoint}/${productId}/reviews?limit=${parseLimit}&offset=${newOffset}&include_aggregate_rating=${include_aggregate_rating}&sort_by=${sort_by}`;
      return resolvedEndpoint;
    },
    transformer: TransformProductReviews,
    isPaginated: true,
  },
  submitReview: {
    ...baseFeraQuerySpec,
    queryType: 'post',
    endpoint: '/reviews/create',
    editableInputParams: {
      heading: '',
      body: '',
      isVerified: null,
      feraCustomerId: '',
      productId: '',
      rating: '',
      email: '',
      name: '',
      isProductReview: 'true',
    },
    inputResolver: (inputVariables: any) => {
      const {heading, body, isVerified, feraCustomerId, productId, rating, email, name, isProductReview} =
        inputVariables ?? {};
      return {
        heading: heading,
        body: body,
        is_verified: isVerified,
        fera_customer_id: feraCustomerId,
        shopify_product_id: productId,
        rating: rating,
        email: email,
        name: name,
        isProductReview: isProductReview,
      };
    },
    endpointResolver: (endpoint, inputVariables) => {
      const resolvedEndpoint = `${endpoint}`;
      return resolvedEndpoint;
    },
    transformer: TransformSubmitReview,
  },
  getStoreRating: {
    ...baseFeraQuerySpec,
    queryType: 'get',
    endpoint: '/store/rating',
    editableInputParams: {},
    endpointResolver: endpoint => {
      return endpoint;
    },
    transformer: TransformStoreRating,
  },
  getProductRating: {
    ...baseFeraQuerySpec,
    queryType: 'get',
    endpoint: '/product/rating',
    editableInputParams: {
      productId: ''
    },
    endpointResolver: (endpoint, inputVariables) => {
      return `/products/${inputVariables.productId}/rating`;
    },
    transformer: TransformProductRating,
  },
};
const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'fera',
  type: 'datasource',
  name: 'Fera',
  description: 'Fera integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const feraEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'Fera API Base Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'Proxy API Base Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiVersion',
      props: {
        label: 'Fera Api Version',
        placeholder: 'v3',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<FeraPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, FeraPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

export default wrapDatasourceModel({
  name: 'fera',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.fera.ai',
    proxyUrl: 'https://api.apptile.io/fera',
    appId: '',
    apiVersion: 'v3',
    queryRunner: 'queryRunner',
  } as FeraPluginConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<FeraPluginConfigType>, dsModelValues: any) => {
    const queryRunner = AjaxQueryRunner();

    const feraApiBaseUrl = dsConfig.config.get('apiBaseUrl');
    const apiVersion = dsConfig.config.get('apiVersion');

    const publicKey = dsModelValues.get('publicKey');

    queryRunner.initClient(`${feraApiBaseUrl}/${apiVersion}/public`, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
          public_key: publicKey ?? '',
        },
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return feraApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails = feraApiRecords && feraApiRecords[queryName] ? feraApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials): Partial<FeraPluginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'fera';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = feraApiRecords[queryName];

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = inputVariables;
    }

    typedDataVariables = queryDetails.inputResolver
      ? yield call(queryDetails.inputResolver, typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    let queryRunner = dsModelValues.get('queryRunner');

    const appId = dsModelValues?.get('appId') ?? null;
    const feraProxyBaseUrl = dsModelValues?.get('proxyUrl') ?? null;
    const apiVersion = dsModelValues?.get('apiVersion') ?? null;

    if (!feraProxyBaseUrl || !apiVersion || !appId) throw new Error('Missing parameters');

    if (queryDetails.endpoint === '/reviews/create') {
      queryRunner = AjaxQueryRunner();

      queryRunner.initClient(`${feraProxyBaseUrl}`, config => {
        config.headers = {
          ...config.headers,
          'X-Shopify-App-Id': appId ?? '',
        };
        return config;
      });
    }

    const queryResponse = yield call(queryRunner.runQuery, queryDetails.queryType, endpoint, {
      ...typedDataVariables,
      ...contextInputParam,
    });

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: feraEditors,
});
