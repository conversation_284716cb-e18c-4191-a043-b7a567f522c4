import {RootState} from '@/root/app/store/RootReducer';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IFlitsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import wishistActions, {resolveNumericProductId, runQuery} from './actions/FlitsWishlistActions';
import {IProductFromWishlistProduct, IWishlistProduct, IWishlistProductIds} from './types/index';
import {selectPluginConfig} from 'apptile-core';
import {call, put, select, spawn} from 'redux-saga/effects';
import {transformFlitsWishlistProducts} from './transformer';
import {modelUpdateAction} from 'apptile-core';
import _ from 'lodash';

export interface WishlistPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  userId: string;
  appId: string;
  productIds: IWishlistProductIds[] | null;
  products: IWishlistProduct[] | null;
  getAllWishlistProducts: any;
  addProductToWishlist: any;
  removeProductFromWishlist: any;
  checkWishlist: string | ((id: string) => boolean);
  currentProductId: string;
  isUpdating: string | ((id: string) => boolean);
  shopifyDS: string;
  guestUserRedirectScreen: string;
  booted: boolean;
}

type IEditableParams = Record<string, any>;

type WishlistQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  endpointResolver: (inputParams: any) => string;
  editableInputParams: IEditableParams;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<WishlistPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, WishlistPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams as {[key: string]: any}).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const wishlistApiRecords: Record<string, WishlistQueryDetails> = {
  addToWishList: {
    queryType: 'post',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist/add_to_wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      productId: '',
      productHandle: '',
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    inputResolver: (inputVariables: any) => {
      const {productId, productHandle} = inputVariables;
      return {
        product_id: productId,
        product_handle: productHandle,
        wsl_product_count: 1,
      };
    },
  },
  removeFromWishlist: {
    queryType: 'delete',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist/remove_from_wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      productId: '',
      productHandle: '',
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    inputResolver: (inputVariables: any) => {
      const {productId, productHandle} = inputVariables;
      return {
        product_id: productId,
        product_handle: productHandle,
        wsl_product_count: 1,
      };
    },
  },
  viewWishlist: {
    queryType: 'get',
    endpointResolver: (inputParams: any) => {
      const {userId} = inputParams;
      const resolvedEndpoint = `/api/1/${userId}/wishlist`;
      return resolvedEndpoint;
    },
    editableInputParams: {
      customerAccessToken: '',
    },
    contextInputParams: {
      userId: '',
    },
    transformer: transformFlitsWishlistProducts,
    inputResolver: (inputVariables: any) => {
      const {customerEmail} = inputVariables;
      return {
        customer_email: customerEmail,
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {
  getAllWishlistProducts: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.getAllWishlistProducts;
    },
    actionMetadata: {
      editableInputParams: {customerAccessToken: ''},
    },
  },
  addProductToWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.addProductToWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', customerAccessToken: '', errorMessage: ''},
    },
  },
  removeProductFromWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.removeProductFromWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', customerAccessToken: '', errorMessage: ''},
    },
  },
  productIds: {
    updatesProps: ['checkWishlist'],
  },

  currentProductId: {
    updatesProps: ['isUpdating'],
  },
  checkWishlist: {
    getValue(model, renderedValue, selector) {
      const wishlistedProductIds = wishistActions.getWishlistedProductIds(model);
      return (productId: string) => {
        return wishistActions.isWishlistedProduct(productId, wishlistedProductIds);
      };
    },
  },
  isUpdating: {
    getValue(model, renderedValue, selector) {
      const currentProductId = model?.currentProductId;
      return (productId: string) => {
        const trasformedProductId = resolveNumericProductId(productId);
        return currentProductId === trasformedProductId ? true : false;
      };
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'flitsWishlist',
  type: 'datasource',
  name: 'Flits Wishlist Integration 1.0',
  description: 'Cross Device wishlist integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const wishlistEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'https://app.getflits.com/api',
      props: {
        label: 'API Base url',
        placeholder: 'https://services.mybcapps.com',
      },
    },
    {
      type: 'codeInput',
      name: 'userId',
      defultValue: '',
      props: {
        label: 'User Id',
        placeholder: 'User Id provided by Flits',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      defultValue: '',
      props: {
        label: 'App ID',
        placeholder: 'Apptile app id',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyDS',
      props: {
        label: 'Shopify Datasource',
        placeholder: '{{shopify}}',
      },
    },
    {
      type: 'screenSelector',
      name: 'guestUserRedirectScreen',
      props: {
        label: 'Guest User Redirect Screen',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: WishlistQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    let {endpointResolver, contextInputParams, editableInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    let endpoint = endpointResolver && endpointResolver(contextInputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables},
      {
        headers: {
          'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken,
        },
      },
    );
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'flitsWishlist',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://app.getflits.com/api',
    appId: '',
    userId: '',
    products: [],
    productIds: [],
    getAllWishlistProducts: 'action',
    addProductToWishlist: 'action',
    removeProductFromWishlist: 'action',
    checkWishlist: 'function',
    currentProductId: '',
    isUpdating: 'function',
    shopifyDS: '',
    booted: false,
    guestUserRedirectScreen: '',
  } as WishlistPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {},

  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    const shopifyDSModel = dsModelValues?.get('shopifyDS');
    let queryRunner = dsModelValues?.get('queryRunner');

    const products = dsModelValues?.get('products');
    const productIds = dsModelValues?.get('productIds');
    const booted = dsModelValues?.get('booted') ?? false;

    const updateAvailable = _.isEmpty(products) || _.isEmpty(productIds) ? true : false;

    if (!pageLoad && !booted) {
      if (!_.isEmpty(queryRunner) && shopifyDSModel?.loggedInUserAccessToken?.accessToken && updateAvailable) {
        yield spawn(function* () {
          try {
            pageModels = state.stageModel.getModelValue([]);
            dsModelValues = pageModels.get(dsConfig.get('id'));

            const {data} = yield call(runQuery, dsModelValues, dsConfig, 'viewWishlist', {
              customerAccessToken: shopifyDSModel?.loggedInUserAccessToken?.accessToken,
            });

            const productIds = _.map(data, product => {
              return {
                id: product?.id,
                handle: product?.handle,
              };
            });

            yield put(
              modelUpdateAction([
                {
                  selector: [dsConfig.get('id'), 'products'],
                  newValue: data,
                },
                {
                  selector: [dsConfig.get('id'), 'productIds'],
                  newValue: productIds,
                },
                {
                  selector: [dsConfig.get('id'), 'booted'],
                  newValue: true,
                },
              ]),
            );
          } catch (error) {
            console.log(`Error fetching wishlist products`, error);
            // TODO: show Toast
          }
        });
      }
      return;
    }

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'x-shopify-app-id': dsConfig.config?.get('appId') ?? '',
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'userId'],
          newValue: dsConfig.config.get('userId'),
        },
        {
          selector: [dsConfig.get('id'), 'appId'],
          newValue: dsConfig.config.get('appId'),
        },
      ],
    };
  },

  // onModelUpdate: function* (model: RootState, dsConfig: string, dsModelValues: string) {

  // },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return wishlistApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = wishlistApiRecords && wishlistApiRecords[queryName] ? wishlistApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IFlitsCredentials): Partial<WishlistPluginConfigType> | boolean {
    const {proxyUrl, userId, appId} = credentials;
    if (!proxyUrl || !userId || !appId) return false;
    return {
      apiBaseUrl: proxyUrl,
      appId: appId,
      userId: userId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'appId', 'userId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'flits';
  },

  // onModelUpdate: function* (model: RootState, dsConfig: string, dsModelValues: string) {
  //   // const customerAccessToken = dsModelValues.get('customerAccessToken');
  //   // console.log('MYFLITS onModelUpdate wishlist inside');
  // },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = wishlistApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: wishlistEditors,
});
