import _ from 'lodash';
import {IProductFromWishlistProduct, IWishlistProduct} from './types';

export const transformFlitsWishlistProducts = (response: {
  data: IProductFromWishlistProduct[];
}): {data: IWishlistProduct[]; hasNextPage: boolean; paginationMeta: {}} => {
  if (!response?.data) return {data: [], hasNextPage: false, paginationMeta: {}};

  const transformedProducts: IWishlistProduct[] = _.map(response?.data, product => {
    return {
      handle: product.product_handle,
      id: product.product_id,
      image: product.product_image,
      title: product.product_title,
    };
  });
  return {data: transformedProducts, hasNextPage: false, paginationMeta: {}};
};
