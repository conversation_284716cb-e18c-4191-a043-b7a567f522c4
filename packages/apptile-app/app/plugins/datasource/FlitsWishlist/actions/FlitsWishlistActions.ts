import {navigateToScreen} from 'apptile-core';
import _, {get} from 'lodash';
import {modelUpdateAction} from 'apptile-core';
import {Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {GetRegisteredPlugin} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import {executeQuery, wishlistApiRecords} from '../index';
import {IProductFromWishlistProduct, IWishlistProduct} from '../types/index';

const SHOPIFY_PRODUCT_GID_MATCH_REGEX = /^gid:\/\/shopify\/Product\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PRODUCT_GID_PREFIX = 'gid://shopify/Product/';

export interface WishistActionInterface {
  getAllWishlistProducts: ActionHandler;
  addProductToWishlist: ActionHandler;
  removeProductFromWishlist: ActionHandler;
  fetchWishlistItems: ActionHandler;
}

export interface wishlistProductsPayload {
  customerAccessToken: string;
  errorMessage?: string;
}
export interface wishlistAddProductsPayload {
  customerEmail: string;
  customerId: number;
  productId: string;
  productHandle: string;
  customerAccessToken: string;
  errorMessage?: string;
}

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = wishlistApiRecords[queryName];
  const queryDetails = wishlistApiRecords[queryName];
  if (!queryDetails) return;

  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}

export const resolveNumericProductId = (productId: string | number): string => {
  if (SHOPIFY_PRODUCT_GID_MATCH_REGEX.test(`${productId}`)) {
    return _.replace(`${productId}`, SHOPIFY_PRODUCT_GID_PREFIX, '');
  }
  return productId.toString();
};

class WishistActions implements WishistActionInterface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    key: string,
  ) {
    const modelUpdates = [];
    const lineItemsSelector = selector.concat([key]);
    modelUpdates.push({
      selector: lineItemsSelector,
      newValue: lineItems,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  getWishlistedProductIds = (model: any) => {
    return model?.productIds ?? [];
  };

  private addProductToWishlistedProductIds = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: {id: string; handle: string},
  ) => {
    const wishlistedProductIds = this.getWishlistedProductIds(model);
    wishlistedProductIds.push(params);
    return await this.applyChanges(dispatch, config, model, selector, wishlistedProductIds, 'productIds');
  };

  private removeProductFromWishlistedProductIds = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    productId: string,
  ) => {
    const wishlistedProductIds = this.getWishlistedProductIds(model);
    const filteredWishlist = wishlistedProductIds.filter((products: IWishlistProduct) => products?.id != productId);
    return await this.applyChanges(dispatch, config, model, selector, filteredWishlist, 'productIds');
  };

  private setLoadingState = async (dispatch: any, selector: Selector, currentProductId: string) => {
    const modelUpdates = [];
    modelUpdates.push({
      selector: selector.concat(['loading']),
      newValue: true,
    });
    modelUpdates.push({
      selector: selector.concat(['currentProductId']),
      newValue: currentProductId,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  private removeLoadingState = async (dispatch: any, selector: Selector) => {
    const modelUpdates = [];
    modelUpdates.push({
      selector: selector.concat(['loading']),
      newValue: false,
    });
    modelUpdates.push({
      selector: selector.concat(['currentProductId']),
      newValue: '',
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };

  private getWishlistedProducts = (model: any) => {
    return model.get('products', []);
  };

  private transformWishlistProducts = (products: IProductFromWishlistProduct[]) => {
    const transformedProducts = products.map(product => {
      return {
        handle: product.product_handle,
        id: product.product_id,
        image: product.product_image,
        title: product.product_title,
      };
    });
    return transformedProducts as IWishlistProduct[];
  };

  getAllWishlistProducts = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as wishlistProductsPayload;
    const result = await runQuery(model, config, 'viewWishlist', payload);
    const wishlistProducts = result?.data;
    this.applyChanges(dispatch, config, model, selector, wishlistProducts, 'products');
  };

  isWishlistedProduct = (productId: string, wishlistedProductIds: any) => {
    const trasformedProductId = resolveNumericProductId(productId);
    if (!trasformedProductId || _.isEmpty(wishlistedProductIds)) return false;
    return _.find(wishlistedProductIds, product => product?.id == trasformedProductId) ? true : false;
  };

  addProductToWishlist = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as wishlistAddProductsPayload;
    const {productId: rawProductId, productHandle, customerAccessToken, errorMessage} = payload;

    const guestUserRedirectScreen = model.get('guestUserRedirectScreen');
    if (!customerAccessToken && guestUserRedirectScreen) {
      dispatch(navigateToScreen(guestUserRedirectScreen, {}));
      return;
    }

    const productId = resolveNumericProductId(rawProductId);
    const wishlistedProductIds = this.getWishlistedProductIds(model);
    if (this.isWishlistedProduct(rawProductId, wishlistedProductIds)) return;

    this.setLoadingState(dispatch, selector, productId);
    await this.addProductToWishlistedProductIds(dispatch, config, model, selector, {
      id: productId,
      handle: productHandle,
    });

    try {
      const {rawData, data, hasNextPage, paginationMeta, errors, hasError} = await runQuery(
        model,
        config,
        'addToWishList',
        {productId, productHandle, customerAccessToken},
      );

      if (data?.status !== true) {
        throw new Error(data?.error);
      }
      this.fetchWishlistItems(dispatch, config, model, selector);
      this.removeLoadingState(dispatch, selector);
    } catch (error) {
      console.log(error);
      this.removeLoadingState(dispatch, selector);
      toast.show(errorMessage || error?.message, {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
  };

  removeProductFromWishlist = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params as wishlistAddProductsPayload;
    const {productId: rawProductId, customerAccessToken, errorMessage} = payload;

    const guestUserRedirectScreen = model.get('guestUserRedirectScreen');
    if (!customerAccessToken && guestUserRedirectScreen) {
      dispatch(navigateToScreen(guestUserRedirectScreen, {}));
      return;
    }

    const productId = resolveNumericProductId(rawProductId);

    try {
      if (!productId) {
        throw new Error('Invalid product details');
      }

      this.setLoadingState(dispatch, selector, productId);
      await this.removeProductFromWishlistedProductIds(dispatch, config, model, selector, productId);

      const {rawData, data, hasNextPage, paginationMeta, errors, hasError} = await runQuery(
        model,
        config,
        'removeFromWishlist',
        {productId, customerAccessToken},
      );
      if (data?.status !== true) {
        throw new Error(data?.error);
      }

      this.fetchWishlistItems(dispatch, config, model, selector);
      this.removeLoadingState(dispatch, selector);
    } catch (error) {
      console.log(error);
      this.removeLoadingState(dispatch, selector);
      toast.show(errorMessage || error?.message, {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
  };

  private fetchWishlistItems = async (dispatch: any, config: PluginConfig, model: any, selector: Selector) => {
    const shopifyDSModel = model?.get('shopifyDS');

    if (!shopifyDSModel?.loggedInUserAccessToken?.accessToken) {
      console.log(`Customer is not logged while fetching wishlists`);
      return;
    }

    const {data} = await runQuery(model, config, 'viewWishlist', {
      customerAccessToken: shopifyDSModel?.loggedInUserAccessToken?.accessToken,
    });

    const productIds = _.map(data, product => {
      return {
        id: product?.id,
        handle: product?.handle,
      };
    });

    const modelUpdates = [
      {
        selector: selector.concat(['products']),
        newValue: data,
      },
      {
        selector: selector.concat(['productIds']),
        newValue: productIds,
      },
    ];
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const wishistActions = new WishistActions();
export default wishistActions;
