import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from '../../../common/datatypes/types';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import _ from 'lodash';

export type PazoConfigType = DatasourcePluginConfig & {
  queryRunner: any;
};

type PazoQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

const PazoApiRecords: Record<string, PazoQueryDetails> = {
  getReport: {
    queryType: 'post',
    endpoint: '/salesReport',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}`;
    },
    editableInputParams: {
      apiKey: '',
      fromDate: '',
      toDate: '',
    },
    isPaginated: false,
    transformer: (data, paginationMeta) => {
      let departmentsById = _.keyBy(data?.departments, v => v?._id);
      let sitesById = _.keyBy(data?.sites, v => v?._id);

      for (const siteId in sitesById) {
        const site = sitesById[siteId];
        for (const dept of site.depts) {
          dept.name = departmentsById[dept?._id]?.name;
        }
      }

      const siteGroups = [];
      for (const siteGroup of data?.siteGroups) {
        let sg = {...siteGroup, sitesData: []};
        for (const siteId of siteGroup.sites) {
          sg.sitesData.push(sitesById[siteId]);
        }
        siteGroups.push(sg);
      }
      return {data: {...data, siteGroups}, hasNextPage: false, paginationMeta};
    },
  },
};

// const propertySettings: PluginPropertySettings = {
//   ...pazoDatasourcePropertySettings,
// };

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'pazo',
  type: 'datasource',
  name: 'Pazo',
  description: 'Pazo Reporting API',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const PazoEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<PazoConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, PazoConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, {
        ...typedDataVariables,
        ...contextInputParam,
      });
    }

    const rawData =  queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }
    console.log('Pazo Query Data', transformedData);
    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'pazo',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://internal.gopazo.com/api/v5',
    queryRunner: 'queryrunner',
    //...pazoDatasourcePluginConfig,
  } as PazoConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<PazoConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return PazoApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = PazoApiRecords && PazoApiRecords[queryName] ? PazoApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'pazo';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = PazoApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: PazoEditors,
});
