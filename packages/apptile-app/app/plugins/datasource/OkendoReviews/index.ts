import {select} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IOkendoCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import _ from 'lodash';
import {TransformGetAggregatedReviews, TransformGetReviews, TransformPostReview} from './transformers';

export type OkendoPluginConfigType = DatasourcePluginConfig &
  IOkendoCredentials & {
    queryRunner: any;
  };

type IEditableParams = Record<string, any>;

export type OkendoQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseOkendoQuerySpec: Partial<OkendoQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};
const OkendoApiRecords: Record<string, OkendoQueryDetails> = {
  getAggregatedData: {
    ...baseOkendoQuerySpec,
    queryType: 'get',
    endpoint: '/stores/',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}${inputVariables.userId}/products/shopify-${inputVariables.handle}/review_aggregate`;
    },
    editableInputParams: {
      userId: '',
      handle: '',
    },
    transformer: TransformGetAggregatedReviews,
  },
  listProductReviews: {
    ...baseOkendoQuerySpec,
    queryType: 'get',
    endpoint: '/stores/',
    endpointResolver: (endpoint: string, inputVariables: any, _paginationMeta: any) => {
      if (_paginationMeta?.nextUrl === undefined) {
        return `${endpoint}${inputVariables.userId}/products/shopify-${inputVariables.handle}/reviews?limit=${inputVariables.limit}`;
      } else {
        return _paginationMeta.nextUrl;
      }
    },
    editableInputParams: {
      userId: '',
      handle: '',
      limit: 25,
    },
    transformer: TransformGetReviews,
    isPaginated: true,
  },
  postCustomerReview: {
    ...baseOkendoQuerySpec,
    queryType: 'post',
    endpoint: '/stores/',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}${inputVariables.userId}/products/shopify-${inputVariables.handle}/reviews`;
    },
    transformer: TransformPostReview,
    inputResolver: (inputVariables, inputParams) => {
      const {message, rating, reviewerName, reviewerEmail, reviewTitle, userId, handle} = inputVariables;
      return {
        body: message,
        rating: Number(rating),
        reviewer: {
          name: reviewerName,
        },
        reviewerEmail: reviewerEmail,
        title: reviewTitle,
        handle: handle,
        userId: userId,
      };
    },
    editableInputParams: {
      message: '',
      rating: 0,
      reviewerName: '',
      reviewerEmail: '',
      reviewTitle: '',
      handle: '',
      userId: '',
    },
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'okendoReviews',
  type: 'datasource',
  name: 'Okendo',
  description: '',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const OkendoEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api.okendo.io/v1',
      },
    },
    {
      type: 'codeInput',
      name: 'okendoUserId',
      props: {
        label: 'Okendo User ID',
        placeholder: '',
      },
    },
  ],
};
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<OkendoPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, OkendoPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<OkendoQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(
          queryDetails.queryType,
          endpoint,
          {...typedDataVariables},
          {
            ...options,
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = OkendoApiRecords[queryName];
  const queryDetails = OkendoApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'okendoReviews',
  config: {
    apiBaseUrl: 'https://api.okendo.io/v1/',
    okendoUserId: '',
    queryRunner: 'queryrunner',
  } as OkendoPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<OkendoPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    let queryRunner = dsModelValues?.get('queryRunner');

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return OkendoApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = OkendoApiRecords && OkendoApiRecords[queryName] ? OkendoApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: IOkendoCredentials): Partial<OkendoPluginConfigType> | boolean {
    const {apiBaseUrl, okendoUserId} = credentials;
    if (!(apiBaseUrl && okendoUserId)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      okendoUserId: okendoUserId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'okendoUserId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'okendoReviews';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = OkendoApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    return response;
  },
  options: {
    pluginListing,
  },
  editors: OkendoEditors,
});
