export const TransformGetAggregatedReviews = (reviewData: any) => {
  return {
    data: reviewData,
  };
};

export const TransformGetReviews = (reviewData: any) => {
  let hasNextPage = false;
  if (reviewData?.nextUrl !== undefined) {
    hasNextPage = true;
  }
  return {
    data: [...reviewData.reviews],
    paginationMeta: {nextUrl: reviewData.nextUrl},
    hasNextPage: hasNextPage,
  };
};

export const TransformPostReview = (reponse: any) => {
  return {
    data: reponse,
  };
};
