import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IApptileCartAssistCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {call} from 'redux-saga/effects';
import {makeBoolean} from 'apptile-core';

export type ApptileCartAssistPluginConfigType = DatasourcePluginConfig &
  Partial<IApptileCartAssistCredentials> & {
    appId: string;
    data: [];
  };

export const ApptileCartAssistRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  enabled: {
    getValue(model, renderedValue, selector) {
      return makeBoolean(renderedValue);
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'cartAssist',
  type: 'datasource',
  name: 'Apptile Cart Assist',
  description: 'Apptile Cart Assist',
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptileCartAssistEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base Url',
        placeholder: '',
      },
    },
    {
      type: 'checkbox',
      name: 'enabled',
      props: {
        label: 'Enable',
        placeholder: '',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'cartAssist',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io/shopify-app-proxy/cart-sync',
    enabled: true,
  } as ApptileCartAssistPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<ApptileCartAssistPluginConfigType>,
    dsModelValues: any,
  ) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ApptileCartAssistRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      ApptileCartAssistRecords && ApptileCartAssistRecords[queryName] ? ApptileCartAssistRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IApptileCartAssistCredentials,
  ): Partial<ApptileCartAssistPluginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'cartAssist';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptileCartAssistEditors,
});
