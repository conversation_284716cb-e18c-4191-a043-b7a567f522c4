import {select} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IReviewsIoReviewsCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import _ from 'lodash';
import {TransformAggregatedData, TransformGetReviews, TransformPostReview} from './transformers';

export type ReviewsIoReviewsPluginConfigType = DatasourcePluginConfig &
  IReviewsIoReviewsCredentials & {
    queryRunner: any;
  };

type IEditableParams = Record<string, any>;

export type ReviewsIoReviewsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (
  data: any,
  page: number,
) => {
  data: any;
  hasNextPage?: boolean;
  paginationMeta?: any;
};

const baseReviewsIoReviewsQuerySpec: Partial<ReviewsIoReviewsQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};
const ReviewsIoReviewsApiRecords: Record<string, ReviewsIoReviewsQueryDetails> = {
  getAggregatedData: {
    ...baseReviewsIoReviewsQuerySpec,
    queryType: 'get',
    endpoint: '/reviews',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const after = 1;
      return `${endpoint}?store=${inputVariables.appKey}&sku=${inputVariables.variantId}&type=product_review${
        after ? `&page=${after}` : ''
      }${inputVariables.per_page ? `&per_page=${inputVariables.per_page}` : ''}${
        inputVariables.sort ? `&sort=${inputVariables.sort}` : ''
      }`;
    },
    editableInputParams: {
      variantId: '',
      per_page: 10,
      sort: 'date',
      appKey: '',
    },
    transformer: TransformAggregatedData,
    inputResolver: (inputVariables, inputParams) => {
      const {per_page, star, sort, direction, variantId, appKey} = inputVariables;
      return {
        per_page: per_page,
        star: star,
        sort: sort,
        direction: direction,
        appKey: appKey,
        variantId: variantId,
      };
    },
  },
  listProductReviews: {
    ...baseReviewsIoReviewsQuerySpec,
    queryType: 'get',
    endpoint: '/reviews',
    endpointResolver: (endpoint: string, inputVariables: any, _paginationMeta: any) => {
      const {after = 1} = _paginationMeta ?? {};
      return `${endpoint}?store=${inputVariables.appKey}&sku=${inputVariables.variantId}&type=product_review${
        after ? `&page=${after}` : ''
      }${inputVariables.per_page ? `&per_page=${inputVariables.per_page}` : ''}${
        inputVariables.sort ? `&sort=${inputVariables.sort}` : ''
      }`;
    },
    editableInputParams: {
      variantId: '',
      appKey: '',
      page: 1,
      per_page: 10,
      sort: 'date',
    },
    transformer: TransformGetReviews,
    inputResolver: (inputVariables, inputParams) => {
      const {per_page, star, sort, direction, variantId, page, appKey} = inputVariables;
      return {
        per_page: per_page,
        star: star,
        sort: sort,
        page: page,
        appKey: appKey,
        direction: direction,
        variantId: variantId,
      };
    },
    isPaginated: true,
  },
  postCustomerReview: {
    ...baseReviewsIoReviewsQuerySpec,
    queryType: 'post',
    endpoint: '/product/review/new',
    endpointResolver: (endpoint: string, inputVariables) => {
      return `${endpoint}?store=${inputVariables.appKey}`;
    },
    transformer: TransformPostReview,
    inputResolver: (inputVariables, inputParams) => {
      const {rating, review, email, name, sku, appKey} = inputVariables;
      return {
        rating: rating,
        review: review,
        email: email,
        name: name,
        sku: sku,
        appKey: appKey,
      };
    },
    editableInputParams: {
      sku: '',
      appKey: '',
      name: '',
      email: '',
      rating: '',
      review: '',
    },
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'reviewsIoReviews',
  type: 'datasource',
  name: 'ReviewsIo Reviews',
  description: '',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const ReviewsIoReviewsEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api.reviews.io',
      },
    },
    {
      type: 'codeInput',
      name: 'appKey',
      props: {
        label: 'ReviewsIoReviews Store Key',
        placeholder: '',
      },
    },
  ],
};
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<ReviewsIoReviewsPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ReviewsIoReviewsPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<ReviewsIoReviewsQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(
          queryDetails.queryType,
          endpoint,
          {...typedDataVariables},
          {
            ...options,
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      let page = paginationMeta?.after ? paginationMeta?.after : typedDataVariables?.page;
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, page);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = ReviewsIoReviewsApiRecords[queryName];
  const queryDetails = ReviewsIoReviewsApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'reviewsIoReviews',
  config: {
    apiBaseUrl: 'https://api.reviews.io',
    appKey: '',
    queryRunner: 'queryrunner',
  } as ReviewsIoReviewsPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<ReviewsIoReviewsPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    let queryRunner = dsModelValues?.get('queryRunner');

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return ReviewsIoReviewsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      ReviewsIoReviewsApiRecords && ReviewsIoReviewsApiRecords[queryName]
        ? ReviewsIoReviewsApiRecords[queryName]
        : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IReviewsIoReviewsCredentials,
  ): Partial<ReviewsIoReviewsPluginConfigType> | boolean {
    const {apiBaseUrl, appKey} = credentials;
    if (!(apiBaseUrl && appKey)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      appKey: appKey,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'appKey'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'reviewsIoReviews';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = ReviewsIoReviewsApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    return response;
  },
  options: {
    pluginListing,
  },
  editors: ReviewsIoReviewsEditors,
});
