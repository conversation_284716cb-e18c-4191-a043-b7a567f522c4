export const TransformAggregatedData = (data: any) => {
  return {
    data: {
      stats: data.stats.ratings,
      review_count: data.review_count,
      average_rating: data.average_rating,
    },
  };
};

export const TransformGetReviews = (data: any, page: number) => {
  const {reviews} = data;
  const hasNextPage = !(reviews.length === 0);
  const paginationMeta = {after: page + 1};

  return {
    data: reviews,
    hasNextPage,
    paginationMeta,
  };
};
export const TransformPostReview = (reponse: any) => {
  return {
    data: reponse,
  };
};
