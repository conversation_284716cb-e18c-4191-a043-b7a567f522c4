import {selectPluginConfig} from '@/root/app/selectors/AppConfigSelector';
import {RootState} from '@/root/app/store/RootReducer';
import {select} from 'redux-saga/effects';
import {ImmutableMapType, PluginConfigType} from '../../../common/datatypes/types';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IDiscourseCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformGetCategories,
  TransformGetCategory,
  TransformGetCategoryTopics,
  TransformGetLatestTopics,
  TransformGetTopicByExternalId,
  TransformUser,
} from './transformer';

import {
  IDiscourseActionsDatasourcePluginConfigType,
  discourseActionsDatasourceEditor,
  discourseActionsDatasourcePluginConfig,
  discourseActionsDatasourcePropertySettings,
} from './actions/discourseActions';
import {Editors} from '@/root/app/common/EditorControlTypes';

export interface DiscoursePluginConfigType extends DatasourcePluginConfig, IDiscourseActionsDatasourcePluginConfigType {
  apiBaseUrl: string;
  userId: string;
  appId: string;
  communityEndpoint: string;
}

type IEditableParams = Record<string, any>;

type DiscourseQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any, contextInputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => string;
  apiBaseUrlResolver: (dsModel: any) => string;
  editableInputParams: IEditableParams;
};
export type TransformerFunction = (
  data: any,
  context: any,
  model?: ImmutableMapType<any> | undefined,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<DiscoursePluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, DiscoursePluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams as {[key: string]: any}).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseDiscourseQuerySpec: Partial<DiscourseQueryDetails> = {
  isPaginated: false,
  contextInputParams: {},
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
  endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
    return endpoint;
  },
  apiBaseUrlResolver: (dsModel: any) => {
    return dsModel.get('apiBaseUrl');
  },
  editableInputParams: {},
  inputResolver: (inputVariables: any) => {
    return inputVariables;
  },
  queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
    return {};
  },
};

export const discourseApiRecords: Record<string, Partial<DiscourseQueryDetails>> = {
  GetCategories: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/categories.json`,
    transformer: TransformGetCategories,
  },
  GetLatestTopics: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/latest.json`,
    transformer: TransformGetLatestTopics,
  },
  GetTopTopics: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/top.json`,
    editableInputParams: {
      period: 'all',
    },
    transformer: TransformGetLatestTopics,
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {period} = inputVariables ?? {};
      return `${endpoint}?period=${period}`;
    },
  },
  GetTopicByExternalId: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/t`,
    editableInputParams: {
      handle: '',
      topicId: '',
    },
    transformer: TransformGetTopicByExternalId,
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {topicId, handle} = inputVariables ?? {};
      return `${endpoint}/${handle}/${topicId}.json`;
    },
  },
  GetSingleTopiclId: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/t`,
    editableInputParams: {
      id: '',
    },
    transformer: TransformGetTopicByExternalId,
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {id} = inputVariables ?? {};
      return `t/${id}.json`;
    },
  },

  authenticateUser: {
    ...baseDiscourseQuerySpec,
    queryType: 'post',
    endpoint: `/api/community/users/login`,
    contextInputParams: {
      appId: '',
    },
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      email: '',
    },
    inputResolver(inputVariables) {
      const {email} = inputVariables;
      return {email};
    },
    transformer: TransformUser,
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {customerAccessToken} = inputVariables ?? {};
      const {appId} = contextInputVariables ?? {};
      return {
        'x-shopify-customer-access-token': customerAccessToken,
        'x-shopify-app-id': appId,
      };
    },
  },
  CreatePost: {
    ...baseDiscourseQuerySpec,
    queryType: 'post',
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    endpoint: `/api/community/posts`,
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      title: '',
      topicId: 0,
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    inputResolver(inputVariables) {
      const {title, topicId} = inputVariables;
      return {
        title: title,
        raw: title,
        topic_id: topicId,
      };
    },
  },
  EditPost: {
    //https://{defaultHost}/posts/{id}.json
    ...baseDiscourseQuerySpec,
    queryType: 'put',
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    endpoint: `/api/community/posts`,
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      id: '',
      title: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {id} = inputVariables ?? {};
      return `/api/community/posts/${id}`;
    },
    inputResolver(inputVariables) {
      const {title} = inputVariables;
      return {
        post: {
          raw: title,
        },
      };
    },
  },
  deletePost: {
    ...baseDiscourseQuerySpec,
    queryType: 'delete',
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    endpoint: `/api/community/posts`,
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      id: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {id} = inputVariables ?? {};
      return `/api/community/posts/${id}`;
    },
  },
  CreateTopic: {
    ...baseDiscourseQuerySpec,
    queryType: 'post',
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    endpoint: `/api/community/posts`,
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      title: '',
      description: '',
      categoryId: 0,
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    inputResolver(inputVariables) {
      const {title, categoryId, description} = inputVariables;
      return {
        title: title,
        raw: description,
        category: categoryId,
      };
    },
  },

  EditTopic: {
    //https://{defaultHost}/posts/{id}.json
    ...baseDiscourseQuerySpec,
    queryType: 'put',
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    endpoint: `/api/community/posts`,
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    editableInputParams: {
      id: '',
      title: '',
      categoryId: '',
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {id} = inputVariables ?? {};
      return `/api/community/t/${id}`;
    },
    inputResolver(inputVariables) {
      const {title, categoryId} = inputVariables ?? {};
      return {
        topic: {
          title,
          categoryId: categoryId,
        },
      };
    },
  },

  //c/baby-care/37.json
  ListTopicsByCategory: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/api/community/c/baby-care/37`,
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {categoryId, slug} = inputVariables ?? {};
      return `/api/community/c/${slug}/${categoryId}`;
    },
    transformer: TransformGetCategoryTopics,
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    editableInputParams: {
      slug: '',
      categoryId: 0,
    },
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    inputResolver(inputVariables) {
      const {slug, categoryId} = inputVariables;
      return {
        slug: slug,
        category: categoryId,
      };
    },
  },
  GetCategory: {
    ...baseDiscourseQuerySpec,
    queryType: 'get',
    endpoint: `/c/{id}/show.json`,
    transformer: TransformGetCategory,
    contextInputParams: {
      appId: '',
      discourseLoggedInUser: '',
    },
    editableInputParams: {
      categoryId: 0,
    },
    apiBaseUrlResolver: (dsModel: any) => {
      return dsModel.get('communityEndpoint');
    },
    queryHeadersResolver: (inputVariables: any, contextInputVariables: any) => {
      const {} = inputVariables ?? {};
      const {appId, discourseLoggedInUser} = contextInputVariables ?? {};
      return {
        'x-shopify-app-id': appId,
        Authorization: discourseLoggedInUser?.token ? `Bearer ${discourseLoggedInUser.token}` : '',
      };
    },
    endpointResolver: (endpoint: string, inputVariables: any, contextInputVariables: any) => {
      const {categoryId} = inputVariables ?? {};
      return `/api/community/c/${categoryId}/show`;
    },
  },
};

const propertySettings: PluginPropertySettings = {
  ...discourseActionsDatasourcePropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'discourse',
  type: 'datasource',
  name: 'Discourse Community',
  description: 'Discourse Community',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const discourseEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
      },
    },
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'http://localhost',
      props: {
        label: 'API Base url',
        placeholder: 'http://localhost',
      },
    },
    {
      type: 'codeInput',
      name: 'communityEndpoint',
      defultValue: 'http://localhost:3000',
      props: {
        label: 'Community Helper Endpoint',
        placeholder: 'http://localhost:3000',
      },
    },
    ...(discourseActionsDatasourceEditor.basic as Editors<any>),
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: DiscourseQueryDetails,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    let {endpointResolver, contextInputParams, editableInputParams, endpoint} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const contextInputVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let typedInputVariables, typedDataVariables;
    if (editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    }

    endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables, contextInputVariables);

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    const apiBaseUrl = queryDetails.apiBaseUrlResolver(dsModelValues);

    let headers = {};
    if (queryDetails.queryHeadersResolver)
      headers = queryDetails.queryHeadersResolver(inputVariables, contextInputVariables);

    let queryRunner = AjaxQueryRunner();

    queryRunner.initClient(apiBaseUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          ...headers,
          'Content-Type': 'application/json',
        },
      };
      return config;
    });

    // const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = await queryRunner.runQuery(
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables},
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, null, dsModelValues);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex: any) {
    const errors = ex?.response?.data?.errors || [ex?.message];
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: errors,
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'discourse',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: '',
    appId: '',
    communityEndpoint: 'https://api.apptile.io/apptile-community',
    ...discourseActionsDatasourcePluginConfig,
  } as DiscoursePluginConfigType,

  // initDatasource: function* (
  //   dsModel: any,
  //   dsConfig: PluginConfigType<DiscoursePluginConfigType>,
  //   dsModelValues: any,
  // ) {},

  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {
    // const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    // const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    // const mergedConfig = configGen(pluginConfig.config);
    // const dsConfig = pluginConfig.set('config', mergedConfig);
    // let queryRunner = AjaxQueryRunner();
    // queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
    //   config.headers = {
    //     ...config.headers,
    //     ...{
    //       'Content-Type': 'application/json',
    //     },
    //   };
    //   return config;
    // });
    // return {
    //   modelUpdates: [
    //     {
    //       selector: [dsConfig.get('id'), 'queryRunner'],
    //       newValue: queryRunner,
    //     },
    //   ],
    // };
  },

  // onModelUpdate: function* (model: RootState, dsConfig: string, dsModelValues: string) {
  // },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return discourseApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = discourseApiRecords && discourseApiRecords[queryName] ? discourseApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IDiscourseCredentials,
  ): Partial<DiscoursePluginConfigType> | boolean {
    const {apiBaseUrl, appId, communityHelperEndpoint} = credentials;
    
    if (!(apiBaseUrl && appId && communityHelperEndpoint)) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      appId: appId,
      communityEndpoint: communityHelperEndpoint,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'discourse';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = discourseApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: discourseEditors,
});
