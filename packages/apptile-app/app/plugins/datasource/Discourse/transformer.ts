import _ from 'lodash';
import {formatQueryReturn, jsonArrayMapper, JSONMapperSchema, jsonObjectMapper} from '../ShopifyV_22_10/utils/utils';
import {
  IApptileCommunityCategory,
  IApptileCommunityTopic,
  IApptileCommunityTopicDetails,
  IApptileCommunityUser,
  IApptileCommunityUserAndToken,
  IDiscourseGetCategories,
  IDiscourseGetLatestTopics,
  IDiscourseGetTopicDetails,
  IDiscourseTopic,
} from './types';
import {ImmutableMapType} from '@/root/app/common/datatypes/types';

export const _transformCategories = (
  data: IDiscourseGetCategories['category_list']['categories'],
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
): IApptileCommunityCategory[] => {
  if (!data) return [];

  const apiBaseUrl = dsModelValues?.get('apiBaseUrl');

  const categorySchema: JSONMapperSchema = [
    'id',
    'name',
    'color',
    'slug',
    'position',
    'description',
    {
      field: 'textColor',
      path: 'text_color',
    },
    {
      field: 'topicCount',
      path: 'topic_count',
    },
    {
      field: 'postCount',
      path: 'post_count',
    },
    {
      field: 'descriptionText',
      path: 'description_text',
    },
    {
      field: 'descriptionExcerpt',
      path: 'description_excerpt',
    },
    {
      field: 'topicUrl',
      path: 'topic_url',
    },
    {
      field: 'numFeaturedTopics',
      path: 'num_featured_topics',
    },
    {
      field: 'logoUrl',
      path: 'uploaded_logo.url',
      formatterFunction: (val: string) => {
        return `${apiBaseUrl}${val}`;
      },
    },
  ];

  return data?.map(category => {
    return {
      ...jsonObjectMapper(categorySchema, category),
      categoryUrl: `/c/${category?.slug}/${category?.id}`,
    };
  }) as IApptileCommunityCategory[];
};

export const TransformGetCategories = (
  data: IDiscourseGetCategories,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const categories = _.get(data, 'category_list.categories');
  const result = _transformCategories(categories, context, dsModelValues) as IApptileCommunityCategory[];
  return formatQueryReturn(result, categories, {}, false, null);
};

export const _transformTopics = (
  data: IDiscourseGetLatestTopics['topic_list']['topics'] | undefined,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
): IApptileCommunityTopic[] => {
  if (!data) return [];

  const topicSchema: JSONMapperSchema = [
    'id',
    'title',
    {
      field: 'fancyTitle',
      path: 'fancy_title',
    },
    'slug',
    {
      field: 'postsCount',
      path: 'posts_count',
    },
    {
      field: 'replyCount',
      path: 'reply_count',
    },
    {
      field: 'highestPostNumber',
      path: 'highest_post_number',
    },
    {
      field: 'imageUrl',
      path: 'image_url',
    },
    {
      field: 'createdAt',
      path: 'created_at',
    },
    {
      field: 'lastPostedAt',
      path: 'last_posted_at',
    },
    'pinned',
    'visible',
    'liked',
    'tags',
    'views',
    {
      field: 'lastPostedAt',
      path: 'last_posted_at',
    },
    {
      field: 'likeCount',
      path: 'like_count',
    },
    {
      field: 'lastPosterUsername',
      path: 'last_poster_username',
    },
    {
      field: 'categoryId',
      path: 'category_id',
    },
    {
      field: 'updatedAt',
      path: 'bumped_at',
    },
  ];

  return data?.map(topic => {
    return {
      ...jsonObjectMapper(topicSchema, topic),
      topicUrl: `/t/${topic?.slug}/${topic?.id}`,
    };
  }) as IApptileCommunityTopic[];
};

export const TransformGetLatestTopics = (
  data: IDiscourseGetCategories,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const rawTopics = _.get(data, 'topic_list.topics');
  const result = _transformTopics(rawTopics, context, dsModelValues) as IApptileCommunityTopic[];
  return formatQueryReturn(result, rawTopics, {}, false, null);
};

export const _transformTopic = (
  data: IDiscourseTopic | undefined,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
): IApptileCommunityTopicDetails | undefined => {
  if (!data) return;

  const postStreamSchema: JSONMapperSchema = [
    'id',
    'name',
    'username',
    {
      field: 'title',
      path: 'cooked',
    },
    {
      field: 'createdAt',
      path: 'created_at',
    },
    {
      field: 'updatedAt',
      path: 'updated_at',
    },
    {
      field: 'displayUsername',
      path: 'display_username',
    },
  ];

  const topicSchema: JSONMapperSchema = [
    'id',
    'title',
    {
      field: 'fancyTitle',
      path: 'fancy_title',
    },
    'slug',
    {
      field: 'postsCount',
      path: 'posts_count',
    },
    {
      field: 'replyCount',
      path: 'reply_count',
    },
    {
      field: 'highestPostNumber',
      path: 'highest_post_number',
    },
    {
      field: 'imageUrl',
      path: 'image_url',
    },
    {
      field: 'createdAt',
      path: 'created_at',
    },
    {
      field: 'lastPostedAt',
      path: 'last_posted_at',
    },
    'pinned',
    'visible',
    'liked',
    'tags',
    'views',
    {
      field: 'lastPostedAt',
      path: 'last_posted_at',
    },
    {
      field: 'likeCount',
      path: 'like_count',
    },
    {
      field: 'lastPosterUsername',
      path: 'details.last_poster.username',
    },
    {
      field: 'lastPosterName',
      path: 'details.last_poster.name',
    },
    {
      field: 'categoryId',
      path: 'category_id',
    },
    {
      field: 'posts',
      path: 'post_stream.posts',
      transform: currentValue => {
        return jsonArrayMapper(postStreamSchema, currentValue);
      },
    },
  ];

  return {
    ...jsonObjectMapper(topicSchema, data),
    topicUrl: `/t/${data?.slug}/${data?.id}`,
  } as IApptileCommunityTopicDetails;
};

export const TransformGetTopicByExternalId = (
  data: IDiscourseGetTopicDetails,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const result = _transformTopic(data, context, dsModelValues) as IApptileCommunityTopicDetails;
  return formatQueryReturn(result, data, {}, false, null);
};

export const _transformUser = (
  data: any,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
): IApptileCommunityUserAndToken | undefined => {
  if (!data) return;

  const userSchema: JSONMapperSchema = [
    'id',
    'name',
    'username',
    'avatar_template',
    {
      field: 'avatarTemplate',
      path: 'avatar_template',
    },
    {
      field: 'lastPostedAt',
      path: 'last_posted_at',
    },
    {
      field: 'createdAt',
      path: 'created_at',
    },
  ];

  const {user, token} = data;
  return {
    token,
    user: jsonObjectMapper(userSchema, user) as IApptileCommunityUser,
  } as IApptileCommunityUserAndToken;
};

export const TransformUser = (
  data: IDiscourseGetTopicDetails,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const result = _transformUser(data, context, dsModelValues) as IApptileCommunityUserAndToken;
  return formatQueryReturn(result, data, {}, false, null);
};

export const TransformGetCategoryTopics = (
  data: IDiscourseGetCategories,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const rawTopics = _.get(data, 'response.topic_list.topics');
  const result = _transformTopics(rawTopics, context, dsModelValues) as IApptileCommunityTopic[];
  return formatQueryReturn(result, rawTopics, {}, false, null);
};

export const TransformGetCategory = (
  data: IDiscourseGetCategories,
  context: any,
  dsModelValues: ImmutableMapType<any> | undefined,
) => {
  const rawData = _.get(data, 'response.category') as any;

  const apiBaseUrl = dsModelValues?.get('apiBaseUrl');

  const categorySchema: JSONMapperSchema = [
    'id',
    'name',
    'color',
    'slug',
    'position',
    'description',
    {
      field: 'textColor',
      path: 'text_color',
    },
    {
      field: 'topicCount',
      path: 'topic_count',
    },
    {
      field: 'postCount',
      path: 'post_count',
    },
    {
      field: 'descriptionText',
      path: 'description_text',
    },
    {
      field: 'descriptionExcerpt',
      path: 'description_excerpt',
    },
    {
      field: 'topicUrl',
      path: 'topic_url',
    },
    {
      field: 'numFeaturedTopics',
      path: 'num_featured_topics',
    },
    {
      field: 'logoUrl',
      path: 'uploaded_logo.url',
      formatterFunction: (val: string) => {
        return `${apiBaseUrl}${val}`;
      },
    },
  ];

  const result = {
    ...jsonObjectMapper(categorySchema, rawData),
    categoryUrl: `/c/${rawData?.slug}/${rawData?.id}`,
  } as IApptileCommunityCategory;
  return formatQueryReturn(result, rawData, {}, false, null);
};
