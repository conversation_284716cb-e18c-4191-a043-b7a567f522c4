export interface IDiscourseGetCategories {
  category_list: {
    can_create_category: boolean;
    can_create_topic: boolean;
    categories: {
      id: number;
      name: string;
      color: string;
      text_color: string;
      slug: string;
      topic_count: number;
      post_count: number;
      position: number;
      description: any;
      description_text: any;
      description_excerpt: any;
      topic_url: string;
      read_restricted: boolean;
      permission: any;
      notification_level: number;
      topic_template: any;
      has_children: boolean;
      sort_order: any;
      sort_ascending: any;
      show_subcategory_list: boolean;
      num_featured_topics: number;
      default_view: any;
      subcategory_list_style: string;
      default_top_period: string;
      default_list_filter: string;
      minimum_required_tags: number;
      navigate_to_first_post_after_read: boolean;
      topics_day: number;
      topics_week: number;
      topics_month: number;
      topics_year: number;
      topics_all_time: number;
      subcategory_ids: any[];
      uploaded_logo: any;
      uploaded_logo_dark: any;
      uploaded_background: any;
    }[];
  };
}

export interface IDiscourseTopic {
  id: number;
  title: string;
  fancy_title: string;
  slug: string;
  posts_count: number;
  reply_count: number;
  highest_post_number: number;
  image_url: any;
  created_at: string;
  last_posted_at: string;
  bumped: boolean;
  bumped_at: string;
  archetype: string;
  unseen: boolean;
  pinned: boolean;
  unpinned: any;
  visible: boolean;
  closed: boolean;
  archived: boolean;
  bookmarked: any;
  liked: any;
  tags: any[];
  tags_descriptions: any;
  views: number;
  like_count: number;
  has_summary: boolean;
  last_poster_username: string;
  category_id: number;
  pinned_globally: boolean;
  featured_link: any;
  posters: {
    extras: string;
    description: string;
    user_id: number;
    primary_group_id: any;
    flair_group_id: any;
  }[];
}

export interface IDiscourseGetLatestTopics {
  users: {
    id: number;
    username: string;
    name: string;
    avatar_template: string;
    admin: boolean;
    trust_level: number;
  }[];
  primary_groups: any[];
  flair_groups: any[];
  topic_list: {
    can_create_topic: boolean;
    per_page: number;
    top_tags: any[];
    topics: IDiscourseTopic[];
  };
}

export interface IDiscourseGetTopicDetails extends IDiscourseTopic {
  post_stream: {
    posts: {
      id: number;
      name: string;
      username: string;
      avatar_template: string;
      created_at: string;
      cooked: string;
      post_number: number;
      post_type: number;
      updated_at: string;
      reply_count: number;
      reply_to_post_number?: number;
      quote_count: number;
      incoming_link_count: number;
      reads: number;
      readers_count: number;
      score: number;
      yours: boolean;
      topic_id: number;
      topic_slug: string;
      display_username: string;
      primary_group_name: any;
      flair_name: any;
      flair_url: any;
      flair_bg_color: any;
      flair_color: any;
      flair_group_id: any;
      version: number;
      can_edit: boolean;
      can_delete: boolean;
      can_recover: boolean;
      can_see_hidden_post: boolean;
      can_wiki: boolean;
      read: boolean;
      user_title: any;
      bookmarked: boolean;
      actions_summary: any[];
      moderator: boolean;
      admin: boolean;
      staff: boolean;
      user_id: number;
      hidden: boolean;
      trust_level: number;
      deleted_at: any;
      user_deleted: boolean;
      edit_reason: any;
      can_view_edit_history: boolean;
      wiki: boolean;
      reply_to_user?: {
        username: string;
        name: string;
        avatar_template: string;
      };
    }[];
    stream: number[];
  };
  timeline_lookup: number[][];
  suggested_topics: IDiscourseTopic[];
  slug: string;
  word_count: number;
  deleted_at: any;
  user_id: number;
  pinned_at: any;
  pinned_until: any;
  slow_mode_seconds: number;
  draft: any;
  draft_key: string;
  draft_sequence: any;
  current_post_number: number;
  deleted_by: any;
  actions_summary: {
    id: number;
    count: number;
    hidden: boolean;
    can_act: boolean;
  }[];
  chunk_size: number;
  bookmarks: any[];
  topic_timer: any;
  message_bus_last_id: number;
  participant_count: number;
  show_read_indicator: boolean;
  thumbnails: any;
  slow_mode_enabled_until: any;
  summarizable: boolean;
  details: {
    can_edit: boolean;
    notification_level: number;
    participants: {
      id: number;
      username: string;
      name: string;
      avatar_template: string;
      post_count: number;
      primary_group_name: any;
      flair_name: any;
      flair_url: any;
      flair_color: any;
      flair_bg_color: any;
      flair_group_id: any;
      admin: boolean;
      trust_level: number;
    }[];
    created_by: {
      id: number;
      username: string;
      name: string;
      avatar_template: string;
    };
    last_poster: {
      id: number;
      username: string;
      name: string;
      avatar_template: string;
    };
  };
}

export interface IApptileCommunityCategory {
  id: number;
  name: string;
  color: string;
  slug: string;
  position: number;
  description: any;
  textColor: string;
  topicCount: number;
  postCount: number;
  descriptionText: any;
  descriptionExcerpt: any;
  topicUrl: string;
  numFeaturedTopics: number;
  logoUrl: string;
  categoryUrl: string;
}

export interface IApptileCommunityTopic {
  id: number;
  title: string;
  fancyTitle: string;
  slug: string;
  postsCount: number;
  replyCount: number;
  highestPostNumber: number;
  imageUrl: any;
  createdAt: string;
  lastPostedAt: string;
  pinned: boolean;
  visible: boolean;
  liked: any;
  tags: any[];
  views: number;
  likeCount: number;
  lastPosterUsername: string;
  categoryId: number;
  topicUrl: string;
}

export interface IApptileCommunityPost {
  id: number;
  name: string;
  username: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  displayUsername: string;
}

export interface IApptileCommunityTopicDetails extends IApptileCommunityTopic {
  posts: IApptileCommunityPost[];
}

export interface IApptileCommunityUser {
  id: number;
  name: string;
  username: string;
  avatarTemplate: string;
  lastPostedAt: string;
  createdAt: string;
}

export interface IApptileCommunityUserAndToken {
  token: string;
  user: IApptileCommunityUser;
}
