import {modelUpdateAction} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import _ from 'lodash';
import {executeQuery} from '..';
import {ModelChange, Selector} from 'apptile-core';
import {LocalStorage} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {ActionHandler} from 'apptile-core';

export interface IDiscourseActionsDatasourcePluginConfigType {
  authenticateUser: string;
  discourseLoggedInUser: any;
  authLoader: boolean;
}

export const discourseActionsDatasourcePluginConfig: IDiscourseActionsDatasourcePluginConfigType = {
  authenticateUser: TriggerActionIdentifier,
  discourseLoggedInUser: '',
  authLoader: false,
};

export const discourseActionsDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [],
};

export const discourseActionsDatasourcePropertySettings: PluginPropertySettings = {
  authenticateUser: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return discourseActions.authenticateUser;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        customerAccessToken: '',
        successMessage: '',
        badParameterMessage: '',
        errorMessage: '',
      },
    },
  },
};

export interface ISignInUserPayload {
  email: string;
  customerAccessToken: string;
  successMessage: string;
  badParameterMessage: string;
  errorMessage: string;
}

export interface IDiscourseActionsInterface {
  authenticateUser: ActionHandler;
}

class DiscourseActions implements IDiscourseActionsInterface {
  private async communityHelperQueryExecutor(
    dsModelValues: any,
    dsConfig: any,
    queryName: string,
    inputVariables: any,
  ) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];
    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }

  private getQueries() {
    const dsConfig = GetRegisteredPluginInfo('discourse');
    const queries = dsConfig?.plugin?.getQueries();
    return queries;
  }

  private async updateLocalStore(key: string, value: any) {
    await LocalStorage.setValue(key, value);
  }

  private async storeLoggedInUserDetails(value: any) {
    await this.updateLocalStore(`discourseLoggedInUser`, value);
  }

  private async setErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });

    setTimeout(() => dispatch(modelUpdateAction(newModelUpdates, undefined, true)), 1);
  }

  private async setBadParamErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private async setSuccessMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    customerData: any,
    message: string,
  ) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    if (!_.isEmpty(message)) {
      toast.show(message, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private preSignInUser = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as ISignInUserPayload;
    const {email, customerAccessToken} = payload;
    let customerData = null;

    const {data: signInResponse, errors: transformedError} = await this.communityHelperQueryExecutor(
      model,
      config,
      'authenticateUser',
      {
        email,
        customerAccessToken,
      },
    );

    if (!signInResponse || !signInResponse?.token) {
      console.log(`Error signInResponse`, signInResponse);
      return {customerData, error: _.first(transformedError)?.message};
    }

    await this.storeLoggedInUserDetails(signInResponse);
    customerData = {
      discourseLoggedInUser: signInResponse,
    };
    return {customerData, error: null};
  };

  authenticateUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    const payload = params as ISignInUserPayload;
    const {successMessage, errorMessage, badParameterMessage} = payload;
    try {
      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const {customerData, error} = await this.preSignInUser(dispatch, config, model, selector, params);

      if (error) {
        console.log(`error`, error);
        this.setBadParamErrorMessage(dispatch, config, model, selector, error, badParameterMessage);
        return;
      }

      this.setSuccessMessage(dispatch, config, model, selector, customerData, successMessage);
    } catch (error) {
      console.log(`Error signUpResponse`, error);
      this.setErrorMessage(dispatch, config, model, selector, error?.message, errorMessage);
    }
  };
}

const discourseActions = new DiscourseActions();
export default discourseActions;
