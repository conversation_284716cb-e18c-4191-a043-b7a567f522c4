import {select} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType, IEasyAppointmentCredentials} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredPlugin,
  PluginListingSettings,
  PluginModelType,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {RootState} from '@/root/app/store/RootReducer';
import {selectPluginConfig} from 'apptile-core';
import _ from 'lodash';
import {TransformData} from './transformers';

export type EasyAppointmentPluginConfigType = DatasourcePluginConfig &
  IEasyAppointmentCredentials & {
    queryRunner: any;
  };

type IEditableParams = Record<string, any>;

export type EasyAppointmentQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => Boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseEasyAppointmentQuerySpec: Partial<EasyAppointmentQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return after ? {...inputVariables, after} : inputVariables;
  },
};
const EasyAppointmentApiRecords: Record<string, EasyAppointmentQueryDetails> = {
  getProductData: {
    ...baseEasyAppointmentQuerySpec,
    queryType: 'get',
    endpoint: '/products/',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}/${inputVariables.productId}/events?variantId=${inputVariables.variantId}`;
    },
    editableInputParams: {
      productId: '',
      variantId: '',
    },
    transformer: TransformData,
  },
  getStoreData: {
    ...baseEasyAppointmentQuerySpec,
    queryType: 'get',
    endpoint: '/settings',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}?storeUrl=${inputVariables.storeUrl}`;
    },
    editableInputParams: {
      storeUrl: '',
    },
    transformer: TransformData,
  },
  getProductAllEvents: {
    ...baseEasyAppointmentQuerySpec,
    queryType: 'get',
    endpoint: '/products/',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}/${inputVariables.productId}/allEvents`;
    },
    editableInputParams: {
      productId: '',
    },
    transformer: TransformData,
  },
  getSlotDetails: {
    ...baseEasyAppointmentQuerySpec,
    queryType: 'post',
    endpoint: '/events',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}/${inputVariables.slotId}/employees/busy-slots`;
    },
    inputResolver: (inputVariables, inputParams) => {
      const {employeeIds, dateFrom, dateTo, inputTimezone, slotId} = inputVariables;
      return {
        employeeIds: employeeIds,
        dateFrom: dateFrom,
        dateTo: dateTo,
        inputTimezone: inputTimezone,
        slotId: slotId,
      };
    },
    editableInputParams: {
      slotId: '',
      employeeIds: '',
      dateFrom: '',
      dateTo: '',
      inputTimezone: '',
    },
    transformer: TransformData,
  },
  bookAppointment: {
    ...baseEasyAppointmentQuerySpec,
    queryType: 'post',
    endpoint: '/storefront/events',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      return `${endpoint}/${inputVariables.eventId}/bookings`;
    },
    inputResolver: (inputVariables, inputParams) => {
      const {
        eventId,
        userId,
        isAdminCheckout,
        productId,
        variantId,
        orderId,
        price,
        customerEmail,
        contactEmail,
        contactPhone,
        orderProperties,
        quantity,
        firstName,
        lastName,
        employeeId,
        startDate,
        startTime,
        endTime,
        dateFormat,
        timeFormat,
        timezone,
        variantTitle,
        variantFullTitle,
      } = inputVariables;
      return {
        eventId: eventId,
        userId: userId,
        isAdminCheckout: isAdminCheckout,
        productId: productId,
        variantId: variantId,
        orderId: orderId,
        price: price,
        customerEmail: customerEmail,
        contactEmail: contactEmail,
        contactPhone: contactPhone,
        orderProperties: orderProperties,
        quantity: quantity,
        firstName: firstName,
        lastName: lastName,
        employeeId: employeeId,
        startDate: startDate,
        startTime: startTime,
        endTime: endTime,
        dateFormat: dateFormat,
        timeFormat: timeFormat,
        timezone: timezone,
        variantTitle: variantTitle,
        variantFullTitle: variantFullTitle,
      };
    },
    editableInputParams: {
      //  {"eventId":62845,"userId":41186,"isAdminCheckout":false,"productId":"8928656851234","variantId":"47670639296802","orderId":null,"price":0,"customerEmail":"<EMAIL>","contactEmail":"<EMAIL>","contactPhone":"918295041538","orderProperties":[{"name":"Event #","value":62845},{"name":"Booking Timezone","value":"Asia/Calcutta"},{"name":"Start Date","value":"Friday, May 10th 2024"},{"name":"Start Time","value":"1:40 pm"},{"name":"End Time","value":"2:10 pm"},{"name":"Booked With","value":"Vanilla candle Event   - 52026"}],"quantity":1,"firstName":"Sahil","lastName":"Goyal","employeeId":52026,"startDate":"2024-05-10","startTime":"1:40 pm","endTime":"2:10 pm","dateFormat":"YYYY-MM-DD","timeFormat":"h:mm a","timezone":"Asia/Calcutta","variantTitle":"Vanilla candle","variantFullTitle":"Vanilla candle"}
      eventId: '',
      userId: '',
      isAdminCheckout: '',
      productId: '',
      variantId: '',
      orderId: '',
      price: 0,
      customerEmail: '',
      contactEmail: '',
      contactPhone: '',
      orderProperties: '',
      quantity: 0,
      firstName: '',
      lastName: '',
      employeeId: '',
      startDate: '',
      startTime: '',
      endTime: '',
      dateFormat: '',
      timeFormat: '',
      timezone: '',
      variantTitle: '',
      variantFullTitle: '',
    },
    transformer: TransformData,
  },
};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'easyAppointment',
  type: 'datasource',
  name: 'Easy Appointment',
  description: '',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const EasyAppointmentEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://servicify-appointments.herokuapp.com/api',
      },
    },
  ],
};
const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<EasyAppointmentPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, EasyAppointmentPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: Partial<EasyAppointmentQueryDetails>,
  inputVariables: any,
  options: AppPageTriggerOptions = {},
) => {
  try {
    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = await queryRunner.runQuery(
          queryDetails.queryType,
          endpoint,
          {...typedDataVariables},
          {
            ...options,
          },
        );
      } catch (error) {
        logger.error('error', error);
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  } catch (ex) {
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [ex],
      hasError: true,
    };
  }
};

export async function runQuery(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
  const dsType = dsModelValues.get('pluginType');
  const dsModel = GetRegisteredPlugin(dsType);
  const querySchema = EasyAppointmentApiRecords[queryName];
  const queryDetails = EasyAppointmentApiRecords[queryName];
  if (!queryDetails) return;
  return await executeQuery(dsModel, dsConfig, dsModelValues, querySchema, {...inputVariables}, {});
}
export default wrapDatasourceModel({
  name: 'easyAppointment',
  config: {
    apiBaseUrl: 'https://servicify-appointments.herokuapp.com/api',
    queryRunner: 'queryrunner',
  } as EasyAppointmentPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<EasyAppointmentPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  _onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ): any {
    const pluginConfig = yield select(selectPluginConfig, pageKey, pluginId);
    const configGen = GetRegisteredConfig(pluginConfig.get('subtype'));
    const mergedConfig = configGen(pluginConfig.config);
    const dsConfig = pluginConfig.set('config', mergedConfig);

    var pageModels = state.stageModel.getModelValue([]);
    let dsModelValues = pageModels.get(dsConfig.get('id'));
    let queryRunner = dsModelValues?.get('queryRunner');

    queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  get onPluginUpdate() {
    return this._onPluginUpdate;
  },
  set onPluginUpdate(value) {
    this._onPluginUpdate = value;
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return EasyAppointmentApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      EasyAppointmentApiRecords && EasyAppointmentApiRecords[queryName] ? EasyAppointmentApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IEasyAppointmentCredentials,
  ): Partial<EasyAppointmentPluginConfigType> | boolean {
    const {apiBaseUrl} = credentials;
    if (!apiBaseUrl) return false;
    return {
      apiBaseUrl: apiBaseUrl,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'easyAppointment';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = EasyAppointmentApiRecords[queryName];
    if (!queryDetails) return;
    const response = yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
    return response;
  },
  options: {
    pluginListing,
  },
  editors: EasyAppointmentEditors,
});
