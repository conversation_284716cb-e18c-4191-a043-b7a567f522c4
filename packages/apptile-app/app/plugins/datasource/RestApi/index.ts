import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {GetProcessedInput, jsonToCookies, jsonToQueryString} from './utils';

export interface RestApiPluginConfigType {
  apiBaseUrl: string;
  queryRunner: any;
}

interface IEditableParams {
  endpoint: string;
  queryHeaders: Record<string, any>;
  queryParams: Record<string, any>;
  queryPayload?: Record<string, any>;
  queryCookies: Record<string, any>;
}

type RestApiQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver: (
    inputVariables: Record<string, any>,
    paginationMeta: any,
    isPaginated: boolean | undefined,
    getNextPage: boolean | undefined,
  ) => Record<string, any>;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseEditableInputParams: IEditableParams = {
  endpoint: '/',
  queryHeaders: {},
  queryParams: {},
  queryCookies: {},
};

const baseRestApiQuerySpec: Partial<RestApiQueryDetails> = {
  isPaginated: true,
  endpointResolver: (endpoint, inputParams) => endpoint,
  inputResolver: inputVariables => inputVariables,
  paginationResolver: (
    inputVariables: Record<string, any>,
    paginationMeta: any,
    isPaginated: boolean | undefined,
    getNextPage: boolean | undefined,
  ): Record<string, any> => {
    const {after} = paginationMeta ?? {};
    return isPaginated && getNextPage && after ? {...inputVariables, after} : inputVariables;
  },
};

const RestApiApiRecords: Record<string, Partial<RestApiQueryDetails>> = {
  Get: {
    queryType: 'get',
    editableInputParams: {
      ...baseEditableInputParams,
    },
    ...baseRestApiQuerySpec,
  },
  Post: {
    queryType: 'post',
    editableInputParams: {
      ...baseEditableInputParams,
      queryPayload: {},
    },
    ...baseRestApiQuerySpec,
  },
  Put: {
    queryType: 'put',
    editableInputParams: {
      ...baseEditableInputParams,
      queryPayload: {},
    },
    ...baseRestApiQuerySpec,
  },
  Patch: {
    queryType: 'patch',
    editableInputParams: {
      ...baseEditableInputParams,
      queryPayload: {},
    },
    ...baseRestApiQuerySpec,
  },
  Delete: {
    queryType: 'delete',
    editableInputParams: {
      ...baseEditableInputParams,
    },
    ...baseRestApiQuerySpec,
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'RestApi',
  type: 'datasource',
  name: 'RestApi Integration',
  description: 'RestApi Integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const RestApiEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base url',
        placeholder: 'https://api-v3.RestApi.io',
      },
    },
    {
      type: 'codeInput',
      name: 'authHeaders',
      props: {
        label: 'RestApi Headers',
        placeholder: '{"Authorization": "Basic XXXXX"}',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'RestApi',
  config: {
    apiBaseUrl: 'https://api-v3.RestApi.io',
    authHeaders: '',
    queryRunner: 'queryrunner',
  } as RestApiPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      let authHeaders = dsModelValues?.get('authHeaders') ?? {};
      try {
        if (typeof authHeaders === 'string') authHeaders = JSON.parse(authHeaders);
      } catch (e) {}
      config.headers = {...config.headers, ...authHeaders};
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return RestApiApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = RestApiApiRecords && RestApiApiRecords[queryName] ? RestApiApiRecords[queryName] : null;
    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = RestApiApiRecords[queryName];
      if (!queryDetails) return;

      const {endpointResolver, transformer} = queryDetails ?? {};
      let {endpoint, queryCookies, queryHeaders, queryParams} = inputVariables ?? {};

      const {contextInputParam, typedDataVariables} = GetProcessedInput(
        inputVariables,
        queryDetails,
        options,
        dsConfig,
        dsModelValues,
      );

      endpoint = endpointResolver && endpointResolver(endpoint, typedDataVariables);

      const cookiesHeader = queryCookies ? {Cookie: jsonToCookies(queryCookies)} : {};
      const queryString = jsonToQueryString(queryParams);
      let typedQueryPayload = typedDataVariables?.queryPayload ?? {};
      try {
        if (typeof typedQueryPayload === 'string') typedQueryPayload = JSON.parse(typedQueryPayload);
      } catch (e) {}

      const queryRunner = dsModelValues.get('queryRunner');
      const queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint + queryString,
        {
          ...typedQueryPayload,
          ...contextInputParam,
        },
        {
          headers: {
            ...cookiesHeader,
            ...queryHeaders,
          },
          t_client: new Date().valueOf(),
        },
        options,
      );

      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = rawData;
      let queryHasNextPage, paginationDetails;
      if (transformer) {
        const {data, hasNextPage, paginationMeta: pageData} = transformer(rawData);

        transformedData = data;
        queryHasNextPage = hasNextPage;
        paginationDetails = pageData;
      }
      // const data = transformedData;
      return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: RestApiEditors,
});
