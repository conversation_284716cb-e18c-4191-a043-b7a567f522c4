import {PluginConfigType} from 'apptile-core';
import {isObject} from 'lodash';
import {RestApiPluginConfigType} from '.';
import {AppPageTriggerOptions, PluginModelType} from 'apptile-core';

export const jsonToCookies = (cookies: Record<string, string>) => {
  let cok = '';
  cookies &&
    Object.entries(cookies).forEach(([key]) => {
      cok += `${key}=${cookies[key]}; `;
    });
  return cok ? cok : '';
};

export const jsonToQueryString = (json: Record<string, string>) => {
  return json
    ? '?' +
        Object.keys(json)
          .map(function (key) {
            return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);
          })
          .join('&')
    : '';
};

export const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  if (!contextInputParams) return () => {};
  return (dsConfig: PluginConfigType<RestApiPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, RestApiPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

export const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any} | undefined,
) => {
  if (!inputVariables) return inputVariables;

  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (typeof editableInputParams[key] === 'number') {
      return {
        ...acc,
        [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
      };
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
    return acc;
  }, {});
};

/**
 * Process Contextual Variables if any
 * Process the Input variables and return typesafe variables
 * Stiches the Pagination Parameters with input Params
 * @param inputVariables
 * @param queryDetails
 * @param options
 * @param dsConfig
 * @param dsModelValues
 * @returns
 */
export const GetProcessedInput = (
  inputVariables: any,
  queryDetails: any,
  options: AppPageTriggerOptions | undefined,
  dsConfig,
  dsModelValues,
) => {
  const {getNextPage, paginationMeta} = options ?? {};

  const {contextInputParams, editableInputParams, isPaginated, paginationResolver, inputResolver} = queryDetails ?? {};

  const contextInputParamResolver = makeInputParamsResolver(contextInputParams);
  const contextInputParam = contextInputParamResolver(dsConfig, dsModelValues);

  // inputResolver:: To handle nested input in graphql
  let typedInputVariables = inputVariables;
  Object.keys(inputVariables).forEach(key => {
    if (inputVariables[key] && isObject(inputVariables[key]) && editableInputParams[key]) {
      typedInputVariables[key] = makeInputVariablesTypeCompatible(inputVariables[key], editableInputParams[key]);
    }
  });

  typedInputVariables = paginationResolver
    ? paginationResolver(typedInputVariables, paginationMeta, isPaginated, getNextPage)
    : typedInputVariables;

  const typedDataVariables = inputResolver && inputResolver(typedInputVariables);

  return {contextInputParam, typedDataVariables};
};
