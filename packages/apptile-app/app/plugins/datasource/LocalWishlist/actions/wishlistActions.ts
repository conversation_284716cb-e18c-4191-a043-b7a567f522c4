import {<PERSON>Handler} from '../../../triggerAction';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import { LocalStorage } from 'apptile-core';
import {TransformedProduct} from '../types/index';
import _ from 'lodash';
import {
  isGidCheck,
  isInvalidId,
  checkForDuplicates,
  gidAppender,
  resolveNumericProductId,
  resolveGid,
} from '../helpers';
export interface WishistActionInterface {
  addProductToWishlist: ActionHandler;
  removeProductFromWishlist: ActionHandler;
  clearWishlist: ActionHandler; // New method
  refreshWishlist: ActionHandler; // New method
}

export interface wishlistProductsPayload {
  customerAccessToken: string;
}

export type wishlistRemoveProductsPayload = Omit<wishlistProductsPayload, 'productHandle'>;
export interface wishlistStoredProducts {
  id: string;
  handle: string;
}

class WishistActions implements WishistActionInterface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    keys: string[],
  ) {
    const modelUpdates = [] as Array<{selector: Array<string>; newValue: any}>;
    keys.forEach((key, index) => {
      const lineItemsSelector = selector.concat([key]);
      modelUpdates.push({
        selector: lineItemsSelector,
        newValue: lineItems[index],
      });
    });

    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  addProductToWishlist = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: wishlistProductsPayload,
  ) => {
    let {productId: rawProductId, productHandle} = params;
    const productId = resolveNumericProductId(rawProductId);

    if (isInvalidId(productId)) return;

    let wishlistProducts: Array<wishlistStoredProducts> = model?.get('productIds');

    let wishlistProductDetails = model?.get('products') as Array<TransformedProduct>;

    const productObj = {id: productId, handle: productHandle};

    if (!wishlistProducts) {
      wishlistProducts = [productObj];
    } else {
      //check duplicates in the local storage and if any, do not add it to local storage
      if (checkForDuplicates(productId, wishlistProducts)) {
        await this.applyChanges(
          dispatch,
          config,
          model,
          selector,

          [{actionName: '', value: ''}],
          ['lastTriggeredUpdate'],
        );
        return;
      } else {
        wishlistProducts.push(productObj);
      }
    }

    await this.applyChanges(
      dispatch,
      config,
      model,
      selector,

      [
        {actionName: 'addProduct', value: productObj},
        wishlistProducts,
        [...wishlistProductDetails, {...productObj, gid: resolveGid(productId)}],
      ],
      ['lastTriggeredUpdate', 'productIds', 'products'],
    );

    await LocalStorage.setValue('WishlistProducts', wishlistProducts);
  };

  removeProductFromWishlist = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
  ) => {
    const payload = params as wishlistRemoveProductsPayload;
    const {productId} = payload;

    if (isInvalidId(productId)) return;
    let gidproductId = productId;
    if (!isGidCheck(gidproductId)) gidproductId = gidAppender(productId);
    let numericProductId = resolveNumericProductId(productId);

    const wishlistProducts: Array<wishlistStoredProducts> = model.get('productIds');
    const filteredWishlist = wishlistProducts.filter(product => {
      if (product?.id == numericProductId) return false;
      if (product?.id == gidproductId) return false;
      return true;
    });

    let wishlistProductDetails = model.get('products') as Array<TransformedProduct>;
    const filteredWishlistDetails = _.filter(
      wishlistProductDetails,
      product => product?.id != numericProductId && product?.gid != gidproductId,
    );

    await this.applyChanges(
      dispatch,
      config,
      model,
      selector,
      [{actionName: 'removeProduct', value: {id: productId}}, [...filteredWishlist], [...filteredWishlistDetails]],
      ['lastTriggeredUpdate', 'productIds', 'products'],
    );
    await LocalStorage.setValue('WishlistProducts', filteredWishlist);
  };

  refreshWishlist = async (dispatch: any, config: PluginConfig, model: any, selector: Selector) => {
    let wishlistProducts: Array<wishlistStoredProducts> = model?.get('productIds');
    let wishlistProductDetails = model?.get('products') as Array<TransformedProduct>;
    if (!wishlistProducts) return;
    await this.applyChanges(
      dispatch,
      config,
      model,
      selector,
      [{actionName: 'refreshProducts', value: {}}, wishlistProducts, [...wishlistProductDetails]],
      ['lastTriggeredUpdate', 'productIds', 'products'],
    );
  };

  clearWishlist = async (dispatch: any, config: PluginConfig, model: any, selector: Selector) => {
    // Clear the wishlist by setting empty arrays
    const emptyWishlist: Array<wishlistStoredProducts> = [];
    const emptyWishlistDetails: Array<TransformedProduct> = [];

    await this.applyChanges(
      dispatch,
      config,
      model,
      selector,
      [{actionName: 'clearWishlist', value: {}}, emptyWishlist, emptyWishlistDetails],
      ['lastTriggeredUpdate', 'productIds', 'products'],
    );

    // Clear the wishlist in local storage
    await LocalStorage.setValue('WishlistProducts', emptyWishlist);
  };
}

const wishistActions = new WishistActions();
export default wishistActions;
