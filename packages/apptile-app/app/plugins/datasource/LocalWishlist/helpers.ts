import {ModelChange, Selector} from 'apptile-core';
import {WishlistProducts, TransformedProduct} from './types';
import _ from 'lodash';

const SHOPIFY_PRODUCT_GID_MATCH_REGEX = /^gid:\/\/shopify\/Product\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
const SHOPIFY_PRODUCT_GID_PREFIX = 'gid://shopify/Product/';
export function getPluginModelUpdate(pluginSelector: Selector, changes: Record<string, any>): any {
  let modelChanges: ModelChange[] = [];
  if (changes && !_.isEmpty(changes)) {
    Object.entries(changes).map(([key, value]) => {
      modelChanges.push({
        selector: pluginSelector.concat([key]),
        newValue: value,
      });
    });
  }
  return modelChanges;
}

export function productIdMapper(products: WishlistProducts) {
  let mappedProducts: string[] = [];

  for (let index = 0; index < products?.length; index++) {
    const product = products[index];
    if (!product || isInvalidId(product?.id)) continue; // this check is still needed as some of the localstorages in customers who use localwishlist v1 have null,empty objects,invalid formats already stored.
    let id = typeof product.id == 'number' ? product.id.toString() : product.id;
    if (!isGidCheck(id)) {
      id = `gid://shopify/Product/${id}`;
    } //used to tell backward compatibility transformer to not to store gid as number(gid.split('/).pop()) as part to backward compatibility

    mappedProducts.push(id);
  }
  return mappedProducts;
}

export function backWardCompatibilityTransformer(products: TransformedProduct[]) {
  const Transformedproducts: TransformedProduct[] = products.map((product, index) => {
    let {id} = product;
    product['gid'] = id;
    product['image'] = product['featuredImage'];

    id = id?.split('/')?.pop() ?? '';

    product['id'] = id;
    return product;
  });
  return Transformedproducts;
}

export function isGidCheck(productId: string | number) {
  if (!productId) return false;
  const gidRegEx = /^gid:\/\/shopify\/Product\/\d+/; // matches productId with pattern gid://shopify/Product/{any id}
  if (gidRegEx.test(productId?.toString())) return true;

  return false;
}

export function isInvalidId(productId: string | number) {
  if (!productId) return true; //null or undefined check on product Id
  const emptyStringRegex = /^[\w\d]+/i; // match characters that only begins with alphabets or numbers, any spaces etc are ignored
  if (!emptyStringRegex.test(productId.toString())) return true;

  return false;
}

export const resolveNumericProductId = (productId: string | number): string => {
  if (SHOPIFY_PRODUCT_GID_MATCH_REGEX.test(`${productId}`)) {
    return _.replace(`${productId}`, SHOPIFY_PRODUCT_GID_PREFIX, '');
  }
  return productId.toString();
};

export const resolveGid = (productId: string | number): string => {
  if (SHOPIFY_PRODUCT_GID_MATCH_REGEX.test(`${productId}`)) {
    return productId.toString();
  }
  return `${SHOPIFY_PRODUCT_GID_PREFIX}` + productId.toString();
};

export function checkForDuplicates(productId, wishlistProducts) {
  for (const existingWishlistProduct of wishlistProducts) {
    if (existingWishlistProduct?.id == productId) return true;
  }
  return false;
}

export function gidAppender(nonGidId: string) {
  return `gid://shopify/Product/${nonGidId}`;
}
