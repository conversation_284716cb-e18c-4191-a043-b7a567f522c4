import {call, put, select, delay, retry, spawn} from 'redux-saga/effects';
import {modelUpdateAction} from 'apptile-core';
import {getPluginModelUpdate} from './helpers';
import {selectAppConfig} from 'apptile-core';
import {GetRegisteredPlugin} from 'apptile-core';
import _ from 'lodash';
import {productIdMapper, backWardCompatibilityTransformer, isGidCheck, gidAppender} from './helpers';

export function* fetchProductsById(pluginSelector: string[], pluginId: string, productIds, pageLoad: boolean) {
  yield call(queryShopifyDataSource, pluginSelector, pluginId, productIds, pageLoad);
}

export function* queryShopifyDataSource(pluginSelector, pluginId, productIds, pageLoad) {
  const state = yield select();
  const datasource = 'shopify';
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(datasource);
  const appConfig = yield select(selectAppConfig);
  const dsConfig = appConfig.getPlugin('shopify');
  const dsType = dsModelValues.get('pluginType');

  const dsModel = GetRegisteredPlugin(dsType);

  const wishlistModel = state.stageModel.getModelValue([pluginId]);
  let products = wishlistModel?.get('products');
  // Added DSmodel check to avoid api call before shopify DS initialzed
  if (productIds.length === 0) return;
  else {
    // const modelUpdates = getPluginModelUpdate(pluginSelector, {
    //   products: products.map(product => {
    //     const newProduct = {...product};
    //     if (_.includes(productIds, product?.gid)) {
    //       newProduct._isFetched = false;
    //       newProduct._isFetching = true;
    //     }
    //     return product;
    //   }),
    // });
    // yield put(modelUpdateAction(modelUpdates));

    const {
      data: transformedData,
      errors,
      hasError,
    } = yield call(
      dsModel.runQuery,
      dsModel,
      dsConfig,
      dsModelValues,
      'GetProductByIds',
      {productIds},
      {
        transformers: undefined,
        getNextPage: false,
        paginationMeta: undefined,
        cachePolicy: null,
      },
    );

    yield call(updateWishlistProducts, productIds, transformedData, pageLoad, pluginSelector, pluginId);
  }
}

export function* updateWishlistProducts(productIds, transformedData, pageLoad, pluginSelector, pluginId) {
  const updatedstateSelect = yield select(); // fetching state again after network call to avoid any stale data from model

  const wishlistUpdatedModel = updatedstateSelect.stageModel.getModelValue([pluginId]);
  const updatedProductIds = wishlistUpdatedModel?.get('productIds');

  let products = pageLoad ? [] : (wishlistUpdatedModel?.get('products') as TransformedProduct[]);

  // removing the placeholders from the products array in wishlist Model
  if (!pageLoad) products = products.filter(product => !_.includes(productIds, product.gid));
  let filteredTransformedData = transformedData;
  const backwardTransformedData = backWardCompatibilityTransformer(transformedData);

  // filtering backwardTransformed data once again because if any remove action happens while api fetch, we do not add those removed productIds in the products array in wishlistModel
  if (!pageLoad) {
    filteredTransformedData = backwardTransformedData.filter(transformedProduct => {
      for (const product of updatedProductIds) {
        const productid = isGidCheck(product.id) ? product.id : gidAppender(product.id);
        if (productid === transformedProduct.gid) return true;
      }
      return false;
    });
  }

  const modelUpdates = getPluginModelUpdate(pluginSelector, {
    products: [...products, ...filteredTransformedData],
  });
  yield put(modelUpdateAction(modelUpdates));
}
