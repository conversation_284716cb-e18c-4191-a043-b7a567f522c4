import {PluginListingSettings, PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import wishistActions, {wishlistStoredProducts} from './actions/wishlistActions';
import {IWishlistProduct, WishlistProducts, TransformedProduct} from './types/index';
import { LocalStorage } from 'apptile-core';
import {RootState} from '@/root/app/store/RootReducer';
import {modelUpdateAction} from 'apptile-core';
import _ from 'lodash';
import {fetchProductsById} from './generators';
import {spawn, put} from 'redux-saga/effects';
import {AppPageTriggerOptions, connectPlugin, GetRegisteredPlugin, PluginModelChange} from 'apptile-core';
import {productIdMapper} from './helpers';
import {debounce} from 'lodash';
import {isGidCheck, gidAppender, getPluginModelUpdate, resolveGid, resolveNumericProductId} from './helpers';

export interface WishlistPluginConfigType extends DatasourcePluginConfig {
  appId: string;
  products: TransformedProduct[];
  productIds: WishlistProducts;
  getAllWishlistProducts: any;
  addProductToWishlist: any;
  removeProductFromWishlist: any;
  clearWishlist: any;
  refreshWishlist: any;
}

export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const propertySettings: PluginPropertySettings = {
  products: {
    updatesProps: ['checkWishlist'],
  },
  addProductToWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.addProductToWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', productObj: {}, customerAccessToken: ''},
    },
  },
  removeProductFromWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.removeProductFromWishlist;
    },
    actionMetadata: {
      editableInputParams: {productId: '', productHandle: '', customerAccessToken: ''},
    },
  },
  clearWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.clearWishlist;
    },
    actionMetadata: {
      editableInputParams: {}, // No input parameters needed for clearing the wishlist
    },
  },
  refreshWishlist: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return wishistActions.refreshWishlist;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
  checkWishlist: {
    getValue(model, renderedValue, selector) {
      // const wishListProducts = model[0]?.get('productIds') as Array<wishlistStoredProducts>;
      const wishListProducts2 = model?.productIds;

      return (rawProductId: string) => {
        if (!rawProductId) return false;
        const productId = resolveNumericProductId(rawProductId);
        const bool = wishListProducts2.find(product => product?.id === productId) ? true : false;
        return bool;
      };
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'localWishlist',
  type: 'datasource',
  name: 'Local Wishlist Integration',
  description: 'Local wishlist integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const wishlistEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'appId',
      defultValue: '',
      props: {
        label: 'App ID',
        placeholder: 'Apptile app id',
      },
    },
    {
      type: 'codeInput',
      name: 'addToWishlist',
      props: {
        label: 'Added to Wishlist',
        placeholder: 'Product added to the Wishlist',
      },
      isVisibleInV2: false,
    },
    {
      type: 'codeInput',
      name: 'removeFromWishlist',
      props: {
        label: 'Removed from Wishlist',
        placeholder: 'product Removed from the Wishlist',
      },
      isVisibleInV2: false,
    },
  ],
};

async function* fetchFromLocal() {
  yield LocalStorage.getValue('WishlistProducts');
}

export default wrapDatasourceModel({
  name: 'LocalWishlist',
  config: {
    ...baseDatasourceConfig,
    appId: '',
    lastTriggeredUpdate: {
      actionName: '',
      value: [],
    },
    isWishlistInitialized: '{{false}}',
    products: '{{[]}}',
    productIds: '{{[]}}',
    getAllWishlistProducts: 'action',
    addProductToWishlist: 'action',
    removeProductFromWishlist: 'action',
    clearWishlist: 'action',
    refreshWishlist: 'action',
    checkWishlist: 'function',
    queryRunner: '{{shopify.queryRunner}}',
  } as unknown as WishlistPluginConfigType,

  resolveCredentialConfigs: function (): Partial<any> | boolean {
    return {};
  },

  getQueries: function (): Record<string, any> {
    return {};
  },

  getQueryInputParams: function () {
    return {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'localWishlist';
  },

  onPluginUpdate: function* (
    state: RootState,
    pluginId: string,
    pageKey: string,
    instance: number | null,
    userTriggered: boolean,
    pageLoad: boolean,
    options: AppPageTriggerOptions,
  ) {
    const wishlistModel = state.stageModel.getModelValue([pluginId]);
    const shopifyQueryRunner = wishlistModel?.get('queryRunner')?.runQuery;
    const isLocalWishlistInitialized = wishlistModel?.get('isWishlistInitialized');
    const pluginSelector = [pluginId];
    if (!shopifyQueryRunner && isLocalWishlistInitialized == false && !wishlistModel?.get('productIds')) {
      return {
        modelUpdates: [
          {
            selector: pluginSelector.concat(['productIds']),
            newValue: [],
          },
          {
            selector: pluginSelector.concat(['products']),
            newValue: [],
          },
        ],
      };
    }

    if (shopifyQueryRunner && isLocalWishlistInitialized === false && wishlistModel?.get('productIds')?.length === 0) {
      const modelUpdates = getPluginModelUpdate(pluginSelector, {
        isWishlistInitialized: true,
      });
      yield put(modelUpdateAction(modelUpdates));
      let localWishlistproducts: Array<IWishlistProduct> | [];
      yield fetchFromLocal()
        .next()
        .then(({value}) => {
          localWishlistproducts = value || [];
        });

      const productIds = productIdMapper(localWishlistproducts);
      yield spawn(fetchProductsById, pluginSelector, pluginId, productIds, !isLocalWishlistInitialized);
      return {
        modelUpdates: [
          {
            selector: pluginSelector.concat(['lastTriggeredUpdate']),
            newValue: {actionName: 'addProduct', value: localWishlistproducts},
          },
          {
            selector: pluginSelector.concat(['productIds']),
            newValue: localWishlistproducts,
          },
          {
            selector: pluginSelector.concat(['products']),
            newValue: localWishlistproducts.map(product => {
              const newProduct = {...product};
              newProduct.gid = resolveGid(product?.id);
              return newProduct;
            }),
          },
        ],
      };
    }

    const lastTriggeredUpdate = wishlistModel?.get('lastTriggeredUpdate');

    if (shopifyQueryRunner && lastTriggeredUpdate?.actionName === 'addProduct') {
      const wishlistModel = state.stageModel.getModelValue([pluginId]);
      const lastTriggeredUpdate = wishlistModel?.get('lastTriggeredUpdate');
      let productIds = wishlistModel?.get('productIds');
      productIds = productIdMapper(productIds);
      yield spawn(fetchProductsById, pluginSelector, pluginId, productIds, !isLocalWishlistInitialized);
    }

    if (shopifyQueryRunner && lastTriggeredUpdate?.actionName === 'refreshProducts') {
      const wishlistModel = state.stageModel.getModelValue([pluginId]);
      const lastTriggeredUpdate = wishlistModel?.get('lastTriggeredUpdate');
      let productIds = wishlistModel?.get('productIds');
      productIds = productIdMapper(productIds);
      yield spawn(fetchProductsById, pluginSelector, pluginId, productIds, !isLocalWishlistInitialized);
    }
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: wishlistEditors,
});
