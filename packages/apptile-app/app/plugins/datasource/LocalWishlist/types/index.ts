import {IProduct as Product} from '../../ShopifyV_22_10/types';

export interface IProduct {
  customerEmail: string;
  customerId: number;
  productId: number;
  productHandle: string;
}

export interface IWishlistProduct {
  handle: string;
  id: number;
}

export type WishlistProducts = Array<IWishlistProduct>;
export interface TransformedProduct extends Product {
  image: string;
  gid: string;
}
