import {modelUpdateAction} from 'apptile-core';
import {ApptileMFAuthApiRecords, executeQuery} from '..';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {PluginModelChange} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';

export interface AuthActionInteface {
  SignIn: ActionHandler;
  SignUp: ActionHandler;
  OtpVerify: ActionHandler;
  PasswordVerify: ActionHandler;
}

interface ISignInParams {
  email: string;
  phone: number;
}

interface ISignUpParams {
  email: string;
  firstname?: string;
  lastname?: string;
}

interface IOtpVerifyParams {
  otp: string;
}
interface IPasswordVerifyParams {
  password: string;
}
interface IAuthResponse {
  status: string;
  accessToken?: string;
  user?: {
    email: string;
  };
  message?: string;
}

export enum PhoneLoginStateEnum {
  ACCOUNT_CREATION_REQUIRED = 'ACCOUNT_CREATION_REQUIRED',
  PASSWORD_REQUIRED = 'PASSWORD_REQUIRED',
  OTP_GENERATED = 'OTP_GENERATED',
}

export enum MFAuthEventsEnum {
  ACCOUNT_CREATION_REQUIRED = 'onSignupRequired',
  PASSWORD_REQUIRED = 'onPasswordRequire',
  OTP_GENERATED = 'onOtpGenerated',
  SUCCESS = 'onSuccess',
  ERROR = 'onError',
  USER_ALREADY_EXISTS = 'onExistingUser',
}

class AuthActions implements AuthActionInteface {
  private async runQuery(
    config: PluginConfig,
    model: any,
    queryName: keyof typeof ApptileMFAuthApiRecords,
    inputVariables: any,
  ) {
    return await executeQuery(model, config, model, queryName, inputVariables);
  }

  private errorHandler = (error: any, selector: string[]) => {
    let eventToBeTriggered = null;

    const errorObject = error?.response ?? null;
    if (!errorObject?.status) {
      eventToBeTriggered = MFAuthEventsEnum.ERROR;
    }
    switch (errorObject?.status) {
      case 409:
        eventToBeTriggered = MFAuthEventsEnum.USER_ALREADY_EXISTS;
        break;
      case 400:
      default:
        eventToBeTriggered = MFAuthEventsEnum.ERROR;
        break;
    }

    const errorMessage = errorObject?.data?.message ?? 'Unknown Error Occured';
    const modelUpdates: PluginModelChange[] = [
      {
        selector: selector.concat(['data']),
        newValue: null,
      },
      {
        selector: selector.concat(['rawData']),
        newValue: null,
      },
      {
        selector: selector.concat(['errors']),
        newValue: [errorMessage],
      },
      {
        selector: selector.concat(['hasError']),
        newValue: !!error,
      },
      {
        selector: selector.concat('loading'),
        newValue: false,
      },
    ];

    return {modelUpdates, eventToBeTriggered};
  };

  private handleResponse = (dispatch: any, config: PluginConfig, queryResponse: any, selector: string[]) => {
    const responseData: IAuthResponse = queryResponse?.data ?? null;
    const modelUpdates: PluginModelChange[] = [
      {
        selector: selector.concat(['data']),
        newValue: responseData,
      },
      {
        selector: selector.concat(['rawData']),
        newValue: responseData,
      },
      {
        selector: selector.concat(['errors']),
        newValue: [],
      },
      {
        selector: selector.concat(['hasError']),
        newValue: false,
      },
      {
        selector: selector.concat('loading'),
        newValue: false,
      },
    ];
    let eventToBeTriggered = null;
    switch (responseData?.status) {
      case PhoneLoginStateEnum.ACCOUNT_CREATION_REQUIRED:
        eventToBeTriggered = MFAuthEventsEnum.ACCOUNT_CREATION_REQUIRED;
        break;
      case PhoneLoginStateEnum.PASSWORD_REQUIRED:
        eventToBeTriggered = MFAuthEventsEnum.PASSWORD_REQUIRED;
        break;
      case PhoneLoginStateEnum.OTP_GENERATED:
        eventToBeTriggered = MFAuthEventsEnum.OTP_GENERATED;
        break;
      default:
        if (responseData?.accessToken) {
          eventToBeTriggered = MFAuthEventsEnum.SUCCESS;
        } else {
          eventToBeTriggered = MFAuthEventsEnum.ERROR;
        }
    }
    if (responseData?.user?.email) {
      this.updateDatasourceModel(dispatch, config, [{selector: ['maskedEmail'], newValue: responseData.user?.email}]);
    }
    return {modelUpdates, eventToBeTriggered};
  };

  private updateDatasourceModel = (dispatch: any, config: PluginConfig, modelUpdates: PluginModelChange[]) => {
    const datasource = config?.config?.get('datasource');
    if (!datasource) {
      logger.info('Unable to find Datasource');
    }
    const datasourceSelector = [datasource];
    const datasourceUpdates = modelUpdates.map((modelUpdate: PluginModelChange) => {
      return {
        selector: datasourceSelector.concat(modelUpdate.selector),
        newValue: modelUpdate.newValue,
      };
    });
    dispatch(modelUpdateAction(datasourceUpdates));
  };

  SignIn = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: ISignInParams) => {
    try {
      const queryResponse = await this.runQuery(config, model, 'SignIn', params);
      this.updateDatasourceModel(dispatch, config, [{selector: ['phone'], newValue: params?.phone}]);
      return this.handleResponse(dispatch, config, queryResponse, selector);
    } catch (error) {
      logger.error(`while SignIn`, error);
      return this.errorHandler(error, selector);
    }
  };
  SignUp = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: ISignUpParams) => {
    try {
      const queryResponse = await this.runQuery(config, model, 'SignUp', params);
      const {email} = params;
      this.updateDatasourceModel(dispatch, config, [{selector: ['email'], newValue: email}]);
      return this.handleResponse(dispatch, config, queryResponse, selector);
    } catch (error) {
      logger.error(`while SignUp`, error);
      return this.errorHandler(error, selector);
    }
  };
  OtpVerify = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: IOtpVerifyParams) => {
    try {
      const queryResponse = await this.runQuery(config, model, 'OtpVerify', params);
      return this.handleResponse(dispatch, config, queryResponse, selector);
    } catch (error) {
      logger.error(`while OtpVerify`, error);
      return this.errorHandler(error, selector);
    }
  };
  PasswordVerify = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: IPasswordVerifyParams,
  ) => {
    try {
      const queryResponse = await this.runQuery(config, model, 'PasswordVerify', params);
      return this.handleResponse(dispatch, config, queryResponse, selector);
    } catch (error) {
      logger.error(`while PasswordVerify`, error);
      return this.errorHandler(error, selector);
    }
  };
}

const authActions = new AuthActions();
export default authActions;
