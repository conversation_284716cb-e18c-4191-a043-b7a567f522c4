import axios from 'axios';
import {Buffer} from 'buffer';

export function getQueryParamByNameFromUrl(url: string, name: string) {
  name = name.replace(/[\[\]]/g, '\\$&');
  var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
    results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

export function parseSimplyOTPJwt(token: string | null) {
  var base64Payload = token?.split('.')[1];
  if (!base64Payload) throw new Error(`invlid token ${token}`);
  var payload = Buffer.from(base64Payload, 'base64');
  return JSON.parse(payload.toString());
}

//hor passing in header auth of all apis
export async function isJWTValid(token: any | null, proxyUrl: string, appId: string): Promise<boolean> {
  if (!token) {
    return false;
  }
  try {
    const response = await axios.post(
      `${proxyUrl}/generate-jwt/verify`,
      { token },
      {
        headers: {
          "Content-Type": "application/json",
          "x-shopify-app-id": appId,
        },
      }
    );
    return response.data.valid;
  } catch (error) {
    return false; 
  }
}

