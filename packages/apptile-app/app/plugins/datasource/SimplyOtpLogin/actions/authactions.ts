import {modelUpdateAction} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';

import {triggerAction} from 'apptile-core';
import _ from 'lodash';
import {ModelChange, Selector} from 'apptile-core';
import {AppModelType, PluginConfig} from 'apptile-core';
import {
  GetRegisteredPlugin,
  GetRegisteredPluginInfo,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {executeQuery} from '../index';
import {ISimplyOTPLoginDsPluginConfigType, ISimplyOTPLoginInterface} from '../types';
import {getQueryParamByNameFromUrl, parseSimplyOTPJwt} from './utils';

export const simplyOTPLoginActionDsPluginConfig: ISimplyOTPLoginDsPluginConfigType = {
  sendOTP: TriggerActionIdentifier,
  verifyOTP: TriggerActionIdentifier,
  updateCustomer: TriggerActionIdentifier,
  resetSignInSteps: TriggerActionIdentifier,
  currentSignInStep: 'SEND_OTP',
};

export const simplyOTPLoginActionDatasourceEditor: PluginEditorsConfig<any> = {};

export const simplyOTPLoginActionDsPropertySettings: PluginPropertySettings = {
  sendOTP: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return simplyOTPLogin.sendOTP;
    },
    actionMetadata: {
      editableInputParams: {
        type: '',
        username: '',
        countryCode: '',
        recaptchatoken: '',
        shopDomain:''
      },
    },
  },
  verifyOTP: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return simplyOTPLogin.verifyOTP;
    },
    actionMetadata: {
      editableInputParams: {
        otp: '',
        successMessage: '',
        skipPostAuthRedirect: false,
        forceUpdateProfile: false,
        shopDomain:''
      },
    },
  },
  updateCustomer: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return simplyOTPLogin.updateCustomer;
    },
    actionMetadata: {
      editableInputParams: {
        email: '',
        phone: '',
        firstname: '',
        lastname: '',
        successMessage: '',
        shopDomain:'',
        acceptEmailMarketing: 'true or false',
        acceptSmsMarketing: 'true or false',
        acceptWhatsappMarketing: 'true or false'
      },
    },
  },
  resetSignInSteps: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return simplyOTPLogin.resetSignInSteps;
    },
    actionMetadata: {
      editableInputParams: {},
    },
  },
};

export interface IUpdateCustomer {
  firstname: string;
  lastname: string;
  email: string;
  successMessage: string;
  shopDomain: string
}

class SimplyOTPLogin implements ISimplyOTPLoginInterface {
  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];

    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }

  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('simplyOTPLogin');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  private async setSuccessMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    message: string,
  ) {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });

    toast.show(message, {
      type: 'success',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private setBadParamErrorMessage(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    skipPostAuthRedirect: boolean,
    customerData: any,
    errorMessage: string,
    customErrorMessage: string,
  ) {
    const errorString = customErrorMessage ? customErrorMessage : errorMessage;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['authLoader']),
      newValue: false,
    });
    _.entries(customerData || []).map(([selectorKey, newValue]) =>
      newModelUpdates.push({
        selector: selector.concat([selectorKey]),
        newValue: newValue,
      }),
    );

    toast.show(errorString, {
      type: 'error',
      placement: 'bottom',
      duration: 2000,
      style: {marginBottom: 80},
    });
    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  }

  private requestShopifyUserSignIn = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: AppModelType,
    email: string,
    password: string,
    successMessage: string,
    skipPostAuthRedirect?: boolean,
  ) => {
    try {
      const payload = {
        pluginConfig: appConfig.getPlugin('shopify'),
        pluginModel: appModel.getPluginModel('', 'shopify'),
        pluginSelector: ['shopify'],
        eventModelJS: {
          value: 'signInUser',
          params: {
            email: email,
            password: password,
            successMessage,
            // badParameterMessage: '',
            // errorMessage: '',
            skipPostAuthRedirect: !!skipPostAuthRedirect,
          },
        },
      };

      dispatch(triggerAction(payload));
    } catch (error) {
      console.log(`Error while triggering requestShopifyUserSignIn`, error);
    }
  };

  private loginUser = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
    redirectUrl: string,
    successMessage: string,
    skipPostAuthRedirect?: boolean,
    verifyOtpResponseData?: any,
  ) => {
    if (_.isEmpty(redirectUrl)) {
      // SIGNUP PROCESS
      const customerDetailModelUpdates = [
        {
          selector: selector.concat(['currentSignInStep']),
          newValue: 'UPDATE_CUSTOMER',
        },
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(customerDetailModelUpdates, undefined, true));
    } else {
      // Activate account
      const isActivationUrl = redirectUrl.includes('account/activate');

      if (isActivationUrl) {
        try {
          const payload = {
            pluginConfig: appConfig.getPlugin('shopify'),
            pluginModel: appModel.getPluginModel('', 'shopify'),
            pluginSelector: ['shopify'],
            eventModelJS: {
              value: 'customerActivateByUrl',
              params: {
                activationUrl: redirectUrl,
                password: Date.now().toString(),
                successMessage,
                // badParameterMessage: '',
                // errorMessage: '',
                skipPostAuthRedirect: !!skipPostAuthRedirect,
              },
            },
          };

          dispatch(triggerAction(payload));
        } catch (error) {
          this.setBadParamErrorMessage(
            dispatch,
            config,
            model,
            selector,
            true,
            {},
            'Unknown error occured. Please try again.',
            'Unknown error occured. Please try again.',
          );
          console.log(`Error while activating account`, error);
        }
      } else {
        // SIGNIN PROCESS
        const encodedLoginToken = getQueryParamByNameFromUrl(redirectUrl, 'logintoken');
        const loginToken = parseSimplyOTPJwt(encodedLoginToken);

        const username = loginToken?.jti;
        const password = loginToken?.iss;

        this.requestShopifyUserSignIn(
          dispatch,
          config,
          model,
          selector,
          params,
          appConfig,
          appModel,
          username,
          password,
          successMessage,
          skipPostAuthRedirect,
        );
      }

      const customerDetailModelUpdates = [
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(customerDetailModelUpdates, undefined, true));
    }
  };

  sendOTP = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {type, username, countryCode, recaptchatoken,shopDomain} = params;

    const setAuthLoading = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(setAuthLoading, undefined, true));

    const {data: signInResponse, errors} = await this.queryExecutor(model, config, 'sendOTP', {
      type,
      username: type === 'phone' ? `+${countryCode}${username}` : username,
      recaptchatoken: recaptchatoken,
      shopDomain
    });

    if (signInResponse.status === 500 || signInResponse.status !== 200) {
      console.error(`Error while sending OTP ${errors}`);
      const errorMessage = _.first(errors)?.message || `An Unknown error occured!`;
      this.setBadParamErrorMessage(dispatch, config, model, selector, true, {}, errorMessage, '');
      return;
    }

    const customerDetailModelUpdates = [
      {
        selector: selector.concat(['type']),
        newValue: type,
      },
      {
        selector: selector.concat(['username']),
        newValue: username,
      },
      {
        selector: selector.concat(['countryCode']),
        newValue: countryCode,
      },
      {
        selector: selector.concat(['otpId']),
        newValue: signInResponse?.data?.otpId,
      },
      {
        selector: selector.concat(['currentSignInStep']),
        newValue: 'VERIFY_OTP',
      },
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },
      {
        selector: selector.concat(['recaptchatoken']),
        newValue: recaptchatoken,
      },
    ];
    dispatch(modelUpdateAction(customerDetailModelUpdates, undefined, true));
  };

  verifyOTP = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {otp, successMessage, skipPostAuthRedirect, forceUpdateProfile,shopDomain} = params;

    const setAuthLoading = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(setAuthLoading, undefined, true));

    const type = model.get('type');
    const username = model?.get('username');
    const countryCode = model?.get('countryCode');
    const otpId = model.get('otpId');

    const {data: verifyOtpResp, errors} = await this.queryExecutor(model, config, 'verifyOTP', {
      otp,
      type,
      username: type === 'phone' ? `+${countryCode}${username}` : username,
      otpId,
      forceUpdateProfile,
      shopDomain
    });

    if (verifyOtpResp.status === 500 || verifyOtpResp.status !== 200) {
      console.error(`Error while verifying OTP ${errors}`);
      const errorMessage = _.first(errors)?.message || verifyOtpResp.message || `An Unknown error occured!`;
      this.setBadParamErrorMessage(dispatch, config, model, selector, true, {}, errorMessage, '');
      let authLoadingState = [
        {
          selector: selector.concat(['authLoader']),
          newValue: false,
        },
      ];
      dispatch(modelUpdateAction(authLoadingState, undefined, true));
      return;
    }

    const redirect_url = verifyOtpResp?.data.redirect_url;
    this.loginUser(
      dispatch,
      config,
      model,
      selector,
      params,
      appConfig,
      appModel,
      redirect_url,
      successMessage,
      skipPostAuthRedirect,
      verifyOtpResp.data,
    );
  };

  updateCustomer = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const {
      email,
      firstname,
      lastname,
      successMessage,
      phone,
      shopDomain,
      acceptEmailMarketing,
      acceptSmsMarketing,
      acceptWhatsappMarketing,
    } = params;

    const setAuthLoading = [
      {
        selector: selector.concat(['authLoader']),
        newValue: true,
      },
    ];
    dispatch(modelUpdateAction(setAuthLoading, undefined, true));

    const otpId = model.get('otpId');
    const countryCode = model.get('countryCode');

    const updateCustomerPayload: Record<string, string> = {
      firstname,
      lastname,
      otpId,
      email,
      acceptEmailMarketing,
      acceptSmsMarketing,
      acceptWhatsappMarketing,
      shopDomain
    };

    if (!_.isEmpty(phone)) {
      updateCustomerPayload.phone = `+${countryCode}${phone}`;
    }

    const {data: updateCustomerResp, errors} = await this.queryExecutor(
      model,
      config,
      'updateCustomerDetails',
      updateCustomerPayload,
    );

    if (updateCustomerResp.status === 500 || updateCustomerResp.status !== 200) {
      console.error(`Error while updating customer ${errors}`);
      const errorMessage = updateCustomerResp?.message ? updateCustomerResp.message : (_.first(errors)?.message || `An Unknown error occured!`);
      this.setBadParamErrorMessage(dispatch, config, model, selector, true, {}, errorMessage, '');
      return;
    }

    const redirect_url = updateCustomerResp?.data?.redirect_url;
    this.loginUser(
      dispatch,
      config,
      model,
      selector,
      params,
      appConfig,
      appModel,
      redirect_url,
      successMessage,
      undefined,
      updateCustomerResp.data,
    );
  };

  resetSignInSteps = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const modelUpdates = [
      {
        selector: selector.concat(['currentSignInStep']),
        newValue: '',
      },
      {
        selector: selector.concat(['authLoader']),
        newValue: false,
      },
    ];

    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const simplyOTPLogin = new SimplyOTPLogin();
export default simplyOTPLogin;
