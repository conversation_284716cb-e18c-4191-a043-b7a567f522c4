import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, ISimplyOTPLoginCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {simplyOTPLoginActionDsPluginConfig, simplyOTPLoginActionDsPropertySettings} from './actions/authactions';
import {LocalStorage} from 'apptile-core';
import {isJWTValid} from './actions/utils';
import axios from 'axios';
import _ from 'lodash-es'

export type SimplyOTPLoginConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  apiBaseUrl: string;
  apiVersion: string;
  proxyUrl: string;
  appId: string;
};

type SimplyOTPLoginQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  headersResolver?: (inputVariables: any) => any;
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any} | undefined,
) => {
  if (!inputVariables) return inputVariables;

  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (typeof editableInputParams[key] === 'number') {
      return {
        ...acc,
        [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
      };
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
    return acc;
  }, {});
};

const simplyOTPLoginApiRecords: Record<string, SimplyOTPLoginQueryDetails> = {
  sendOTP: {
    queryType: 'post',
    endpoint: '/simplyotplogin',
    endpointResolver: (endpoint, inputVariables) => {
      const {apiVersion} = inputVariables;
      return `${endpoint}/${apiVersion}/otp`;
    },
    editableInputParams: {
      type: '',
      username: '',
      recaptchatoken: '',
      shopDomain: ''
    },
    inputResolver(inputVariables) {
      const {type, username, countryCode, recaptchatoken} = inputVariables;
      const inputs = {
        username: username,
        type: type,
        recaptcha_token: _.isEmpty(recaptchatoken) ? '' : recaptchatoken,
      };
      return inputs;
    },
    headersResolver(inputVariables) {
      return {
        action: 'sendOTP',
        shop_name: inputVariables.shopDomain
      };
    },
  },
  verifyOTP: {
    queryType: 'post',
    endpoint: '/simplyotplogin',
    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {apiVersion} = inputVariables;
      return `${endpoint}/${apiVersion}/otp`;
    },
    editableInputParams: {
      type: '',
      username: '',
      otp: '',
      otpId: '',
      forceUpdateProfile: false,
      shopDomain: '',
    },
    inputResolver(inputVariables) {
      const {type, username, otp, otpId, forceUpdateProfile} = inputVariables;
      const inputs = {
        username: username,
        type: type,
        otp: otp,
        otp_id: otpId
      };

      return forceUpdateProfile
        ? inputs
        : {
            ...inputs,
            force_update_profile: false,
          };
    },
    headersResolver(inputVariables) {
      return {
        action: 'verifyOTP',
        shop_name: inputVariables.shopDomain
      };
    },
  },
  updateCustomerDetails: {
    queryType: 'post',
    endpoint: '/simplyotplogin',

    endpointResolver: (endpoint: string, inputVariables: any) => {
      const {apiVersion} = inputVariables;
      return `${endpoint}/${apiVersion}/otp`;
    },
    editableInputParams: {
      firstname: '',
      lastname: '',
      email: '',
      otpId: '',
      phone: '',
      acceptEmailMarketing: 'true or false',
      acceptSmsMarketing: 'true or false',
      acceptWhatsappMarketing: 'true or false',
      shopDomain: ''
    },
    inputResolver(inputVariables) {
      const {
        otpId,
        firstname,
        lastname,
        phone,
        email,
        acceptEmailMarketing,
        acceptSmsMarketing,
        acceptWhatsappMarketing,
      } = inputVariables;
      const inputs = {
        first_name: firstname,
        last_name: lastname,
        otp_id: otpId,
      };

      if (phone) {
        inputs['phone_no'] = phone;
      }
      if (email) {
        inputs['email'] = email;
      }
      if (acceptEmailMarketing) {
        inputs['accept_email_marketing'] = acceptEmailMarketing;
      }
      if (acceptSmsMarketing) {
        inputs['accept_sms_marketing'] = acceptSmsMarketing;
      }
      if (acceptWhatsappMarketing) {
        inputs['accept_whatsapp_marketing'] = acceptWhatsappMarketing;
      }

      return inputs;
    },
    headersResolver(inputVariables) {
      return {
        action: 'updateEmail',
        shop_name: inputVariables.shopDomain
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {
  ...simplyOTPLoginActionDsPropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'simplyOTPLogin',
  type: 'datasource',
  name: 'Simply OTP Login',
  description: 'Simply OTP Login',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const SimplyOTPLoginEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Url',
        placeholder: 'https://omqkhavcch.execute-api.ap-south-1.amazonaws.com',
      },
    },
    {
      type: 'codeInput',
      name: 'apiVersion',
      props: {
        label: 'Api Version',
        placeholder: 'v6',
      },
    },
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'SimplyOtp Proxy Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
        placeholder: '',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let {endpointResolver, headersResolver, editableInputParams, endpoint} = queryDetails ?? {};

    const apiVersion = dsConfig.config?.get('apiVersion');

    endpoint = endpointResolver && endpointResolver(endpoint, {...inputVariables, apiVersion});

    const headers = headersResolver && headersResolver(inputVariables);

    const queryRunner = dsModelValues.get('queryRunner');

    const typedInputs = makeInputVariablesTypeCompatible(inputVariables, editableInputParams);
    const typedInputVariables = queryDetails.inputResolver ? queryDetails.inputResolver(typedInputs) : typedInputs;

    const proxyUrl = dsModelValues?.get('proxyUrl') ?? null;
    const appId = dsConfig.config?.get('appId') ?? null;

    let simplyOtpJwtToken = await LocalStorage.getValue('SIMPLY_OTP_JWT_TOKEN');

    const isValidJwt = await isJWTValid(simplyOtpJwtToken, proxyUrl, appId);

    if (!isValidJwt) {
      await axios
        .get(`${proxyUrl}/generate-jwt`, {
          headers: {
            'Content-Type': 'application/json',
            'x-shopify-app-id': appId,
          },
        })
        .then(response => {
          simplyOtpJwtToken = response.data.token;
          LocalStorage.setValue('SIMPLY_OTP_JWT_TOKEN', response.data.token);
        })
        .catch(error => {
          console.error('Error: generating token', error);
        });
    }

    const queryResponse = await queryRunner.runQuery(queryDetails.queryType, endpoint, typedInputVariables, {
      headers: {...headers, Authorization: `Bearer ${simplyOtpJwtToken}`, 'Content-Type': 'application/json'},
    });

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    return {rawData, data: transformedData, hasNextPage: false, paginationMeta: null};
  } catch (error) {
    console.error(error);
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: error?.response?.data?.message,
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'simplyOTPLogin',
  config: {
    ...baseDatasourceConfig,
    queryRunner: 'queryrunner',
    authLoader: 'false',
    apiBaseUrl: '=',
    apiVersion: '',
    proxyUrl: 'https://api.apptile.io/simply-otp',
    appId: '',
    ...simplyOTPLoginActionDsPluginConfig,
  } as SimplyOTPLoginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<SimplyOTPLoginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsModelValues?.get('apiBaseUrl');
    // const shopDomain = dsModelValues?.get('shopDomain');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
        ...{
          'Content-Type': 'application/json',
          // shop_name: shopDomain,
        },
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return simplyOTPLoginApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      simplyOTPLoginApiRecords && simplyOTPLoginApiRecords[queryName] ? simplyOTPLoginApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: ISimplyOTPLoginCredentials,
  ): Partial<SimplyOTPLoginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'simplyOtp';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = simplyOTPLoginApiRecords[queryName];
    if (!queryDetails) return;
    return yield executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: SimplyOTPLoginEditors,
});
