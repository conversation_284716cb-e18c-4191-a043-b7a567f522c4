import _ from 'lodash';

export const TransformedListGeneratedCupons: TransformerFunction<any, TransformedListGeneratedCuponsResponse[]> = (
  data,
  paginationMeta?: any,
) => {
  // use data as a key to show result in editor eg (data : data.transactions ....)
  return {
    data: data.transactions
      .filter(transaction => transaction.resource === 'claimed_reward')
      .map(transaction => ({
        id: transaction.id,
        createdAt: transaction.created_at,
        customerId: transaction.customer.id,
        customerEmail: transaction.customer.email,
        merchantId: transaction.customer.merchant_id,
        pointsApproved: transaction.customer.points_approved,
        pointsPending: transaction.customer.points_pending,
        pointsSpent: transaction.customer.points_spent,
        pointsChange: transaction.points_approved || transaction.points_pending,
        resource: transaction.resource,
        rewardId: transaction.claimed_reward.reward.id,
        rewardTitle: transaction.claimed_reward.reward.title,
        rewardDescription: transaction.claimed_reward.reward.description,
        rewardSiteId: transaction.claimed_reward.reward.site_id,
        rewardUrl: transaction.claimed_reward.reward.target_site.url,
        rewardSortKey: transaction.claimed_reward.reward.sort_key,
        rewardKind: transaction.claimed_reward.reward.kind,
        rewardMethod: transaction.claimed_reward.reward.method,
        rewardDiscountType: transaction.claimed_reward.reward.discount_type,
        rewardDiscountAmount: transaction.claimed_reward.reward.discount_amount,
        redeemableType: transaction.claimed_reward.redeemable.redeemable_type,
        voucherCode: transaction.claimed_reward.redeemable.code,
        voucherUsedAt: transaction.claimed_reward.redeemable.used_at,
        voucherUsageCount: transaction.claimed_reward.redeemable.usage_count,
        voucherDiscountAmount: transaction.claimed_reward.redeemable.discount_amount,
        voucherUsageLimit: transaction.claimed_reward.redeemable.usage_limit,
        voucherState: transaction.claimed_reward.state,
        expiresAt: transaction.claimed_reward.expires_at,
        adjustmentNote: transaction.adjustment ? transaction.adjustment.note : null,
      })),
  };
};
