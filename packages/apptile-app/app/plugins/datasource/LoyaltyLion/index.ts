import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from '../../../common/datatypes/types';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, ILoyaltyLionCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';

import {TransformedListGeneratedCupons} from './transformers';
import {baseGestureHandlerWithMonitorProps} from 'react-native-gesture-handler/lib/typescript/handlers/gestureHandlerCommon';

export type LoyaltyLionConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  appId: string;
  apiBaseUrl: string;
  proxyUrl: string;
  username: string;
  password: string;
};

// Following snippet initiates the base for API calls
type LoyaltyLionQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
};

// Following API Record snippet helps initializing all the API calls required, eg : chooseVoucher,listGeneratedCoupons,generateCouponCode
const LoyaltyLionApiRecords: Record<string, LoyaltyLionQueryDetails> = {
  chooseVoucher: {
    queryType: 'get',
    // endpoint: `customers/${inputParams?.merchantId}/available_rewards`,
    endpoint: '/customers/{merchantId}/available_rewards',
    // The endpointResolver is used to make changes in the endpoint if required.
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  listGeneratedCoupons: {
    queryType: 'get',
    endpoint: '/customers/{merchantId}/transactions',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
    transformer: TransformedListGeneratedCupons,
  },
  listTransactions: {
    queryType: 'get',
    endpoint: '/customers/{merchantId}/transactions',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  getCustomerDetails: {
    queryType: 'get',
    endpoint: '/customers?email={email}',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{email}', inputParams?.email);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      email: '',
      shopifyCustomerAccessToken: '',
    },
    isPaginated: false,
  },
  saveBirthday: {
    // Add/Save birthday code
    queryType: 'patch',
    endpoint: '/customers/{merchantId}',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      birthday: '',
      merchantId: '',
      shopifyCustomerAccessToken: '',
    },
    // inputResolver is used to send the request body data in case of POST/PATCH call
    inputResolver: inputVariables => {
      return {
        customer: {
          birthday: inputVariables.birthday,
        },
      };
    },
    isPaginated: false,
  },
  registerActivities: {
    // Add/Save activities like instagram follow, facebook like etc
    queryType: 'post',
    endpoint: '/activities',
    editableInputParams: {
      activityName: '',
      merchantId: '',
      customerId: '',
      email: '',
      shopifyCustomerAccessToken: '',
    },
    // inputResolver is used to send the request body data in case of POST/PATCH call
    inputResolver: inputVariables => {
      return {
        name: inputVariables.activityName,
        customer_email: inputVariables.email,
        customer_id: inputVariables.customerId,
        merchant_id: inputVariables.merchantId,
        state: 'approved',
      };
    },
    isPaginated: false,
  },
  generateCoupon: {
    // Add points code
    queryType: 'post',
    endpoint: '/customers/{merchantId}/claimed_rewards',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      reward_id: '',
      shopifyCustomerAccessToken: '',
    },
    // inputResolver is used to send the request body data in case of POST call
    inputResolver: inputVariables => {
      return {
        reward_id: Number(inputVariables.reward_id),
      };
    },
    isPaginated: false,
  },
  addPoints: {
    // Add points code
    queryType: 'post',
    endpoint: '/customers/{merchantId}/points',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      points: '',
      reason: '',
      shopifyCustomerAccessToken: '',
    },
    // inputResolver is used to send the request body data in case of POST call
    inputResolver: inputVariables => {
      return {
        points: Number(inputVariables.points),
        reason: inputVariables.reason,
      };
    },
    isPaginated: false,
  },
  removePoints: {
    // Add points code
    queryType: 'post',
    endpoint: '/customers/{merchantId}/remove_points',
    endpointResolver: (endpoint, inputParams) => {
      return endpoint.replace('{merchantId}', inputParams?.merchantId);
      // return `${endpoint}/${inputParams?.merchantId}/available_rewards`;
    },
    editableInputParams: {
      merchantId: '',
      points: '',
      reason: '',
      shopifyCustomerAccessToken: '',
    },
    // inputResolver is used to send the request body data in case of POST call
    inputResolver: inputVariables => {
      return {
        points: Number(inputVariables.points),
        reason: inputVariables.reason,
      };
    },
    isPaginated: false,
  },
};

// This function is used to show the datasource in the right panel
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'loyaltyLion',
  type: 'datasource',
  name: 'Loyalty Lion',
  description: 'Reward Tracker for LoyaltyLion',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

// This function is used to set the global inputs for the datasource to function.
export const LoyaltyLionEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'proxyUrl',
        placeholder: 'https://api.apptile.io/loyalty-lion-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://api.loyaltylion.com/v2/',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'app Id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<LoyaltyLionConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, LoyaltyLionConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

// This function is used to execute the query, and send headers.
export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        // Following code is used to send inpur variables of query in the header.
        {
          headers: {
            'x-shopify-customer-access-token': inputVariables?.shopifyCustomerAccessToken || null,
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

// This function is used to wrap the datasource model and list it in the datasouces
export default wrapDatasourceModel({
  name: 'loyaltyLion',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.loyaltylion.com/v2/',
    proxyUrl: 'https://api.apptile.io/loyalty-lion-proxy',
    appId: '',
    queryRunner: 'queryrunner',
  } as LoyaltyLionConfigType,

  resolveCredentialConfigs: function (credentials: ILoyaltyLionCredentials): Partial<LoyaltyLionConfigType> | boolean {
    const {apiBaseUrl, username, password, appId} = credentials;
    if (!(apiBaseUrl && username && password)) return false;

    return {
      appId: appId,
      apiBaseUrl: apiBaseUrl,
      username: username,
      password: password,
    };
  },

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<LoyaltyLionConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    // const apiBaseUrl = dsModelValues.get('apiBaseUrl'); // Get apiBaseUrl from config
    let proxyUrl = dsModelValues.get('proxyUrl'); // Get proxyUrl from config

    if (proxyUrl.endsWith('/')) {
      proxyUrl = proxyUrl.slice(0, -1);
    }

    const appId = dsConfig.config?.get('appId'); // Use appId from config

    queryRunner.initClient(proxyUrl, config => {
      config.headers = {
        'x-shopify-app-id': appId,
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return LoyaltyLionApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      LoyaltyLionApiRecords && LoyaltyLionApiRecords[queryName] ? LoyaltyLionApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'loyaltyLion';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = LoyaltyLionApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: LoyaltyLionEditors,
});
