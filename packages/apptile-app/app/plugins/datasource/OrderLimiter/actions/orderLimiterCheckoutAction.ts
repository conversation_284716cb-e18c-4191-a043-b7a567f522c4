import _ from 'lodash';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import {SHOPIFY_MODEL_CART_ITEMS_KEY} from '../../ShopifyV_22_10/constants';
import {CartProductVariantQuantityChangeParam} from '../../ShopifyV_22_10/actions/checkoutAction';

export interface CheckoutActionInteface {
  increaseCartLineItemQuantity: ActionHandler;
}

class CheckoutActions implements CheckoutActionInteface {
  checkIfOrderLimitsSatisfied = (pluginModel, lineItems, payload: CartProductVariantQuantityChangeParam) => {
    let orderLimitsSatisfied = true;
    const orderLimiterConfig = pluginModel?.toJS();
    const {merchandiseId, quantity, itemPrice} = payload;

    if (orderLimiterConfig) {
      let {
        maximumOrderValue,
        maximumOrderItems,
        maximumItemQuantity,
        orderValueExceededErrorText,
        orderItemsExceededErrorText,
        singleItemCountExceededErrorText,
      } = orderLimiterConfig;
      maximumOrderValue = Number(maximumOrderValue);
      maximumOrderItems = Number(maximumOrderItems);
      maximumItemQuantity = Number(maximumItemQuantity);
      const totalPrice = _.reduce(
        lineItems,
        function (price, item) {
          return price + (item.itemPrice ?? 0) * item.displayQuantity;
        },
        0,
      );
      const totalItems = _.sumBy(lineItems, 'displayQuantity');
      const matchingItems = _.filter(lineItems, {merchandiseId: merchandiseId});
      let totalAfterAddition = totalPrice + itemPrice ?? 0 * quantity;
      let totalItemCountAfterAddition = totalItems + quantity;
      let maxItemQuantity = 1;
      if (matchingItems && matchingItems.length > 0 && matchingItems[0] != null) {
        maxItemQuantity = _.sumBy(matchingItems, 'displayQuantity') + 1;
      }

      let errorMessage: string = '';
      if (maximumOrderValue && totalAfterAddition > maximumOrderValue) {
        orderLimitsSatisfied = false;
        errorMessage = orderValueExceededErrorText || 'Order value limit reached!';
      } else if (totalItemCountAfterAddition > maximumOrderItems) {
        orderLimitsSatisfied = false;
        errorMessage = orderItemsExceededErrorText || 'Total item count limit reached';
      } else if (maxItemQuantity > maximumItemQuantity) {
        orderLimitsSatisfied = false;
        errorMessage = singleItemCountExceededErrorText || 'Single item max limit reached';
      }

      if (!orderLimitsSatisfied) {
        toast.show(errorMessage, {
          type: 'warning',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
      }
    }
    return orderLimitsSatisfied;
  };

  increaseCartLineItemQuantity = async (
    dispatch,
    config: PluginConfig,
    model,
    selector: Selector,
    params: any,
    appConfig,
    appModel,
  ) => {
    const shopifyDatasourceId = model.get('shopifyDatasourceId');
    if (shopifyDatasourceId == null) {
      logger.error('please define shopifyDatasourceId');
      return;
    }

    const shopifyDatasourceModel = appModel?.getModelValue([shopifyDatasourceId]);
    if (shopifyDatasourceModel == null) {
      logger.error('please define shopifyDatasourceId correctly');
      return;
    }

    const payload = params as CartProductVariantQuantityChangeParam;
    const {merchandiseId} = payload;

    if (_.isEmpty(merchandiseId)) {
      logger.error(`invalid merchandiseId`, merchandiseId);
      return;
    }

    let lineItemsModel = shopifyDatasourceModel?.get(SHOPIFY_MODEL_CART_ITEMS_KEY) ?? [];

    const orderLimitsSatisfied = this.checkIfOrderLimitsSatisfied(model, lineItemsModel, payload);
    if (orderLimitsSatisfied) {
      const shopifyLineItemIncreaseAction = shopifyDatasourceModel.get('increaseCartLineItemQuantity');
      const shopifyPluginConfig = appConfig.getPlugin(shopifyDatasourceId);
      if (!shopifyLineItemIncreaseAction) {
        logger.error('increaseCartLineItemQuantity action not defined on shopify datasource');
        return;
      }
      shopifyLineItemIncreaseAction(
        dispatch,
        shopifyPluginConfig,
        shopifyDatasourceModel,
        [shopifyDatasourceId],
        params,
        appModel,
      );
    }
  };
}

const checkoutActions = new CheckoutActions();
export default checkoutActions;
