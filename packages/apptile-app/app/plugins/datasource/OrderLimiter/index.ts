import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IOrderLimiterCredentials, IntegrationPlatformType} from '../datasourceTypes';
import wrapDatasourceModel from '../wrapDatasourceModel';
import checkoutActions from './actions/orderLimiterCheckoutAction';

export type OrderLimiterPluginConfigType = DatasourcePluginConfig &
  IOrderLimiterCredentials & {increaseCartLineItemQuantity: string};

export const orderLimiterApiRecords: Record<string, any> = {};

const propertySettings: PluginPropertySettings = {
  increaseCartLineItemQuantity: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return checkoutActions.increaseCartLineItemQuantity;
    },
    actionMetadata: {
      editableInputParams: {
        merchandiseId: '',
        quantity: '{{1}}',
        syncWithShopify: '{{false}}',
        sellingPlanId: '',
        itemPrice: '',
        successToastText: 'Item added successfully!',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'orderLimiter',
  type: 'datasource',
  name: 'Order Limiter',
  description: 'OrderLimiter integration',
  section: 'Integrations',
  icon: 'datasource',
};

export const orderLimiterEditors: any = {
  basic: [
    {
      type: 'codeInput',
      name: 'shopifyDatasourceId',
      defultValue: 'shopify',
      props: {
        label: 'Shopify Datasource ID',
        placeHolder: 'shopify',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumOrderValue',
      props: {
        label: 'Maximum Order Value',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumOrderItems',
      props: {
        label: 'Maximum Order Items',
      },
    },
    {
      type: 'codeInput',
      name: 'maximumItemQuantity',
      props: {
        label: 'Maximum Item Quantity',
      },
    },
    {
      type: 'codeInput',
      name: 'orderValueExceededErrorText',
      props: {
        label: 'Order Value Exceeded Error Text',
      },
    },
    {
      type: 'codeInput',
      name: 'orderItemsExceededErrorText',
      props: {
        label: 'Max Total Item Count Exceeded Error Text',
      },
    },
    {
      type: 'codeInput',
      name: 'singleItemCountExceededErrorText',
      props: {
        label: 'Max Single Count Exceeded Error Text',
      },
    },
  ],
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  return {
    rawData: {},
    data: {},
    hasNextPage: false,
    paginationMeta: undefined,
    errors: [],
    hasError: false,
  };
};

export default wrapDatasourceModel({
  name: 'orderLimiter',
  config: {
    ...baseDatasourceConfig,
    shopifyDatasourceId: 'shopify',
    maximumOrderValue: '',
    maximumOrderItems: '',
    maximumItemQuantity: '',
    orderValueExceededErrorText: '',
    orderItemsExceededErrorText: '',
    singleItemCountExceededErrorText: '',
    increaseCartLineItemQuantity: 'action',
  } as OrderLimiterPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<OrderLimiterPluginConfigType>,
    dsModelValues: any,
  ) {},
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return orderLimiterApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      orderLimiterApiRecords && orderLimiterApiRecords[queryName] ? orderLimiterApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IOrderLimiterCredentials,
  ): Partial<OrderLimiterPluginConfigType> | boolean {
    const {maximumOrderValue, maximumOrderItems, maximumItemQuantity} = credentials;
    return {
      maximumOrderValue,
      maximumOrderItems,
      maximumItemQuantity,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['maximumOrderValue', 'maximumOrderItems', 'maximumItemQuantity'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'orderLimiter';
  },

  runQuery: function* (
    dsModel: any,
    dsConfig: any,
    dsModelValues: any,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {},
  options: {
    propertySettings,
    pluginListing,
  },
  editors: orderLimiterEditors,
});
