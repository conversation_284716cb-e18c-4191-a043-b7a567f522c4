import Immutable from 'immutable';
import {call, put} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail} from '../../query';
import {DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {getUniqueDeviceId} from 'apptile-core';
import {v4 as uuidv4} from 'uuid';
import {Platform} from 'react-native';

export interface FindifyPluginConfigType {
  apiBaseUrl: string;
  apiAccessKey: string;
  queryRunner: any;
}

type FindifyQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<FindifyPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, FindifyPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const findifyApiRecords: Record<string, FindifyQueryDetails> = {
  GetCollection: {
    queryType: 'post',
    endpoint: '/v3/smart-collection/collections/${collectionHandle}',
    endpointResolver: inputParams => `/v3/smart-collection/collections/${inputParams?.collectionHandle}`,
    contextInputParams: {
      key: 'apiAccessKey',
      user: 'user',
    },
    editableInputParams: {
      collectionHandle: '',
      limit: 12,
      offset: 0,
    },
    inputResolver: inputVariables => {
      const {collectionHandle, ...rest} = inputVariables;
      return {
        slot: `collections/${collectionHandle}`,
        ...rest,
      };
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: PluginListingSettings = {
  labelPrefix: 'findify',
  type: 'datasource',
  name: 'Findify Integration',
  description: 'Findify store front integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const findifyEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defultValue: 'https://api-v3.findify.io',
      props: {
        label: 'API Base url',
        placeholder: 'https://api-v3.findify.io',
      },
    },
    {
      type: 'codeInput',
      name: 'apiAccessKey',
      props: {
        label: 'Findify API Key',
        placeholder: '',
      },
    },
  ],
};

let findifyUser = null;

export default wrapDatasourceModel({
  name: 'Findify',
  config: {
    apiBaseUrl: 'https://api-v3.findify.io',
    apiAccessKey: '',
    queryRunner: 'queryrunner',
  } as FindifyPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      const accessToken = dsConfig.config.get('apiAccessKey');
      if (accessToken) {
        config.headers['x-key'] = `${accessToken}`;
      }
      return config;
    });
    findifyUser = {
      uid: Platform.select({native: getUniqueDeviceId(), web: uuidv4()}),
      sid: uuidv4(),
    };
    return {
      modelUpdates: [
        {
          selector: [dsModelValues.get('id')].concat('user'),
          newValue: findifyUser,
        },
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return findifyApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails = findifyApiRecords && findifyApiRecords[queryName] ? findifyApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = findifyApiRecords[queryName];

    if (!queryDetails) return;
    const {getNextPage, paginationMeta} = options;

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }

    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      // inputResolver:: To handle nested input in graphql
    }
    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) endpoint = queryDetails.endpointResolver(typedInputVariables);

    const queryRunner = dsModelValues.get('queryRunner');
    const queryResponse = yield call(
      queryRunner.runQuery,
      queryDetails.queryType,
      endpoint,
      {...typedDataVariables, ...contextInputParam, t_client: new Date().valueOf()},
      options,
    );

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {
        data,
        hasNextPage,
        paginationMeta: pageData,
      } = queryDetails.transformer(rawData, queryDetails.transformers);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }
    // const data = transformedData;
    return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: findifyEditors,
});
