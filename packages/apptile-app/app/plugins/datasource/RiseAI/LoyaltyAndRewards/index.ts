import Immutable from 'immutable';
import {call, select} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../../query/';
import AjaxQueryRunner from '../../AjaxWrapper/model';
import {baseDatasourceConfig} from '../../base';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../../datasourceTypes';
import wrapDatasourceModel from '../../wrapDatasourceModel';

export type RiseAILoyaltyConfigType = DatasourcePluginConfig & {
  queryRunner: any;
  appId: string;
};

type RiseAILoyaltyQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, contextInputParams: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
  checkInputVariables?: (inputVariables: Record<string, any>) => boolean;
  headerType?: string;
  authenticated?: boolean;
};

const RiseAILoyaltyApiRecords: Record<string, RiseAILoyaltyQueryDetails> = {
  getGiftCard: {
    queryType: 'get',
    endpoint: '/customer',
    endpointResolver: (endpoint, inputParams) => {
      return `${endpoint}/${inputParams.customerId}/profile`;
    },
    editableInputParams: {
      customerId: '',
      customerAccessToken: '',
    },
    isPaginated: false,
    authenticated: true,
  },
};

// const propertySettings: PluginPropertySettings = {
//   ...riseAILoyaltyDatasourcePropertySettings,
// };

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'riseAI',
  type: 'datasource',
  name: 'Rise AI Loyalty and Rewards',
  description: 'Rise AI Loyalty and Rewards',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const RiseAILoyaltyEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://api.apptile.io/rise-ai-proxy',
      },
    },
    {
      type: 'codeInput',
      name: 'x-apptile-app-id',
      props: {
        label: 'x-apptile-app-id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<RiseAILoyaltyConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, RiseAILoyaltyConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariables) {
        isReadyToRun = queryDetails.checkInputVariables(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, contextInputParam)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedDataVariables, paginationMeta);
    }

    const queryRunner = dsModelValues.get('queryRunner');

    const riseApiToken = dsModelValues.get('x-apptile-app-id');

    let customerAccessToken;
    if (queryDetails.authenticated) {
      customerAccessToken = typedDataVariables.customerAccessToken;
    }

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            'x-app-id': riseApiToken,
            'x-shopify-customer-access-token': customerAccessToken,
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'riseAI',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io/rise-ai-proxy',
    queryRunner: 'queryrunner',
    'x-apptile-app-id': '',
    //...riseAILoyaltyDatasourcePluginConfig,
  } as RiseAILoyaltyConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<RiseAILoyaltyConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    const requestUrl = dsConfig.config.get('apiBaseUrl');
    const appId = dsConfig.config?.get('x-apptile-app-id');

    queryRunner.initClient(requestUrl, config => {
      config.headers = {
        ...config.headers,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'x-apptile-app-id'],
          newValue: appId,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return RiseAILoyaltyApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      RiseAILoyaltyApiRecords && RiseAILoyaltyApiRecords[queryName] ? RiseAILoyaltyApiRecords[queryName] : null;
    return queryDetails && queryDetails?.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials: RiseAILoyaltyConfigType): any | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      'x-apptile-app-id': appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['x-apptile-app-id'];
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'riseAI';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = RiseAILoyaltyApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },

  options: {
    // propertySettings,
    pluginListing,
  },
  editors: RiseAILoyaltyEditors,
});
