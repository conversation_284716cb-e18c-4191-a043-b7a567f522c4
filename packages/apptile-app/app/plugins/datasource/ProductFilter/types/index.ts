import {IProduct} from '../../ShopifyV_22_10/types';

export type TransformerTypeName = 'TransformProducts';
export type TransformerFn<V, T> = (value: V) => T;
export const transformerIdentity = <T extends any, V>(a: T) => a as V;

export type IProductAndFilterOptionSortType =
  | 'manual'
  | 'title-ascending'
  | 'title-descending'
  | 'title-s-ascending'
  | 'title-s-descending'
  | 'price-ascending'
  | 'price-descending'
  | 'variant-price-ascending'
  | 'variant-price-descending'
  | 'sale-ascending'
  | 'sale-descending'
  | 'published-ascending'
  | 'published-descending'
  | 'created-ascending'
  | 'created-descending'
  | 'best-selling'
  | 'relevance'
  | 'best-selling-ascending'
  | 'best-selling-descending'
  | 'relevance-ascending'
  | 'relevance-descending'
  | 'review-ratings-ascending'
  | 'review-ratings-descending'
  | 'extra-sort1-ascending'
  | 'extra-sort1-descending'
  | 'extra-sort2-ascending'
  | 'extra-sort2-descending'
  | 'extra-sort3-ascending'
  | 'extra-sort3-descending';

export type IProductAndFilterOptionStatus = 'active' | 'disabled';
export type IProductAndFilterOptionDisplayType = 'list' | 'box' | 'swatch' | 'range' | 'rating';
export type IProductAndFilterOptionSelectType = 'single' | 'multiple';
export type IProductAndFilterOptionValue = {
  key?: any;
  label?: string;
  doc_count?: number;
  from?: number;
};

export interface IProductAndSearchFilterOption {
  filterType: string;
  label: string;
  isCollapseMobile: boolean;
  isCollapsePC: boolean;
  tooltip: string | null;
  displayAllValuesInUppercaseForm: boolean;
  showSearchBoxFilterMobile: boolean;
  showSearchBoxFilterPC: boolean;
  showMoreType: string;
  sortManualValues: boolean;
  filterOptionId: string;
  position: number;
  status: IProductAndFilterOptionStatus;
  displayType: IProductAndFilterOptionDisplayType;
  sliderDelimiter: string | null;
  replaceTextFilterValues: string | null;
  sliderRange?: number;
  sliderStep?: number;
  hideTextBoxes?: boolean;
  excludePriceFromValue?: boolean;
  shortenPipsRange?: boolean;
  moneyFormatValue?: string;
  selectType?: IProductAndFilterOptionSelectType;
  valueType?: string | null;
  showExactRating?: boolean;
  starColor?: string;
  swatchStyle?: string;
  removeTextFilterValues?: string;
  excludedValues?: any[];
  prefix?: string;
  values: Array<IProductAndFilterOptionValue> | IProductAndFilterPriceOptionValue | null;
  manualValues?: Array<string>;
}

export interface IProductAndFilterPriceOptionValue {
  min: number;
  max: number;
}

export interface IProductAndSearchProductVariants {
  merged_options: string[];
  inventory_quantity: number;
  image?: string;
  compare_at_price?: string;
  inventory_management: string;
  fulfillment_service: string;
  available: boolean;
  title: string;
  inventory_policy: string;
  price: string;
  id: number;
  sku: string;
  barcode?: string;
}
export interface IProductAndSearchProduct {
  body_html: string;
  skus: string[];
  available: boolean;
  created_at: string;
  review_count: number;
  variants: Array<IProductAndSearchProductVariants>;
  media: any[];
  title: string;
  price_min: number;
  images_info: Array<{
    src: string;
    width: number;
    alt?: string;
    id: number;
    position: number;
    height: number;
  }>;
  review_ratings: number;
  template_suffix?: string;
  updated_at: string;
  collections: Array<{
    template_suffix: string;
    handle: string;
    id: number;
    sort_value: string;
    title: string;
  }>;
  vendor: string;
  percent_sale_min: number;
  best_selling_rank: number;
  html: {
    theme_id: number;
    value: string;
  };
  id: number;
  published_at: string;
  images: Record<string, string>;
  options_with_values: Array<{
    values: Array<{
      image?: number;
      title: string;
    }>;
    name: string;
    label: string;
  }>;
  weight_min: number;
  handle: string;
  compare_at_price_min?: number;
  barcodes: any[];
  tags: string[];
  published_scope: string;
  metafields: any[];
  product_type: string;
  weight_max: number;
  locations: any[];
  position: any;
  compare_at_price_max?: number;
  price_max: number;
}

export interface IProductAndFilterResult {
  options: Array<IProductAndSearchFilterOption>;
}

export interface IProductAndFilterResponse {
  total_product: number;
  total_collection: number;
  total_page: number;
  from_cache: boolean;
  products: IProductAndSearchProduct[];
  event_type: any;
  filter: IProductAndFilterResult;
}

// ============= Apptile Types =============
export type IApptilePfsFilterOptionValue = {
  key: string;
  label: string;
  docCount: number;
  displayLabel: string;
  displayKey: string;
};

export type IApptilePfsFilterType =
  | 'collection'
  | 'vendor'
  | 'product_type'
  | 'Product'
  | 'title'
  | 'price'
  | 'percent_sale'
  | 'review_ratings'
  | 'tag';

export type IApptileFilterDisplayType = 'list' | 'box' | 'swatch' | 'range';
export type IApptileFilterStatus = 'active' | 'disabled';
export type IApptileFilterSelectType = 'single' | 'multiple';

export interface IApptilePfsFilterOption {
  filterOptionId: string;
  filterType: IApptilePfsFilterType;
  label: string;
  displayType: IApptileFilterDisplayType;
  status: IApptileFilterStatus;
  position: number;
  selectType?: IApptileFilterSelectType;
  manualValues?: Array<string>; //TODO: manualValues is now Deprecated by PFS please remove gracefully
  values: Array<IApptilePfsFilterOptionValue>;
  prefix?: string;
  tooltip: string | null;
  sliderDelimiter: string | null;
  sliderRange?: number;
  sliderStep?: number;
}

export interface IApptilePfsFilters {
  options: Array<IApptilePfsFilterOption>;
}

export interface IApptilePfsProduct extends Omit<IProduct, 'id' | 'strikeoutPriceRange' | 'priceRange'> {
  productId: number;
  reviewCount: number;
  reviewRatings: number;
  minPrice: number;
  maxPrice: number;
  variants: Array<IProductAndSearchProductVariants>;
  vendor: string;
}

export interface IApptileFilterObj {
  pf_opt_unit?: string[];
  pf_p_price?: string;
  pf_r_review_ratings?: string;
  pf_st_stock_status?: string;
  pf_t_caffeine?: string;
  pf_t_origin?: string;
  pf_t_tea_type: string[];
  pf_t_temperature: string;
}
