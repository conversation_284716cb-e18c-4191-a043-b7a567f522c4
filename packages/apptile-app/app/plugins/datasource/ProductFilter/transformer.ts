import {
  IApptilePfsProduct,
  IProductAndFilterResponse,
  IProductAndSearchProduct,
  IProductAndSearchProductVariants,
} from './types';

import _ from 'lodash';

export type TransformerFunction = (data: any) => {
  data: any;
  rawData?: any;
  hasError?: boolean;
  errors?: [];
  hasNextPage?: boolean;
  paginationMeta?: any;
};

export const TransformSuggested = (data: any) => {
  const {did_you_mean, suggestions, collections, products, total_product, total_page} = data;
  return {did_you_mean, suggestions, collections, products, totalProducts: total_product, pages: total_page};
};

export const TransformFilters = (data: any) => {
  const {filter, products, total_product, total_page} = data;
  return {filters: filter, products, totalProducts: total_product, pages: total_page};
};

export const TransformProductVariant = (data: IProductAndSearchProductVariants) => {
  const {id, title, price} = data;
  return {
    id,
    title,
    salePrice: price,
  };
};

//IApptilePfsProduct
export const TransformProduct = (data: IProductAndSearchProduct): IApptilePfsProduct => {
  const {
    id,
    title,
    handle,
    available,
    images,
    price_min,
    price_max,
    compare_at_price_min,
    compare_at_price_max,
    review_count,
    review_ratings,
    product_type,
    variants,
    vendor,
  } = data;

  const imageObj = images && Object.keys(images).length > 0 ? {image: _.get(images, '1')} : {};

  const transformedProductVariant = variants.map(v => TransformProductVariant(v));

  return {
    productId: id,
    title: title,
    handle: handle,
    availableForSale: available,
    ...imageObj,
    minPrice: price_min,
    maxPrice: price_max,
    compareAtMinPrice: compare_at_price_min,
    compareAtMaxPrice: compare_at_price_max,
    reviewCount: review_count,
    reviewRatings: review_ratings,
    productType: product_type,
    variants: transformedProductVariant,
    vendor: vendor,
  };
};

export const TransformQueryProducts = (data: IProductAndFilterResponse): Array<IApptilePfsProduct> => {
  return data?.products.length > 0 ? data.products.map(product => TransformProduct(product)) : [];
};
