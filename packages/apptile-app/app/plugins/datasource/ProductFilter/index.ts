import {PluginEditorsConfig} from 'apptile-core';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {
  AppPageTriggerOptions,
  PluginListingSettings,
  PluginModelType,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {DatasourcePluginConfig, IntegrationPlatformType, IProductFilterAndSearchCredentials} from '../datasourceTypes';
import {ShopifyPluginConfigType} from '../ShopifyV_22_10';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {sortByFilters, SORT_FIRST_KEY} from './actions/constants';
import filterActions from './actions/filterActions';
import {objectToQueryString, transformFilterInput, transformToPFS} from './actions/utils';
import {TransformQueryProducts, TransformSuggested} from './transformer';

export interface ProductFilterPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  shopUrl: string;
  queryRunner: any;
}

type ProductFilterQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, getNextPage: boolean) => string;
  transformer?: TransformerFunction;
  contextInputParams?: {[key: string]: any};
  queryHeadersResolver?: (inputVariables: any) => any;
  inputResolver?: (inputVariables: any) => any;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};
export type TransformerFunction = (data: any) => any;

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<ProductFilterPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, ProductFilterPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export const productFilterApiRecords: Record<string, ProductFilterQueryDetails> = {
  getSuggestions: {
    queryType: 'get',
    endpoint: '/bc-sf-filter/search/suggest',
    editableInputParams: {
      query: '',
      pageLimit: '',
      productLimit: '',
      collectionLimit: '',
    },
    contextInputParams: {
      shopUrl: '',
    },
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const {shopUrl, query, pageLimit, productLimit, collectionLimit} = inputVariables ?? {};
      let input = {
        shop: shopUrl,
        q: query || '',
        page_limit: pageLimit || 3,
        product_limit: productLimit || 3,
        collection_limit: collectionLimit || 3,
      };
      const resolvedEndpoint = `${endpoint}?${objectToQueryString(input)}`;
      return resolvedEndpoint;
    },
    transformer: TransformSuggested,
  },
  searchProducts: {
    queryType: 'get',
    endpoint: '/bc-sf-filter/search',
    editableInputParams: {
      query: '',
      limit: 10,
      sort: '',
    },
    contextInputParams: {
      shopUrl: '',
    },
    isPaginated: true,
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const { shopUrl, query, limit, sort } = inputVariables ?? {};
      const { after  = 1 } = paginationMeta ?? {};
      let input = {
        q: query,
        sort: sort,
        shop: shopUrl,
        page: after,
        limit,
      };
      const resolvedEndpoint = `${endpoint}?${objectToQueryString(input)}`;
      return resolvedEndpoint;
    },
    transformer: TransformQueryProducts
  },
  getAllProducts: {
    queryType: 'get',
    endpoint: '/bc-sf-filter/filter',
    editableInputParams: {
      collectionScope: '',
      sort: '',
      getFilterTree: true,
      filter: null,
      limit: 10,
    },
    contextInputParams: {
      shopUrl: '',
    },
    isPaginated: true,
    endpointResolver: (endpoint, inputVariables, paginationMeta) => {
      const {collectionScope, sort, getFilterTree, filter, shopUrl, limit} = inputVariables ?? {};
      const {after = 1} = paginationMeta ?? {};
      const transformedFilterInput = filter?.filter ? transformFilterInput(filter?.filter) : {};
      const paramObj = {
        collection_scope: collectionScope,
        sort_first: SORT_FIRST_KEY,
        build_filter_tree: getFilterTree,
        sort: sort,
        shop: shopUrl,
        page: after,
        limit,
      };
      const transformedInput = transformToPFS({...paramObj, ...transformedFilterInput});
      const resolvedEndpoint = `${endpoint}?${objectToQueryString(transformedInput)}`;
      return resolvedEndpoint;
    },
    transformer: TransformQueryProducts,
  },
};

const propertySettings: PluginPropertySettings = {
  setFilterItemState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.setFilterItemState;
    },
    actionMetadata: {
      editableInputParams: {
        selectedFilterOption: '',
        selectedFilterObj: {},
      },
    },
  },
  resetFilter: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.resetFilter;
    },
  },
  destroyFilterState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.destroyFilterState;
    },
  },
  setSortingOrderState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.setSortingOrderState;
    },
    actionMetadata: {
      editableInputParams: {selectedSortByFilterValue: null},
    },
  },
  setPostFilterState: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.setPostFilterState;
    },
    actionMetadata: {
      editableInputParams: {selectedSortByFilterValue: null, rawFilter: null, collectionScope: null},
    },
  },
  applyFilters: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return filterActions.applyFilters;
    },
  },
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'productFilterSearch',
  type: 'datasource',
  name: 'Product Filter Integration',
  description: 'Custom product filter integration',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const productFilterEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      defaultValue: 'https://services.mybcapps.com',
      props: {
        label: 'API Base url',
        placeholder: 'https://services.mybcapps.com',
      },
    },
    {
      type: 'codeInput',
      name: 'shopUrl',
      props: {
        label: 'Shop URL',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiTimeOut',
      props: {
        label: 'API Time Out(ms)',
        placeholder: '30000',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'productFilterSearch',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://services.mybcapps.com',
    apiTimout: 30000,
    shopUrl: '',
    queryRunner: 'queryrunner',
    allFilters: [],
    sortByFilters: [],
    filterDataObj: {}, //can be renamed as collectionFilterMap
    collectionScope: null,
    resetFilter: 'action',
    destroyFilterState: 'action',
    setSortingOrderState: 'action',
    setPostFilterState: 'action',
    setFilterItemState: 'action',
    applyFilters: 'action',
  } as ProductFilterPluginConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<ShopifyPluginConfigType>, dsModelValues: any) {
    const queryRunner = AjaxQueryRunner();
    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
        {
          selector: [dsConfig.get('id'), 'shopUrl'],
          newValue: dsConfig.config.get('shopUrl'),
        },
        {
          selector: [dsConfig.get('id'), 'sortByFilters'],
          newValue: sortByFilters,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return productFilterApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      productFilterApiRecords && productFilterApiRecords[queryName] ? productFilterApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IProductFilterAndSearchCredentials,
  ): Partial<ProductFilterPluginConfigType> | boolean {
    const {apiBaseUrl, shopUrl} = credentials;
    if (!apiBaseUrl || !shopUrl) return false;
    return {
      apiBaseUrl: apiBaseUrl,
      shopUrl: shopUrl,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl', 'shopUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'productFilterSearch';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = productFilterApiRecords[queryName];
      if (!queryDetails) return;

      let {transformer, endpoint, endpointResolver} = queryDetails ?? {};
      const {paginationMeta} = options ?? {};

      let contextInputParam;
      if (queryDetails && queryDetails.contextInputParams) {
        const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
        contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
      }
      const typedInputs = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      const typedInputVariables = queryDetails.inputResolver ? queryDetails.inputResolver(typedInputs) : typedInputs;

      if (endpointResolver) {
        endpoint =
          endpointResolver &&
          endpointResolver(
            endpoint,
            {
              ...typedInputVariables,
              ...contextInputParam,
            },
            paginationMeta,
          );
      }

      const queryRunner = dsModelValues.get('queryRunner');
      const timeout = Number.parseInt(dsModelValues.get('apiTimeOut') ?? 30000);
      const queryOptions = options ? {...options, timeout} : {timeout};
      const queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint,
        {
          ...typedInputVariables,
          ...contextInputParam,
        },
        queryOptions,
      );

      const {limit = 10} = typedInputVariables;

      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = [];
      let queryHasNextPage, paginationDetails;
      if (transformer) {
        transformedData = transformer(rawData);
        const {after: currentPage = 1} = paginationMeta ?? {};
        queryHasNextPage = rawData.total_product - limit * currentPage > 0;
        paginationDetails = {after: currentPage ? currentPage + 1 : 1};
      } else {
        transformedData = rawData.data;
      }
      return yield {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: productFilterEditors,
});
