import {ActionHandler} from '../../../triggerAction';
import {PluginConfig} from 'apptile-core';
import {ModelChange, Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';
import pull from 'lodash/pull';
import _, {cloneDeep, isEmpty} from 'lodash';
import {
  IApptilePfsFilters,
  IProductAndSearchFilterOption,
  IProductAndFilterResult,
  IProductAndFilterOptionValue,
} from '../types';

export interface FilterActionInteface {
  resetFilter: ActionHandler;
  destroyFilterState: ActionHandler;
  setSortingOrderState: ActionHandler;
  setPostFilterState: ActionHandler;
  setFilterItemState: ActionHandler;
  applyFilters: ActionHandler;
}

export interface selectedFilterOptionParams {
  selectedFilterOption: string;
  selectedFilterObj: IProductAndSearchFilterOption;
}

export interface createFilterModelParams {
  filters: Array<IProductAndSearchFilterOption>;
}

export interface getAllFilterParams {
  queryParams: any;
  endpoint: string;
  collectionScope: string;
  sort: string;
}

export interface ISetFilterState extends getAllFilterParams {
  rawFilter: any;
}

export interface toggleLoaderParams {
  isLoading: boolean;
}

export interface selectedSortByFilterParams {
  selectedSortByFilterValue: boolean;
}

class FilterActions implements FilterActionInteface {
  private async applyChanges(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    lineItems: any,
    key: string,
  ) {
    const modelUpdates = [];
    const lineItemsSelector = selector.concat([key]);
    modelUpdates.push({
      selector: lineItemsSelector,
      newValue: lineItems,
    });
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  }

  private async bulkApplyChanges(
    dispatch: any,
    config: PluginConfig,
    modelSelector: Selector,
    changesets: ModelChange[],
  ) {
    dispatch(
      modelUpdateAction(
        changesets.map(({selector, newValue}) => {
          return {
            selector: modelSelector.concat(selector),
            newValue,
          };
        }),
        undefined,
        true,
      ),
    );
  }

  private createFilters(filters: any) {
    if (_.isEmpty(filters)) return {};
    const filterObj = filters.reduce(function (acc: any, cur: any, i: number) {
      if (cur.selectType === 'single') {
        acc[cur.filterOptionId] = '';
      } else if (cur.selectType === 'multiple') {
        acc[cur.filterOptionId] = [];
      } else if (!cur.selectType && cur.displayType === 'range') {
        acc[cur.filterOptionId] = '';
      } else {
        acc[cur.filterOptionId] = [];
      }
      return acc;
    }, {});
    return filterObj;
  }

  private updateFilters = (oldFilters: any, newFilters: any) => {
    const updatedFilters = !_.isEmpty(newFilters) ? cloneDeep(newFilters) : {};
    for (var key of Object.keys(oldFilters)) {
      if (oldFilters[key]) {
        updatedFilters[key] = oldFilters[key];
      }
    }
    return updatedFilters;
  };

  private transformFilters = (receivedFilters: IProductAndFilterResult) => {
    const filters = cloneDeep(receivedFilters);
    if (!filters.options) {
      return {} as IApptilePfsFilters;
    }
    return filters.options.filter((filter: IProductAndSearchFilterOption) => {
      if (filter.filterOptionId === 'pf_ps_discount') {
        ((filter.values as Array<IProductAndFilterOptionValue>) || []).forEach(
          (value: IProductAndFilterOptionValue, index: number) => {
            value.label = value.key;
            value.key = filter.manualValues ? filter.manualValues[index] : '';
          },
        );
      }
      if (filter.filterOptionId === 'pf_r_review_ratings') {
        ((filter.values as Array<IProductAndFilterOptionValue>) || []).forEach(
          (value: IProductAndFilterOptionValue, index: number) => {
            value.key = value.from;
          },
        );
        (filter.values as Array<IProductAndFilterOptionValue>).reverse();
      }
      if (filter.filterOptionId === 'pf_p_price') {
        if (!filter.values?.max || !filter.values?.min) {
          filter.values = null;
        }
      }
      Array.isArray(filter.values) &&
        (filter.values || []).forEach((value: any, index: number) => {
          const displayKey = typeof value.key === 'string' ? value.key.split(':') : value.key;
          const displayLabel = typeof value.label === 'string' ? value.label.split(':') : value.label;
          filter.values[index] = {
            ...filter.values[index],
            displayKey: Array.isArray(displayKey)
              ? displayKey.length > 1
                ? displayKey[1]
                : displayKey[0]
              : displayKey,
            displayLabel: Array.isArray(displayLabel)
              ? displayLabel.length > 1
                ? displayLabel[1]
                : displayLabel[0]
              : displayLabel,
          };
        });
      return !isEmpty(filter.values);
    });
  };

  applyFilters = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const collectionScope = model.get('collectionScope');
    let filterDataObj = model.get('filterDataObj') || {};
    filterDataObj[collectionScope].applyFilter = true;
    await this.applyChanges(dispatch, config, model, selector, filterDataObj, 'filterDataObj');
  };

  setFilterItemState = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as selectedFilterOptionParams;
    const {selectedFilterOption, selectedFilterObj} = payload;
    let collectionScope = model.get('collectionScope');
    let filterDataObj = model.get('filterDataObj') || {};
    let filterObj = filterDataObj[collectionScope].filter ? filterDataObj[collectionScope].filter : {};
    if (selectedFilterObj.displayType === 'range') {
      const rangeInputs = selectedFilterOption.split(':');
      let x = parseInt(rangeInputs[0]);
      let y = parseInt(rangeInputs[1]);
      if (isNaN(x)) {
        x = selectedFilterObj.values?.min;
      }
      if (isNaN(y)) {
        y = selectedFilterObj.values?.max;
      }
      filterObj[selectedFilterObj.filterOptionId] = `${x}:${y}`;
    } else if (selectedFilterObj.displayType === 'rating') {
      filterObj[selectedFilterObj.filterOptionId] = selectedFilterOption;
    } else if (Array.isArray(filterObj[selectedFilterObj.filterOptionId])) {
      filterObj[selectedFilterObj.filterOptionId].includes(selectedFilterOption)
        ? pull(filterObj[selectedFilterObj.filterOptionId], selectedFilterOption)
        : filterObj[selectedFilterObj.filterOptionId].push(selectedFilterOption);
    } else {
      filterObj[selectedFilterObj.filterOptionId] =
        filterObj[selectedFilterObj.filterOptionId] == selectedFilterOption ? '' : selectedFilterOption;
    }
    filterDataObj[collectionScope].filter = filterObj;
    await this.applyChanges(dispatch, config, model, selector, filterDataObj, 'filterDataObj');
  };

  resetFilter = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const collectionScope = model.get('collectionScope');
    let filterDataObj = model.get('filterDataObj') || {};
    let allFilter = model.get('allFilter') || {};
    const filterObj = this.createFilters(allFilter);
    const sortBy = filterDataObj[collectionScope].sortBy;
    const updatedfFlterObj = this.updateFilters(filterObj, {});
    filterDataObj[collectionScope] = {filter: updatedfFlterObj, applyFilter: false, sortBy: sortBy};
    await this.applyChanges(dispatch, config, model, selector, filterDataObj, 'filterDataObj');
  };

  setPostFilterState = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as ISetFilterState;
    const {collectionScope, rawFilter} = payload;
    const filterDataObj = model.get('filterDataObj') || {};
    const isFilterApplied = filterDataObj[collectionScope]?.applyFilter
      ? filterDataObj[collectionScope].applyFilter
      : false;
    const sortBy = filterDataObj[collectionScope]?.sortBy ? filterDataObj[collectionScope].sortBy : '';
    const filterObj = isFilterApplied ? filterDataObj[collectionScope].filter : {};

    const filters = rawFilter;
    const updatedFilters = this.transformFilters(filters);
    await this.applyChanges(dispatch, config, model, selector, updatedFilters, 'allFilters');
    const newFilterObj = this.createFilters(updatedFilters);
    const updatedfFlterObj = this.updateFilters(filterObj, newFilterObj);
    filterDataObj[collectionScope] = {
      filter: updatedfFlterObj,
      applyFilter: isFilterApplied,
      sortBy: sortBy,
    };

    const changesets: ModelChange[] = [
      {
        selector: ['filterDataObj'],
        newValue: filterDataObj,
      },
      {
        selector: ['collectionScope'],
        newValue: collectionScope,
      },
    ];
    await this.bulkApplyChanges(dispatch, config, selector, changesets);
  };

  setSortingOrderState = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const payload = params as selectedSortByFilterParams;
    const {selectedSortByFilterValue} = payload;
    const collectionScope = model.get('collectionScope');
    let filterDataObj = model.get('filterDataObj') || {};

    filterDataObj[collectionScope].sortBy = selectedSortByFilterValue;
    await this.applyChanges(dispatch, config, model, selector, filterDataObj, 'filterDataObj');
  };

  destroyFilterState = async (dispatch: any, config: PluginConfig, model: any, selector: Selector, params: any) => {
    const changesets: ModelChange[] = [
      {
        selector: ['filterDataObj'],
        newValue: {},
      },
      {
        selector: ['allFilters'],
        newValue: [],
      },
      {
        selector: ['collectionScope'],
        newValue: null,
      },
    ];
    await this.bulkApplyChanges(dispatch, config, selector, changesets);
  };
}

const filterActions = new FilterActions();
export default filterActions;
