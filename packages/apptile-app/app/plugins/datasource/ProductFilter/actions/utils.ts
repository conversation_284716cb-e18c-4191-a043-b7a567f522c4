import {cloneDeep, isEmpty} from 'lodash';

export function objectToQueryString(obj: any, prefix = '') {
  var str = [],
    p;
  for (p in obj) {
    if (obj.hasOwnProperty(p)) {
      var k = prefix ? prefix + '[]' : p,
        v = obj[p];
      str.push(
        v !== null && typeof v === 'object' && v.length > 0
          ? objectToQueryString(v, k)
          : encodeURIComponent(k) + '=' + encodeURIComponent(v),
      );
    }
  }
  return str.join('&');
}

export function transformToPFS(obj: any) {
  const clonedObj = cloneDeep(obj);
  const stockStatus = clonedObj['pf_st_stock_status'] ? clonedObj['pf_st_stock_status'] : [];
  for (let i = 0; i < stockStatus.length; i++) {
    if (stockStatus[i] === 'out-of-stock') {
      clonedObj['pf_st_stock_status'][i] = false;
    } else {
      clonedObj['pf_st_stock_status'][i] = true;
    }
  }
  return clonedObj;
}

export function transformFilterInput(filterObj: any) {
  let updatedFilterObj = !isEmpty(filterObj) ? cloneDeep(filterObj) : {};
  for (var key of Object.keys(updatedFilterObj)) {
    if (typeof updatedFilterObj[key] === 'string' && updatedFilterObj[key]) {
      updatedFilterObj[key] = [filterObj[key]];
    }
  }
  return updatedFilterObj;
}
