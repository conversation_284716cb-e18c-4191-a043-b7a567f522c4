export const sortByFilters = [
  {key: 'best-selling', value: 'Bestselling'},
  {key: 'price-descending', value: 'Price: High to Low'},
  {key: 'price-ascending', value: 'Price: Low to High'},
  {key: 'title-ascending', value: 'Alphabetically: A to Z'},
  {key: 'title-descending', value: 'Alphabetically: Z to A'},
  {key: 'published-descending', value: 'Date: Newest First'},
  {key: 'published-ascending', value: 'Date: Oldest First'},
];

export const SORT_FIRST_KEY = 'available';
