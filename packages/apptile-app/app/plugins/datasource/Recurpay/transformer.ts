import _ from 'lodash';

export type TransformerFunction = (
  data: any,
  paginationMeta?: any,
) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

export const TransformPlans = (data: any) => {
  const plans = data.plans.map((e: any) => {
    let {discount} = e?.pricing_policy.find((item: any) => item.discount);
    return (e = {
      plan_id: e.id,
      selling_plan_id: e?.selling_plan_id,
      selling_plan_name: e?.name,
      description: e?.description,
      status: e?.status,
      discount_type: discount?.type,
      discount_value: discount?.value,
      frequency: e?.delivery_policy?.frequency,
      interval: e?.delivery_policy?.interval,
    });
  });
  return {data: plans};
};

export const TransformCheckoutUrl = (data: any) => {
  const checkoutObj = {checkoutUrl: data?.checkout?.url};
  return {data: checkoutObj};
};

export const TransformgetViewManageSubscriptionUrl = (data: any) => {
  const manageSubscriptionObj = {manageUrl: data?.account?.url};
  return {data: manageSubscriptionObj};
};
