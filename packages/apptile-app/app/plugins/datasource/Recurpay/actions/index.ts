import {GetRegisteredPlugin, GetRegisteredPluginInfo, PluginConfig, navigateToScreen} from 'apptile-core';
import {Selector} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import {executeQuery} from '..';
export interface ISubscriptionProductProperties {
  name: string;
  value: string;
}

export interface ILineItemsArray {
  quantity: number;
  variant_id: string;
  properties: ISubscriptionProductProperties[] | {};
  plan: {
    id: string;
  };
}
export interface initiateCheckoutActionParams {
  lineItems: ILineItemsArray[];
  note: string | '';
  customerAccessToken: string | '';
  loggedInNavigateTo: string;
  nonLoggedInNavigateTo: string;
}

export interface getViewManageSubscriptionUrlActionParams {
  customerAccessToken: string;
  navigateTo: string;
}

export interface IRecurpayActions {
  initiateCheckout: ActionHandler;
  getViewManageSubscriptionUrl: ActionHandler;
}

export interface IInitiateCheckoutResponse {
  checkoutUrl: string;
}

class recurpayActions implements IRecurpayActions {
  //#region Private methods
  private async queryExecutor(dsModelValues: any, dsConfig: any, queryName: string, inputVariables: any) {
    const dsType = dsModelValues.get('pluginType');
    const dsModel = GetRegisteredPlugin(dsType);
    const queries = this.getQueries();
    const queryDetails = queries[queryName];
    return await executeQuery(dsModel, dsConfig, dsModelValues, queryDetails, {...inputVariables}, {});
  }
  private getQueries() {
    const shopifyDatasouceConfig = GetRegisteredPluginInfo('Recurpay');
    const queries = shopifyDatasouceConfig?.plugin?.getQueries();
    return queries;
  }

  initiateCheckout = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: initiateCheckoutActionParams,
  ) => {
    const {lineItems, note, customerAccessToken, loggedInNavigateTo, nonLoggedInNavigateTo} = params;

    if (!customerAccessToken) {
      dispatch(navigateToScreen(nonLoggedInNavigateTo, {}));
    } else {
      const {data} = await this.queryExecutor(model, config, 'initiateCheckout', {
        lineItems: lineItems,
        note: note,
        customerAccessToken: customerAccessToken,
      });
      // console.log('data', data);
      if (data && data.checkoutUrl) {
        dispatch(
          navigateToScreen(loggedInNavigateTo, {
            checkoutUrl: data.checkoutUrl,
          }),
        );
      }
    }
  };

  getViewManageSubscriptionUrl = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: getViewManageSubscriptionUrlActionParams,
  ) => {
    const {customerAccessToken, navigateTo} = params;

    const {data} = await this.queryExecutor(model, config, 'getViewManageSubscriptionUrl', {
      customerAccessToken: customerAccessToken,
    });
    // console.log('data', data);
    if (data && data.manageUrl) {
      dispatch(
        navigateToScreen(navigateTo, {
          manageUrl: data.manageUrl,
        }),
      );
    }
  };
}

const RecurpayActions = new recurpayActions();
export default RecurpayActions;
