import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {DatasourcePluginConfig, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType, PluginModelType, TriggerActionIdentifier} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformerFunction,
  TransformPlans,
  TransformCheckoutUrl,
  TransformgetViewManageSubscriptionUrl,
} from './transformer';
import RecurpayActions from './actions';

export interface RecurpayPluginConfigType extends DatasourcePluginConfig {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
  initiateCheckout: any;
  getViewManageSubscriptionUrl: any;
}

type IEditableParams = Record<string, any>;

type RecurpayQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseRecurpayQuerySpec: Partial<RecurpayQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    proxyUrl: '',
    appId: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return inputVariables;
  },
  transformer: (data: any) => {
    return data;
  },
};

const recurpayApiRecords: Record<string, RecurpayQueryDetails> = {
  getPlans: {
    ...baseRecurpayQuerySpec,
    queryType: 'post',
    endpoint: '/storefront/plans',
    editableInputParams: {
      productId: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {productId} = inputVariables;
      const queryParams = _.omitBy(
        {
          productId,
        },
        _.isNil,
      );
      const resolvedEndpoint = `${endpoint}${jsonToQueryString(queryParams)}`;
      return resolvedEndpoint;
    },
    transformer: TransformPlans,
  },
  initiateCheckout: {
    ...baseRecurpayQuerySpec,
    queryType: 'post',
    endpoint: '/storefront/checkout',
    editableInputParams: {
      lineItems: '',
      note: '',
      customerAccessToken: '',
    },
    inputResolver: (inputVariables: any) => {
      return {
        lineItems: inputVariables.lineItems,
        note: inputVariables.note,
      };
    },
    transformer: TransformCheckoutUrl,
  },
  getViewManageSubscriptionUrl: {
    ...baseRecurpayQuerySpec,
    queryType: 'get',
    endpoint: '/storefront/view/subscriptions',
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: (inputVariables: any) => {
      return {};
    },
    transformer: TransformgetViewManageSubscriptionUrl,
  },
};
const propertySettings: PluginPropertySettings = {
  initiateCheckout: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return RecurpayActions.initiateCheckout;
    },
    actionMetadata: {
      editableInputParams: {
        lineItems: '',
        note: '',
        customerAccessToken: '',
        loggedInNavigateTo: '',
        nonLoggedInNavigateTo: '',
      },
    },
  },
  getViewManageSubscriptionUrl: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return RecurpayActions.getViewManageSubscriptionUrl;
    },
    actionMetadata: {
      editableInputParams: {
        customerAccessToken: '',
        navigateTo: '',
      },
    },
  },
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'recurpay',
  type: 'datasource',
  name: 'Recurpay',
  description: 'Recurpay integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const recurpayEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'API Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<RecurpayPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, RecurpayPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

export const executeQuery = async (
  dsModel: any,
  dsConfig: any,
  dsModelValues: any,
  queryDetails: any,
  inputVariables: any,
  options?: AppPageTriggerOptions,
) => {
  try {
    if (!queryDetails) throw new Error('Invalid Query');
    const {getNextPage, paginationMeta} = options ?? {};

    let contextInputParam;
    if (queryDetails && queryDetails.contextInputParams) {
      const contextInputParamResolve = makeInputParamsResolver(queryDetails.contextInputParams);
      contextInputParam = contextInputParamResolve(dsConfig, dsModelValues);
    }
    let isReadyToRun: boolean = true;
    let typedInputVariables, typedDataVariables;
    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables)
      : typedInputVariables;

    let endpoint = queryDetails.endpoint;
    if (queryDetails.endpointResolver) {
      endpoint = queryDetails.endpointResolver(endpoint, typedInputVariables, paginationMeta);
    }

    const mapsApiKey = dsModelValues?.get('proxyUrl') ?? null;

    if (!mapsApiKey) throw new Error('Please give an API KEY');

    const queryRunner = dsModelValues.get('queryRunner');

    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      queryResponse = await queryRunner.runQuery(
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
          ...contextInputParam,
        },
        {
          headers: {
            'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken ?? '',
          },
        },
      );
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;

    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, paginationMeta);
      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return {rawData, data: transformedData, hasNextPage: queryHasNextPage, paginationMeta: paginationDetails};
  } catch (ex: any) {
    const error = ex?.request?.response ? new Error(JSON.parse(ex?.request?.response).message) : ex;
    return {
      rawData: {},
      data: {},
      hasNextPage: false,
      paginationMeta: {},
      errors: [error],
      hasError: true,
    };
  }
};

export default wrapDatasourceModel({
  name: 'Recurpay',
  config: {
    ...baseDatasourceConfig,
    proxyUrl: 'https://api.apptile.io/recurpay',
    appId: '',
    queryRunner: 'queryRunner',
    initiateCheckout: 'actions',
    getViewManageSubscriptionUrl: 'actions',
  } as RecurpayPluginConfigType,

  initDatasource: async (dsModel: any, dsConfig: PluginConfigType<RecurpayPluginConfigType>, dsModelValues: any) => {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('proxyUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'X-Shopify-App-Id': dsConfig.config?.get('appId') ?? '',
          'Content-Type': 'application/json',
        },
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return recurpayApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails = recurpayApiRecords && recurpayApiRecords[queryName] ? recurpayApiRecords[queryName] : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials): Partial<RecurpayPluginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'recurpay';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = recurpayApiRecords[queryName];
    if (!queryDetails) return;
    return yield call(executeQuery, dsModel, dsConfig, dsModelValues, queryDetails, inputVariables, options);
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: recurpayEditors,
});
