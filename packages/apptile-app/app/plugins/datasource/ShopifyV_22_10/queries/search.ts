import gql from 'graphql-tag';
import {PRODUCT_DETAILS_METAFIELDS} from './product';

export const GET_PREDICTIVE_SEARCH_SUGGESTIONS = gql`
  query getSearchSuggestions(
    $query: String!
    $limit: Int
    $limitScope: PredictiveSearchLimitScope
    $searchableFields: [SearchableField!]
    $types: [PredictiveSearchType!]
    $unavailableProducts: SearchUnavailableProductsType
    $countryCode: CountryCode
    $languageCode: LanguageCode
  ) @inContext(country: $countryCode, language: $languageCode) {
    predictiveSearch(
      query: $query
      limit: $limit
      limitScope: $limitScope
      searchableFields: $searchableFields
      unavailableProducts: $unavailableProducts
      types: $types
    ) {
      queries {
        text
      }
      products {
        id
        handle
        title
        featuredImage {
          id
          height
          width
          altText
          url
        }
        priceRange {
          maxVariantPrice {
            amount
          }
          minVariantPrice {
            amount
          }
        }
        vendor
        availableForSale
        tags
      }
      articles {
        excerpt
        handle
        id
        onlineStoreUrl
        publishedAt
        title
        trackingParameters
        authorV2 {
          bio
          email
          firstName
          lastName
          name
        }
        author {
          bio
          email
          firstName
          lastName
          name
        }
        excerptHtml
      }
      collections {
        handle
        id
        title
        trackingParameters
        image {
          id
          height
          width
          altText
          url
        }
      }
      pages {
        handle
        id
        onlineStoreUrl
        title
        trackingParameters
      }
    }
  }
`;

export const SEARCH_PRODUCTS = gql`
  query searchProducts(
    $query: String!
    $first: Int
    $after: String
    $before: String
    $last: Int
    $prefix: SearchPrefixQueryType
    $productFilters: [ProductFilter!]
    $reverse: Boolean
    $sortKey: SearchSortKeys
    $types: [SearchType!]
    $unavailableProducts: SearchUnavailableProductsType
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
    $countryCode: CountryCode
  ) @inContext(country: $countryCode) {
    search(
      query: $query
      first: $first
      after: $after
      before: $before
      last: $last
      prefix: $prefix
      productFilters: $productFilters
      reverse: $reverse
      sortKey: $sortKey
      types: $types
      unavailableProducts: $unavailableProducts
    ) {
      nodes {
        ... on Product {
          ${PRODUCT_DETAILS_METAFIELDS}
        }
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      productFilters {
        id
        label
        type
        values {
          count
          id
          input
          label
        }
      }
      totalCount
    }
  }
`;
