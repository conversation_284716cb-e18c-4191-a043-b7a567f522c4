import gql from 'graphql-tag';

const ARTICLE_DETAILS = `
    authorV2 {
        bio
        email
        firstName
        lastName
        name
    }
    content
    contentHtml
    excerpt
    excerptHtml
    blog {
      id
      title
      handle
    }
  
    handle
    id
    image {
        altText
        id
        originalSrc
    }
    publishedAt
    seo {
        description
        title
    }
    tags
    title
    onlineStoreUrl
`;

const BLOG_DETAILS = `
        id
        authors {
           
                    bio
                    email
                    firstName
                    lastName
                    name
                
        }
        articles(first: 10) {
            edges {
                cursor
                node {
                    ${ARTICLE_DETAILS}
                }
            }
        }
        seo {
            description
            title

        }
        title
        onlineStoreUrl
        handle  
    
`;

export const GET_BLOG = gql`
  query GetBlog($blogId: ID) {
    blog(id: $blogId) {
      ${BLOG_DETAILS}
    }
  }
`;

export const GET_BLOG_BY_HANDLE = gql`
  query GetBlog($blogHandle: String!) {
    blogByHandle(handle: $blogHandle) {
      ${BLOG_DETAILS}
    }
  }
`;

export const GET_BLOGS = gql`
  query GetBlogs($query: String, $first: Int, $after: String, $reverse: Boolean, $sortKey: BlogSortKeys) {
    blogs ( query: $query, first: $first, after: $after, reverse: $reverse, sortKey: $sortKey) {
        edges {
            cursor
            node {
                ${BLOG_DETAILS}
            }
        }
        pageInfo {
            hasNextPage
            endCursor
            startCursor
            hasPreviousPage
        }
    }
  }
`;

export const GET_ARTICLE = gql`
  query GetArticle($blogHandle: String!, $articleHandle: String!) {
      blogByHandle(handle:$blogHandle) {
        id
        title
        articleByHandle(handle:$articleHandle){
          ${ARTICLE_DETAILS}
        }
      }
  }
`;

// node (handle: $handle) {
//   ... on Article {
//       ${ARTICLE_DETAILS}
//   }
// }

export const GET_ARTICLES = gql`
  query GetArticles( $query: String, $first: Int, $after: String, $reverse: Boolean, $sortKey: ArticleSortKeys) {
    articles ( query: $query, first: $first, after: $after, reverse: $reverse, sortKey: $sortKey) {
        edges {
            cursor
            node {
                ${ARTICLE_DETAILS}
            }
        }

        pageInfo {
            hasNextPage
            endCursor
            startCursor
            hasPreviousPage

        }
    }
  }
`;
