import gql from 'graphql-tag';
import {
  PRODUCT_DETAILS_FOR_DROPDOWN,
  PRODUCT_DETAILS_METAFIELDS,
  PRODUCT_DETAILS_METAFIELDS_CAROUSEL,
  PRODUCT_IMAGE,
} from './product';
// import {IShopifyMetafieldInput} from '../types';

const COLLECTION_DETAILS_METAFIELDS = `
  id
  title
  handle
  description
  descriptionHtml
  image {
    id
    url
  }
  onlineStoreUrl
  seo {
    title
    description
  }
  updatedAt
  metafields(identifiers: $collectionMetafields) {
    id
    key
    value
    namespace
    description
    type
    references(first: 100) {
      nodes {
          ... on Collection {
              description
              descriptionHtml
              handle
              id
              onlineStoreUrl
              title
              trackingParameters
              updatedAt
          }
      }
    }
    reference {
      ... on GenericFile {
        id
        previewImage {
          url
        }
        url
      }
      ... on MediaImage {
        id
        previewImage {
          url
        }
        image {
          url
        }
      }
    }
  }
`;

const COLLECTION_DETAILS = `
  id
  title
  handle
  description
  descriptionHtml
  image {
    id
    url
  }
  onlineStoreUrl
  seo {
    title
    description
  }
  updatedAt
`;

const COLLECTION_DETAILS_FOR_DROPDOWN = `
  id
  title
  handle
  image {
    id
    url
  }
  products(first: 2) {
    edges {
      node {
        ${PRODUCT_DETAILS_FOR_DROPDOWN}
      }
    }
  }
  updatedAt
`;

export const GET_COLLECTION_BY_HANDLE = gql`
  query GetCollection($collectionHandle: String!
    $countryCode: CountryCode
    $languageCode: LanguageCode
    $collectionMetafields: [HasMetafieldsIdentifier!]!
  ) @inContext(country: $countryCode, language: $languageCode) {
    collectionByHandle(handle: $collectionHandle) {
      ${COLLECTION_DETAILS_METAFIELDS}
    }
  }
`;

export const GET_COLLECTION_HANDLE_PRODUCTS = gql`
  query GetCollection(
    $collectionHandle: String!
    $sortKey: ProductCollectionSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
    $filters: [ProductFilter!]
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
    $languageCode: LanguageCode
  ) @inContext(country: $countryCode, language: $languageCode) {
    collectionByHandle(handle: $collectionHandle) {
      id
      title
      handle
      products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse, filters: $filters) {
        pageInfo {
          hasNextPage
          endCursor
          startCursor
          hasPreviousPage
        }
        edges {
          cursor
          node {
            ${PRODUCT_DETAILS_METAFIELDS_CAROUSEL}
          }
        }
      }
    }
  }
`;

export const GET_COLLECTION_HANDLE_PRODUCTS_ONLY_IMAGES = gql`
  query GetCollection(
    $collectionHandle: String!
    $sortKey: ProductCollectionSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
  ) @inContext(country: $countryCode) {
    collectionByHandle(handle: $collectionHandle) {
      id
      title
      handle
      products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
        edges {
          cursor
          node {
            images(first: 2) {
              edges {
                node {
                  ${PRODUCT_IMAGE}
                }
              }
            }
          }
        }
      }
    }
  }
`;

/**
 * To test the experiment where we can pass product data from collection caraousal and Observe PDP performance
 */
// export const GET_COLLECTION_HANDLE_PRODUCTS_V1 = gql`
//   query GetCollection(
//     $collectionHandle: String!
//     $sortKey: ProductCollectionSortKeys
//     $reverse: Boolean
//     $first: Int!
//     $after: String
//     $countryCode: CountryCode
//     $filters: [ProductFilter!]
//     $productMetafields: [HasMetafieldsIdentifier!]!
//     $variantMetafields: [HasMetafieldsIdentifier!]!
//   ) @inContext(country: $countryCode) {
//     collectionByHandle(handle: $collectionHandle) {
//       id
//       title
//       handle
//       products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse, filters: $filters) {
//         pageInfo {
//           hasNextPage
//           endCursor
//           startCursor
//           hasPreviousPage
//         }
//         edges {
//           cursor
//           node {
//             ${PRODUCT_DETAILS}
//           }
//         }
//       }
//     }
//   }
// `;

export const GET_COLLECTION_ID_PRODUCTS = gql`
  query GetCollection(
    $id: ID
    $sortKey: ProductCollectionSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
    $filters: [ProductFilter!]
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
  ) @inContext(country: $countryCode) {
    collection(id: $id) {
      id
      title
      handle
      products(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse, filters: $filters) {
        pageInfo {
          hasNextPage
          endCursor
          startCursor
          hasPreviousPage
        }
        edges {
          cursor
          node {
            ${PRODUCT_DETAILS_METAFIELDS}
          }
        }
      }
    }
  }
`;

export const GET_PRODUCTS_BY_HANDLES = gql`
  query GetProductsByHandles(
    $productHandles: String!
    $first: Int!
    $countryCode: CountryCode
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
    $languageCode: LanguageCode) 
    @inContext(country: $countryCode, language: $languageCode) {
    products(query: $productHandles, first: $first) {
      edges {
        node {
          ${PRODUCT_DETAILS_METAFIELDS}
        }
      }
    }
  }
`;

export const SEARCH_PRODUCTS = gql`
  query SearchProducts(
    $query: String!
    $sortKey: ProductSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
    $languageCode: LanguageCode) 
    @inContext(country: $countryCode, language: $languageCode){
    products(query: $query, first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
      pageInfo {
        hasNextPage
        endCursor
        startCursor
        hasPreviousPage
      }
      edges {
        cursor
        node {
          ${PRODUCT_DETAILS_METAFIELDS}
        }
      }
    }
  }
`;

export const SEARCH_PRODUCTS_WITH_FILTERS = gql`
  query SearchProducts(
    $query: String!
    $first: Int
    $after: String
    $before: String
    $last: Int
    $prefix: SearchPrefixQueryType
    $productFilters: [ProductFilter!]
    $reverse: Boolean
    $sortKey: SearchSortKeys
    $unavailableProducts: SearchUnavailableProductsType
    $productMetafields: [HasMetafieldsIdentifier!]!
    $variantMetafields: [HasMetafieldsIdentifier!]!
    $countryCode: CountryCode
    $languageCode: LanguageCode
  ) @inContext(country: $countryCode, language: $languageCode) {
    search(
      query: $query
      first: $first
      after: $after
      before: $before
      last: $last
      prefix: $prefix
      productFilters: $productFilters
      reverse: $reverse
      sortKey: $sortKey
      types: PRODUCT
      unavailableProducts: $unavailableProducts
    ) {
      nodes {
        ... on Product {
          ${PRODUCT_DETAILS_METAFIELDS}
        }
      }
      pageInfo {
        endCursor
        hasNextPage
        hasPreviousPage
        startCursor
      }
      productFilters {
        id
        label
        type
        values {
          count
          id
          input
          label
        }
      }
      totalCount
    }
  }
`;

export const GET_PRODUCTS_FOR_DROPDOWN = gql`
  query SearchProducts(
    $query: String!
    $sortKey: ProductSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
  ) @inContext(country: $countryCode) {
    products(query: $query, first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
      edges {
        cursor
        node {
          ${PRODUCT_DETAILS_FOR_DROPDOWN}
        }
      }
    }
  }
`;

export const SEARCH_PRODUCTS_FOR_DROPDOWN = gql`
  query SearchProducts(
    $query: String!
    $sortKey: ProductSortKeys
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
  ) @inContext(country: $countryCode) {
    products(query: $query, first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
      edges {
        cursor
        node {
          ${PRODUCT_DETAILS_FOR_DROPDOWN}
        }
      }
    }
  }
`;
export const SEARCH_COLLECTIONS_FOR_DROPDOWN = gql`
  query GetCollections($sortKey: CollectionSortKeys, $reverse: Boolean, $first: Int!, $after: String, $query: String!) {
    collections(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse, query: $query) {
      edges {
        node {
          ${COLLECTION_DETAILS_FOR_DROPDOWN}
        }
      }
      pageInfo {
        hasNextPage
        endCursor
        startCursor
        hasPreviousPage
      }
    }
  }
`;

export const GET_PRODUCT_FILTERS = gql`
  query GetCollection($collectionHandle: String!, $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language:$languageCode) {
    collectionByHandle(handle: $collectionHandle) {
      products(first: 2) {
        filters {
          id
          label
          type
          values {
            count
            id
            input
            label
          }
        }
      }
    }
  }
`;

export const GET_SEARCH_FILTERS = gql`
  query SearchProductsFilters(
    $query: String!
    $first: Int
    $productFilters: [ProductFilter!]
    $unavailableProducts: SearchUnavailableProductsType
    $countryCode: CountryCode
    $languageCode: LanguageCode
  ) @inContext(country: $countryCode, language: $languageCode) {
    search(
      query: $query
      first: $first
      productFilters: $productFilters
      types: PRODUCT
      unavailableProducts: $unavailableProducts
    ) {
      productFilters {
        id
        label
        type
        values {
          count
          id
          input
          label
        }
      }
      totalCount
    }
  }
`;

export const GET_ALL_COLLECTIONS = gql`
  query GetCollections($sortKey: CollectionSortKeys, $reverse: Boolean, $first: Int!, $after: String, $collectionMetafields: [HasMetafieldsIdentifier!]!) {
    collections(first: $first, after: $after, sortKey: $sortKey, reverse: $reverse) {
      edges {
        node {
          ${COLLECTION_DETAILS_METAFIELDS}
        }
      }
      pageInfo {
        hasNextPage
        endCursor
        startCursor
        hasPreviousPage
      }
    }
  }
`;

export const GET_COLLECTIONS_BY_IDS = gql`
  query GetCollectionsByIds($collectionIds: [ID!]!, $collectionMetafields: [HasMetafieldsIdentifier!]!) {
    nodes(ids: $collectionIds) {
      ... on Collection {
        ${COLLECTION_DETAILS_METAFIELDS}
      }
    }
  }
`;

// FIXME: DEPRECATE
export const GET_COLLECTION_METAFIELD_IMAGES = gql`
  query GetCollectionMetaFieldImages($collectionHandle: String!, $metafieldkey: String!, $metafieldNamespace: String!) {
    collectionByHandle(handle: $collectionHandle) {
      id
      metafield(namespace: $metafieldNamespace, key: $metafieldkey) {
        references(first: 10) {
          edges {
            node {
              ... on MediaImage {
                image {
                  url
                }
              }
            }
          }
        }
      }
    }
  }
`;

// FIXME: DEPRECATE
export const GET_COLLECTIONS_BY_IDS_WITH_META_FIELD = gql`
  query GetCollectionsByIds($collectionIds: [ID!]!, $identifiers: [HasMetafieldsIdentifier!]!) {
    nodes(ids: $collectionIds) {
      ... on Collection {
        ${COLLECTION_DETAILS}
        metafields(identifiers: $identifiers) {
          id
          key
          value
          namespace
          description
          type
        }
      }
    }
  }
`;
