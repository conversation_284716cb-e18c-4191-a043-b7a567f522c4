import gql from 'graphql-tag';
import {PRODUCT_VARIANT} from './product';

const DiscountApplication = `
allocationMethod
targetSelection
targetType
value`;

const OrderDetails = `
  id
  name
  cancelReason
  canceledAt
  currencyCode
  fulfillmentStatus
  currentSubtotalPrice{
    amount
    currencyCode
  }
  currentTotalPrice{
    amount
    currencyCode
  }
  currentTotalDuties{
    amount
    currencyCode
  }
  currentTotalTax{
    amount
    currencyCode
  }
  customerLocale
  email
  customerUrl
  edited
  shippingDiscountAllocations {
    allocatedAmount {
      amount
      currencyCode
    }
    discountApplication {
      ${DiscountApplication}
    }
  }
  subtotalPrice{
    amount
    currencyCode
  }
  orderNumber
  originalTotalDuties{
    amount
    currencyCode
  }
  successfulFulfillments (first: 10) {
    trackingCompany
    trackingInfo (first: 10) {
      number
      url
    }

  }
  originalTotalPrice{
    amount
    currencyCode
  }
  phone
  totalShippingPrice{
    amount
    currencyCode
  }
  processedAt
  totalRefunded{
    amount
    currencyCode
  }
  totalTax{
    amount
    currencyCode
  }
  statusUrl
  financialStatus
  totalPrice{
    amount
    currencyCode
  }
  shippingAddress {
    id
    address1
    address2
    city
    company
    country
    countryCodeV2
    firstName
    formatted
    formattedArea
    lastName
    latitude
    longitude
    name
    phone
    province
    provinceCode
    zip
  }
  discountApplications(first: 10) {
    edges {
      node {
        ... on DiscountCodeApplication {
          code
        }
      }
    }
  }
  lineItems(first: $first, after: $after, reverse: $reverse) {
    pageInfo {
      hasNextPage
      endCursor
      startCursor
      hasPreviousPage
    }
    edges {
      node {
        title
        currentQuantity
        discountedTotalPrice {
          amount
          currencyCode
        }
        originalTotalPrice {
          amount
          currencyCode
        }
        discountAllocations {
          allocatedAmount {
            amount
            currencyCode
          }
          discountApplication {
            ${DiscountApplication}
          }
        }
        quantity
        variant {
          ${PRODUCT_VARIANT}
          product {
            ... on Product {
              id
              productType
              title
              handle
              compareAtPriceRange {
                maxVariantPrice {
                  amount
                  currencyCode
                }
                minVariantPrice {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
}`;

export const GET_ORDERS = gql`
  query GetOrders(
    $reverse: Boolean
    $first: Int!
    $after: String
    $countryCode: CountryCode
    $customerAccessToken: String!
  ) @inContext(country: $countryCode) {
    customer(customerAccessToken: $customerAccessToken) {
      orders(first: $first, after: $after, reverse: $reverse) {
        pageInfo {
          hasNextPage
          endCursor
          startCursor
          hasPreviousPage
        }
        edges {
          cursor
          node {
            ${OrderDetails}
          }
        }
      }
    }
  }
`;
export const GET_ORDER_DETAILS = gql`
  query GetOrders($reverse: Boolean, $first: Int!, $after: String, $countryCode: CountryCode, $orderId: ID!)
  @inContext(country: $countryCode) {
    node(id: $orderId) {
      id
      ... on Order {
        ${OrderDetails}
      }
    }
  }
`;

``;
