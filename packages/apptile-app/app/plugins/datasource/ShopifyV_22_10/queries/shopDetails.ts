import gql from 'graphql-tag';

const IMAGE = `
    altText
    height
    id
    url
    width
`;

const MEDIA_IMAGE = `
    alt
    id
    image {
      ${IMAGE}
    }
    previewImage {
      ${IMAGE}
    }
`;

export const GET_SHOP = gql`
  query GetShop {
    shop {
      id
      name
      description
      moneyFormat
      primaryDomain {
        host
        url
      }
      paymentSettings {
        acceptedCardBrands
        cardVaultUrl
        countryCode
        currencyCode
        enabledPresentmentCurrencies
        shopifyPaymentsAccountId
        supportedDigitalWallets
      }
      privacyPolicy {
        title
        url
      }
      refundPolicy {
        title
        url
      }
      shippingPolicy {
        title
        url
      }
      subscriptionPolicy {
        title
        url
      }
      termsOfService {
        title
        url
      }
      brand {
        coverImage {
          ${MEDIA_IMAGE}
        }
        logo {
          ${MEDIA_IMAGE}
        }
        shortDescription
        slogan
        squareLogo {
          ${MEDIA_IMAGE}
        }
        colors {
          primary {
            background
            foreground
          }
          secondary {
            background
            foreground
          }
        }
      }
      shipsToCountries
    }
  }
`;
