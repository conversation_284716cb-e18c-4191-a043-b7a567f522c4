import gql from 'graphql-tag';

const OrderDetails = `
  id
  name
  cancelReason
  cancelledAt
  createdAt
  currencyCode
  discountApplications(first: 10) {
    nodes {
      allocationMethod
      targetSelection
      targetType
      value {
        ... on MoneyV2 {
          amount
          currencyCode
        }
        ... on PricingPercentageValue {
          percentage
        }
      }
    }
  }
  email
  fulfillments(first: 50) {
    nodes {
      createdAt
      estimatedDeliveryAt
      id
      isPickedUp
      latestShipmentStatus
      requiresShipping
      status
      updatedAt
      fulfillmentLineItems(first: 50) {
        nodes {
          id
          quantity
          lineItem {
            giftCard
            id
            name
            presentmentTitle
            productId
            productType
            quantity
            refundableQuantity
            requiresShipping
            sku
            title
            variantId
            variantTitle
            vendor
          }
        }
      }
    }
  }
  subtotal {
    amount
    currencyCode
  }
  totalPrice {
    amount
    currencyCode
  }
  totalDuties {
    amount
    currencyCode
  }
  totalTax {
    amount
    currencyCode
  }
  edited
  shippingDiscountAllocations {
    allocatedAmount {
      amount
      currencyCode
    }
    discountApplication {
      allocationMethod
      targetSelection
      targetType
      value {
        ... on MoneyV2 {
          amount
          currencyCode
        }
        ... on PricingPercentageValue {
          percentage
        }
      }
    }
  }
  number
  phone
  totalShipping {
    amount
    currencyCode
  }
  processedAt
  totalRefunded {
    amount
    currencyCode
  }
  statusPageUrl
  financialStatus
  shippingAddress {
    address1
    address2
    city
    company
    country
    firstName
    formatted
    formattedArea
    id
    lastName
    name
    phoneNumber
    province
    territoryCode
    zip
    zoneCode
  }
  lineItems(first: 50) {
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    edges {
      cursor
      node {
        giftCard
        id
        name
        presentmentTitle
        productId
        productType
        quantity
        refundableQuantity
        requiresShipping
        sku
        title
        variantId
        variantTitle
        vendor
        price {
          amount
          currencyCode
        }
        totalPrice {
          amount
          currencyCode
        }
        totalDiscount {
          amount
          currencyCode
        }
        discountAllocations {
          allocatedAmount {
            amount
            currencyCode
          }
          discountApplication {
            allocationMethod
            targetSelection
            targetType
            value {
              ... on MoneyV2 {
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                percentage
              }
            }
          }
        }
        image {
          altText
          height
          id
          url(transform: {maxWidth: 480, maxHeight: 480})
          width
        }
        variantOptions {
          name
          value
        }
        currentTotalPrice {
          amount
          currencyCode
        }
      }
    }
  }
`

export const GET_ORDERS = gql`
  query GetOrders($reverse: Boolean, $first: Int!, $after: String, $sortKey: OrderSortKeys) {
    customer {
      orders(first: $first, after: $after, reverse: $reverse, sortKey: $sortKey) {
        pageInfo {
          endCursor
          hasNextPage
          hasPreviousPage
          startCursor
        }
        edges {
          cursor
          node {
            ${OrderDetails}
          }
        }
      }
    }
  }
`;

export const GET_ORDER_DETAILS = gql`
  query GetOrderDetails($orderId: ID!) {
    order(id: $orderId) {
      ${OrderDetails}
    }
  }
`;
