import gql from 'graphql-tag';

export const GET_SHOPIFY_METAOBJECT = gql`
  query Metaobject($id: ID!) {
    metaobject(id: $id) {
      handle
      id
      onlineStoreUrl
      type
      updatedAt
      fields {
        key
        type
        value
      }
    }
  }
`;

export const GET_SHOPIFY_METAOBJECTS = gql`
  query GetMetaobjects($type: String!, $first: Int!) {
    metaobjects(type: $type, first: $first) {
      edges {
        node {
          handle
          id
          type
          updatedAt
          fields {
            key
            value
            type
            reference {
              __typename
              ... on Product {
                id
                handle
                title
                featuredImage {
                  url
                  altText
                }
              }
              ... on Collection {
                id
                handle
                title
                image {
                  url
                  altText
                }
              }
              ... on MediaImage {
                id
                image {
                  url
                  altText
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
  }
`;