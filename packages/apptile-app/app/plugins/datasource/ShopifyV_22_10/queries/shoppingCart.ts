import gql from 'graphql-tag';

export const CART_DETAIL = `{
  id
  checkoutUrl
  createdAt
  updatedAt
  note
  attributes {
    key
    value
  }
  discountCodes {
    applicable
    code
  }  
  discountAllocations {
    targetType
    discountedAmount {
        amount
        currencyCode
    }
    ... on CartAutomaticDiscountAllocation {
        targetType
        title
    }
  }
  cost {
    checkoutChargeAmount {
      amount
      currencyCode
    }
    subtotalAmount {
      amount
      currencyCode
    }
    subtotalAmountEstimated
    totalAmount {
      amount
      currencyCode
    }
    totalAmountEstimated 
    totalDutyAmount {
      amount
      currencyCode
    }
    totalDutyAmountEstimated
    totalTaxAmount {
      amount
      currencyCode
    }
    totalTaxAmountEstimated
   
  }
  buyerIdentity {
    email
    phone
    customer {
      id
    }
    deliveryAddressPreferences {
      ... on MailingAddress{
          address1
          address2
          city
          province
          countryCodeV2
          zip
          firstName
          lastName
          company
          phone
          name
          id
          formattedArea
      }
     }
    countryCode
  }
  lines(first: 250, reverse: true) {
    pageInfo {
      hasNextPage
      endCursor
      startCursor
      hasPreviousPage
    }
    edges {
      node {
        id
        quantity
        discountAllocations{
            discountedAmount{
              amount
              currencyCode
            }
        }
        merchandise {
          ... on ProductVariant {
            id
            title
            currentlyNotInStock
            availableForSale
            price {
              amount
              currencyCode
            }
            unitPrice{
              amount
              currencyCode
            }
            compareAtPrice{
              amount
              currencyCode
            }
            quantityAvailable
            weight
            weightUnit
            product {
              ... on Product {
                id
                title
                productType
                tags
                handle
                availableForSale
                totalInventory
                vendor
                compareAtPriceRange {
                  maxVariantPrice {
                    amount
                    currencyCode
                  }
                  minVariantPrice {
                    amount
                    currencyCode
                  }
                }
              }
            }
            image {
              id
              src
              url
            }
          }
        }
        sellingPlanAllocation {
          sellingPlan {
            id
            name
            recurringDeliveries
          }
          remainingBalanceChargeAmount {
            amount
          }
          priceAdjustments {
            price {
              amount
              currencyCode
            }
            compareAtPrice {
              amount
              currencyCode
            }
            perDeliveryPrice {
              amount
              currencyCode
            }
          }
        }
        attributes {
          key
          value
        }
      }
    }
  }
}`;

export const SHOPPING_CART_CREATE = gql`
  mutation createCart($input: CartInput, $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartCreate(input: $input) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const GET_CART_DETAILS = gql`
  query GetShoppingCartDetails($cartId: ID!, $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cart(id: $cartId) ${CART_DETAIL}
  }
`;

export const GET_CART_LINE_ITEMS = gql`
  query GetShoppingCartLines($cartId: ID!, $reverse: Boolean, $first: Int!, $after: String , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cart${CART_DETAIL}
  }
`;

export const CART_LINES_ADD = gql`
  mutation cartLinesAdd($cartId: ID!, $lines: [CartLineInput!]! , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartLinesAdd(cartId: $cartId, lines: $lines) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_LINES_REMOVE = gql`
  mutation cartLinesRemove($cartId: ID!, $lineIds: [ID!]! , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartLinesRemove(cartId: $cartId, lineIds: $lineIds) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_LINES_UPDATE = gql`
  mutation cartLinesUpdate($cartId: ID!, $lines: [CartLineUpdateInput!]! , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartLinesUpdate(cartId: $cartId, lines: $lines) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_NOTE_UPDATE = gql`
  mutation cartNoteUpdate($cartId: ID!, $note: String! , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartNoteUpdate(cartId: $cartId, note: $note) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_DISCOUNT_CODE_UPDATE = gql`
  mutation cartDiscountCodesUpdate($cartId: ID!, $discountCodes: [String!] , $countryCode: CountryCode, $languageCode: LanguageCode) @inContext(country: $countryCode, language: $languageCode){
    cartDiscountCodesUpdate(cartId: $cartId, discountCodes: $discountCodes) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_BUYER_IDENTITY_UPDATE = gql`
  mutation cartBuyerIdentityUpdate($buyerIdentity: CartBuyerIdentityInput!, $cartId: ID!) {
    cartBuyerIdentityUpdate(buyerIdentity: $buyerIdentity, cartId: $cartId) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;

export const CART_ATTRIBUTES_UPDATE = gql`
  mutation cartAttributesUpdate($attributes: [AttributeInput!]!, $cartId: ID!) {
    cartAttributesUpdate(attributes: $attributes, cartId: $cartId) {
      cart${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;
