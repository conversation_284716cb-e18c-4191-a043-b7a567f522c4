import {call, delay, put, select, spawn} from 'redux-saga/effects';
import {modelUpdateAction, navigateToScreen} from 'apptile-core';
import {PluginConfigType} from 'apptile-core';
import {
  SHOPIFY_CONFIG_CART_ID_KEY_DEFAULT,
  SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE,
  SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY,
  SHOPIFY_MODEL_CART_ERROR_KEY,
  SHOPIFY_MODEL_CART_ID_KEY,
  SHOPIFY_MODEL_CART_ITEMS_KEY,
  SHOPIFY_MODEL_CART_OBJECT_KEY,
  SHOP<PERSON>Y_M<PERSON><PERSON>_CART_WITH_SUBSCRIPTION_ITEM_KEY,
  SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY,
  SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY,
  SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY,
} from '../constants';
import {LocalStorage} from 'apptile-core';
import recentlyViewedAction from '../actions/shopifyRecentlyViewedActions';
import {GET_SHOP} from '../queries/shopDetails';
import {triggerAction} from 'apptile-core';
import _ from 'lodash';
import {getCurrencyByCode} from '@/root/web/common/currencyConstants';

export function* initShopifyGenerator(dsConfig: PluginConfigType<any>, queryRunner: any, appId: string) {
  // logger.time('[TIMER] initShopifyGenerator');
  const state = yield select();
  var pageModels = state.stageModel.getModelValue([]);
  const dsModelValues = pageModels.get(dsConfig.get('id'));

  const cartIdLocalStorageKey =
    dsConfig.config.get(SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE) || SHOPIFY_CONFIG_CART_ID_KEY_DEFAULT;
  let cartId: string | null | undefined = null;
  if (cartIdLocalStorageKey) {
    cartId = yield call(LocalStorage.getValue, cartIdLocalStorageKey);
  }

  if (!cartId) {
    // const cartLineItemsLocalStorageKey = dsConfig.config.get(SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE);
    // let currentCartLineItems: any[] = [];
    // if (cartLineItemsLocalStorageKey) {
    //   currentCartLineItems = yield call(LocalStorage.getValue, cartLineItemsLocalStorageKey);
    // }

    // [NOTE] Try to read cart Id from legacy LocalStorage value of CartObject.
    const cartLocalStorageKey = dsConfig.config.get(SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE);
    let currentCart: any = null;
    if (cartLocalStorageKey) {
      currentCart = yield call(LocalStorage.getValue, cartLocalStorageKey);
      if (currentCart && currentCart?.id) {
        cartId = currentCart?.id;
        yield call(LocalStorage.setValue, cartIdLocalStorageKey, cartId);
        yield call(LocalStorage.removeItem, cartLocalStorageKey);
      }
    }
  }
  logger.info('[SHOPIFY] Loaded from Local Storage, Cart Id: ', cartId);

  const checkoutLocalStorageKey = dsConfig.config.get(SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE);
  let currentCheckout: any = null;
  if (checkoutLocalStorageKey) {
    currentCheckout = yield call(LocalStorage.getValue, checkoutLocalStorageKey);
  }

  let shopData;

  // logger.info('[DEBUG] Shopify: Fetching Shop data');
  const previousShopData = yield call(LocalStorage.getValue, 'shopData');
  if (previousShopData) {
    let moneyFormat = previousShopData.data.shop.moneyFormat;
    const selectedCurrency = yield call(LocalStorage.getValue, 'selectedCurrency');
    if (selectedCurrency && moneyFormat) {
      const currency = getCurrencyByCode(selectedCurrency);
      if (currency && currency.symbol) {
        moneyFormat = `${currency.symbol} ${moneyFormat.slice(moneyFormat.indexOf('{{'), moneyFormat.length)}`;
      }
      previousShopData.data.shop.moneyFormat = moneyFormat;
    }
  }
  if (!previousShopData) {
    // logger.info('[DEBUG] Shopify: Running Shop data query');
    const shopDataQuery = async () => {
      return queryRunner.runQuery('query', GET_SHOP, {}, {}).then(async (res: any) => {
        // logger.info('[DEBUG] Shopify: Ran Shop data query');
        let moneyFormat = res.data.shop.moneyFormat;
        const selectedCurrency = await LocalStorage.getValue('selectedCurrency');
        if (selectedCurrency && moneyFormat) {
          const currency = getCurrencyByCode(selectedCurrency);
          if (currency && currency.symbol) {
            moneyFormat = `${currency.symbol} ${moneyFormat.slice(moneyFormat.indexOf('{{'), moneyFormat.length)}`;
          }
          res.data.shop.moneyFormat = moneyFormat;
        }
        LocalStorage.setValue('shopData', {data: res.data});
        return;
      });
    };
    yield call(shopDataQuery);
    shopData = yield call(LocalStorage.getValue, 'shopData');
  } else {
    // logger.info('[DEBUG] Shopify: Fetched Shop data from storage.');
    shopData = previousShopData;
    // Refresh shop data anyway without blocking, for next boot.
    queryRunner.runQuery('query', GET_SHOP, {}, {}).then(async (res: any) => {
      // logger.info('[DEBUG] Shopify: Ran Shop data query');
      let moneyFormat = res.data.shop.moneyFormat;
      const selectedCurrency = await LocalStorage.getValue('selectedCurrency');
      if (selectedCurrency && moneyFormat) {
        const currency = getCurrencyByCode(selectedCurrency);
        if (currency && currency.symbol) {
          moneyFormat = `${currency.symbol} ${moneyFormat.slice(moneyFormat.indexOf('{{'), moneyFormat.length)}`;
        }
        res.data.shop.moneyFormat = moneyFormat;
      }
      LocalStorage.setValue('shopData', {data: res.data});
    });
  }

  yield spawn(function* () {
    yield delay(3000);
    const currentState = yield select();
    var currentModel = currentState.stageModel.getModelValue([]);
    const dsModel = currentModel.get(dsConfig.get('id'));
    yield put(
      triggerAction({
        pluginConfig: dsConfig,
        pluginModel: dsModel,
        pluginSelector: [dsConfig.get('id')],
        eventModelJS: {
          value: 'verifyAuthSession',
          params: {
            skipPostSignOutRedirect: true,
          },
        },
      }),
    );
  });

  yield spawn(function* () {
    yield delay(1000);
    const currentState = yield select();
    var currentModel = currentState.stageModel.getModelValue([]);
    const dsModel = currentModel.get(dsConfig.get('id'));
    yield put(
      triggerAction({
        pluginConfig: dsConfig,
        pluginModel: dsModel,
        pluginSelector: [dsConfig.get('id')],
        eventModelJS: {
          value: 'initCart',
        },
      }),
    );
  });

  const recentlyViewList = yield call(recentlyViewedAction.getRecentlyViewedList);
  yield spawn(function* () {
    yield delay(3000);
    const currentState = yield select();
    var currentModel = currentState.stageModel.getModelValue([]);
    const dsModel = currentModel.get(dsConfig.get('id'));
    yield put(
      triggerAction({
        pluginConfig: dsConfig,
        pluginModel: dsModel,
        pluginSelector: [dsConfig.get('id')],
        eventModelJS: {
          value: 'populateRecentlyViewed',
        },
      }),
    );
  });

  yield put(
    modelUpdateAction([
      {
        selector: [dsConfig.get('id'), 'shop'],
        newValue: shopData?.data?.shop,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_ID_KEY],
        newValue: cartId,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_ERROR_KEY],
        newValue: cartId,
      },
      // {
      //   selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_ITEMS_KEY],
      //   newValue: currentCartLineItems,
      // },
      // {
      //   selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_OBJECT_KEY],
      //   newValue: currentCart,
      // },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY],
        newValue: currentCheckout,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY],
        newValue: false,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY],
        newValue: false,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY],
        newValue: '',
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY],
        newValue: recentlyViewList,
      },
      {
        selector: [dsConfig.get('id'), SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY],
        newValue: true,
      },
      {
        selector: [dsConfig.get('id'), 'loggedInUser'],
        newValue: (yield call(LocalStorage.getValue, 'loggedInUser')) ?? null,
      },
      {
        selector: [dsConfig.get('id'), 'loggedInUserAccessToken'],
        newValue: (yield call(LocalStorage.getValue, 'loggedInUserAccessToken')) ?? null,
      },
      {
        selector: [dsConfig.get('id'), 'isloggedInUser'],
        newValue: false,
      },
      {
        selector: [dsConfig.get('id'), 'countryCode'],
        newValue: (yield call(LocalStorage.getValue, 'countryCode')) ?? dsModelValues.get('countryCode'),
      },
      {
        selector: [dsConfig.get('id'), 'selectedCurrency'],
        newValue: (yield call(LocalStorage.getValue, 'selectedCurrency')) ?? dsModelValues.get('selectedCurrency'),
      },
    ]),
  );
  // logger.timeEnd('[TIMER] initShopifyGenerator');

  // Check for mandatory login after a short delay to ensure all components are loaded
  yield spawn(function* () {
    // yield delay(1500);
    const currentState = yield select();
    var currentModel = currentState?.stageModel.getModelValue([]);
    const dsModel = currentModel.get(dsConfig.get('id'));
    const mandatoryLogin = dsConfig.config.get('mandatoryLogin');
    const mandatoryLoginRedirectScreenId = dsConfig.config.get('mandatoryLoginRedirectScreenId');
    const useCustomerApi = dsConfig.config.get('useCustomerApi');
    const customerApiLoginScreen = dsConfig.config.get('customerApiLoginScreen');
    const loggedInUser = dsModel.get('loggedInUser');
    if (!loggedInUser && mandatoryLogin && useCustomerApi && customerApiLoginScreen) {
      yield put(
        navigateToScreen(customerApiLoginScreen, {
          source: 'mandatoryLogin',
        }),
      );
    }
    // If mandatory login is enabled and user is not logged in, redirect to the mandatory login screen
    if (!loggedInUser && mandatoryLogin && mandatoryLoginRedirectScreenId) {
      yield put(
        navigateToScreen(mandatoryLoginRedirectScreenId, {
          source: 'mandatoryLogin',
        }),
      );
    }
  });
}
