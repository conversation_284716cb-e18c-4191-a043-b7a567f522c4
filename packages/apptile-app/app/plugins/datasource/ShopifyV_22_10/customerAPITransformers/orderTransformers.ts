import _ from 'lodash';
import {formatDisplayPrice, flattenConnection, formatQueryReturn, jsonObjectMapper} from '../utils/utils';
import {IOrder, IOrderLineItem, FulfillmentTrackingInfo, ICartProductVariant, IProduct} from '../types';

const TransformOrderLineItem = (data: any, context: any): IOrderLineItem | undefined => {
  if (!data) return;

  try {
    const {discountAllocations, variantId, productId, image} = data;
    const lineItemDiscountSum =
      discountAllocations && _.sum(discountAllocations.map(discount => parseFloat(discount.allocatedAmount?.amount)));

    const schema = [
      {
        field: 'currentQuantity',
        path: 'quantity',
      },
      {
        field: 'discountedTotalPrice',
        path: 'currentTotalPrice.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'originalTotalPrice',
        path: 'totalPrice.amount',
        transform: (value: string) => parseFloat(value),
      },
      'quantity',
      'title',
      'sku',
      'productType',
      'vendor',
      {
        field: 'displayDiscountedTotalPrice',
        path: 'currentTotalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayOriginalTotalPrice',
        path: 'totalPrice.amount',
        transform: formatDisplayPrice(context),
      },
    ];

    const variant: ICartProductVariant = {
      id: variantId,
      product: {
        id: productId,
        title: data.title,
        handle: '', // Not available in the current API response
        totalInventory: 0, // Not available in the current API response
        productType: data.productType,
        vendor: data.vendor,
        tags: [], // Not available in the current API response
      },
      price: parseFloat(data?.price?.amount),
      salePrice: parseFloat(data?.totalPrice?.amount),
      displayPrice: formatDisplayPrice(context)(data?.price?.amount),
      displaySalePrice: formatDisplayPrice(context)(data?.totalPrice?.amount),
      featuredImage: image?.url,
      image: image
        ? {
            altText: image.altText,
            url: image.url,
            src: image.url,
            width: image.width,
            height: image.height,
          }
        : undefined,
      title: data.variantTitle
    };

    return {
      ...jsonObjectMapper(schema, data),
      lineItemDiscount: lineItemDiscountSum,
      displayLineItemDiscount: formatDisplayPrice(context)(lineItemDiscountSum),
      variant,
    };
  } catch (err) {
    console.error(err);
  }
};

function TransformFulfillmentInfo(fulfillments: any[]): FulfillmentTrackingInfo[] {
  return (
    fulfillments?.flatMap(({fulfillmentLineItems}) =>
      fulfillmentLineItems.nodes.map(({lineItem}) => ({
        number: lineItem.sku,
        url: '', // Customer Account API doesn't provide tracking URL
      })),
    ) || []
  );
}

export const TransformOrder = (data: any, context: any): IOrder | undefined => {
  if (!data) return;

  try {
    const OrderStateMap: any = {
      UNFULFILLED: 'OPEN',
      PARTIALLY_FULFILLED: 'CONFIRMED',
      FULFILLED: 'COMPLETED',
      CANCELLED: 'CANCELLED',
    };

    const PaymentStateMap: any = {
      AUTHORIZED: 'AUTHORIZED',
      PAID: 'PAID',
      PARTIALLY_PAID: 'PARTIALLY_PAID',
      PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
      PENDING: 'PENDING',
      REFUNDED: 'REFUNDED',
      VOIDED: 'VOIDED',
    };

    const {
      shippingAddress,
      fulfillments,
      financialStatus,
      lineItems,
      shippingDiscountAllocations,
      discountApplications,
    } = data;

    const shippingDiscount =
      shippingDiscountAllocations &&
      _.sum(shippingDiscountAllocations.map(discount => parseFloat(discount.allocatedAmount?.amount)));

    const schema = [
      'id',
      'name',
      'number',
      'phone',
      'createdAt',
      'statusPageUrl',
      'currencyCode',
      'email',
      {
        field: 'customerEmail',
        path: 'email',
      },
      {
        field: 'orderNumber',
        path: 'number',
      },
      {
        field: 'statusUrl',
        path: 'statusPageUrl',
      },
      {
        field: 'subtotalPrice',
        path: 'subtotal.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'totalPrice',
        path: 'totalPrice.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'totalRefunded',
        path: 'totalRefunded.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'totalShippingPrice',
        path: 'totalShipping.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'totalTax',
        path: 'totalTax.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'currentTotalDuties',
        path: 'totalDuties.amount',
        transform: (value: string) => parseFloat(value),
      },
      {
        field: 'displaySubtotalPrice',
        path: 'subtotal.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalPrice',
        path: 'totalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalRefunded',
        path: 'totalRefunded.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalShippingPrice',
        path: 'totalShipping.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalTax',
        path: 'totalTax.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayCurrentTotalDuties',
        path: 'totalDuties.amount',
        transform: formatDisplayPrice(context),
      },
    ];

    const transformedDiscountApplications = discountApplications?.nodes.map(app => ({
      applicable: true, // Assuming all applications in the response are applicable
      code: '', // Code is not provided in the current API response
      discountAmount: app.value.amount || (app.value.percentage ? `${app.value.percentage}%` : ''),
      displayDiscountAmount: formatDisplayPrice(context)(app.value.amount) || `${app.value.percentage}%`,
    }));

    return {
      ...jsonObjectMapper(schema, data),
      shippingDiscount,
      shippingAddress,
      orderState: OrderStateMap[fulfillments?.nodes[0]?.status || 'UNFULFILLED'],
      fulfillmentInfo: TransformFulfillmentInfo(fulfillments?.nodes || []),
      paymentState: financialStatus && PaymentStateMap[financialStatus],
      lineItems: flattenConnection(lineItems)?.map(line => TransformOrderLineItem(line, context)),
      discountApplications: transformedDiscountApplications,
    };
  } catch (err) {
    console.error(err);
  }
};

export const TransformCustomerApiOrders = (data: any, context: any) => {
  const customer = _.get(data, 'customer', {});
  const orderEdge = _.get(customer, 'orders', {});

  const {hasNextPage, endCursor: after} = _.get(orderEdge, 'pageInfo', {});

  const orders = flattenConnection(orderEdge)?.map(o => TransformOrder(o, context));

  console.log('MARKER', orders);
  return formatQueryReturn(orders, orderEdge, {after}, hasNextPage);
};

export const TransformCustomerApiOrder = (data: any, context: any) => {
  const order = _.get(data, 'order', {});

  const result = TransformOrder(order, context);

  console.log('MARKER', data);
  console.log('MARKER', order);
  console.log('MARKER', result);
  return formatQueryReturn(result, data);
};
