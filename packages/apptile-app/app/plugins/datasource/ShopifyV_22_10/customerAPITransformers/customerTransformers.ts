import _ from 'lodash';
import {ICustomer, IShippingAddress} from '../types';
import {
  flattenConnection,
  formatQueryReturn,
  jsonArrayMapperWithCallback,
  jsonObjectMapper,
  JSONMapperSchema,
  ShopifyQueryReturnValue,
} from '../utils/utils';

export const TransformCustomerAccountAddress = (data?: any): IShippingAddress | undefined => {
  if (!data) return;

  const addressSchema = [
    'id',
    'firstName',
    'lastName',
    'address1',
    'address2',
    'city',
    'company',
    'country',
    {
      field: 'state',
      path: 'province',
    },
    {
      field: 'provinceCode',
      path: 'territoryCode',
    },
    {
      field: 'zip',
      path: 'zoneCode',
    },
    {
      field: 'phone',
      path: 'phoneNumber',
    },
    {
      field: 'formatted',
      path: 'formatted',
      transform: data => {
        if (data && Array.isArray(data)) return data.join(', ');
        return data;
      },
    },
    'formattedArea',
  ];

  return jsonObjectMapper(addressSchema, data);
};

export const TransformCustomerAccountCustomer = (customerData: any): ICustomer => {
  const customerSchema: JSONMapperSchema = [
    'id',
    'firstName',
    'lastName',
    'creationDate',
    'displayName',
    {
      field: 'email',
      path: 'emailAddress.emailAddress',
    },
    {
      field: 'emailAddress',
      path: 'emailAddress',
    },
    {
      field: 'acceptsMarketing',
      path: 'emailAddress.marketingState',
      transform: (marketingState: string) => marketingState === 'SUBSCRIBED',
    },
    {
      field: 'phone',
      path: 'phoneNumber.phoneNumber',
    },
    'phoneNumber',
    'tags',
    {
      field: 'defaultAddress',
      path: 'defaultAddress',
      transform: TransformCustomerAccountAddress,
    },
    {
      field: 'addresses',
      path: 'addresses.nodes',
      transform: (addresses: any) => {
        return jsonArrayMapperWithCallback(addresses, TransformCustomerAccountAddress);
      },
    },
  ];

  return jsonObjectMapper(customerSchema, customerData);
};

export const TransformCustomerApiGetCustomerQuery = (data: any): ShopifyQueryReturnValue<ICustomer> => {
  const customerData = _.get(data, 'customer', {});
  const tData: ICustomer = TransformCustomerAccountCustomer(customerData);

  return formatQueryReturn<ICustomer>(tData, customerData);
};

export const TransformCustomerApiUpdateCustomerQuery = (data: any) => {
  const customerResult = _.get(Object.entries(data), '0.1', {});
  const {customer, userErrors} = customerResult ?? {};

  const tData = TransformCustomerAccountCustomer(customer);

  return formatQueryReturn(tData, customer);
};

export const TransformStorefrontCustomerAccessTokenMutation = (data: any): any => {
  const storefrontAccessTokenData = _.get(data, 'storefrontCustomerAccessTokenCreate', {});
  const storefrontAccessTokenSchema: JSONMapperSchema = ['customerAccessToken'];

  const formattedData = jsonObjectMapper(storefrontAccessTokenSchema, storefrontAccessTokenData);
  return formatQueryReturn(formattedData, storefrontAccessTokenData, {}, false, storefrontAccessTokenData?.userErrors);
};

export const TransformCustomerApiGetCustomerAddresses = (data: any) => {
  const addressData = (data && data?.customer) ?? {};

  const pageInfo = addressData?.addresses?.pageInfo ?? {};
  const {hasNextPage, endCursor: after} = pageInfo;

  const addresses = flattenConnection(addressData?.addresses);

  const tAddresses = jsonArrayMapperWithCallback(addresses, TransformCustomerAccountAddress);

  const paginationMeta = {after};

  return formatQueryReturn(tAddresses, addresses, paginationMeta, hasNextPage);
};

export const TransformCustomerApiGetCustomerDefaultAddresses = (data: any) => {
  const addressData = (data && data?.customer) ?? {};

  const {defaultAddress} = addressData ?? {};

  const tAddresses = TransformCustomerAccountAddress(defaultAddress);

  console.log(tAddresses);
  return formatQueryReturn(tAddresses, defaultAddress);
};

export const TransformCustomerApiCustomerAddressMutation = (data: any) => {
  const customerResult = _.get(Object.entries(data), '0.1', {});

  const {customerAddress, userErrors} = customerResult ?? {};
  console.log(customerAddress, 'MARKER');

  const tAddresses = TransformCustomerAccountAddress(customerAddress);
  console.log(tAddresses, 'MARKER');

  console.log(tAddresses);
  return formatQueryReturn(tAddresses, customerAddress, {}, false, userErrors);
};

export const TransformCustomerApiDeleteCustomerAddress = (data: any) => {
  const deleteAddressRaw = data?.customerAddressDelete;
  const {deletedAddressId, userErrors} = deleteAddressRaw ?? {};
  return formatQueryReturn({deletedCustomerAddressId: deletedAddressId}, deleteAddressRaw, {}, false, userErrors);
};
