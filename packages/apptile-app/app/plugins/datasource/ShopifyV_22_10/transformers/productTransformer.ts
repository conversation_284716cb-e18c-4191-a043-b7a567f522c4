import _ from 'lodash';
import {TransformerFunction} from '..';
import {ICollectionMetafieldImage, IProductSellingPlan, IProductSellingPlanGroup} from '../types';
import * as ShopifyGenerated from '../generated/graphql';
import {
  ICollection,
  ICollectionWithImages,
  IProduct,
  IProductDetail,
  IProductFilters,
  IProductMetaFields,
  IProductVariant,
} from '../types';
import {
  flattenConnection,
  formatQueryReturn,
  jsonArrayMapper,
  jsonArrayMapperWithCallback,
  jsonObjectMapper,
  formatDisplayPrice,
  convertStringToNumber,
  JSONMapperSchema,
} from '../utils/utils';
import {ImmutableMapType} from 'apptile-core';
import {ProductCacheByHandle} from '../cache/ProductCacheByHandle';
import {TransformMetafields} from './metafieldsTransformer';

const productVariantContextInjector = (context: any) => {
  return (data: ShopifyGenerated.ProductVariant) => {
    return TransformProductVariant(data, context);
  };
};

export const TransformProductVariant = (
  data: ShopifyGenerated.ProductVariant,
  context: any,
): IProductVariant | undefined => {
  if (!data) return;
  const {image, selectedOptions, sellingPlanAllocations, metafields} = data;
  const productVariantSchema: JSONMapperSchema = [
    'id',
    'title',
    'barcode',
    'availableForSale',
    'sku',
    'weight',
    'weightUnit',
    {
      field: 'quantityAvailableForSale',
      path: 'quantityAvailable',
      transform: convertStringToNumber,
    },
    {
      field: 'isInStock',
      path: 'currentlyNotInStock',
      transform: (value: boolean) => !value,
    },
    {
      field: 'featuredImage',
      path: 'image.url',
    },
    {
      field: 'price',
      path: 'compareAtPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'salePrice',
      path: 'price.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayPrice',
      path: 'compareAtPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displaySalePrice',
      path: 'price.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
  ];
  const productVariantImageSchema = [
    'id',
    'altText',
    {
      field: 'src',
      path: 'url',
    },
  ];
  const productVariantSellingPlanAllocationSchema = [
    {
      field: 'id',
      path: 'sellingPlan.id',
    },
    {
      field: 'name',
      path: 'sellingPlan.name',
    },
    {
      field: 'value',
      path: 'sellingPlan.options.0.value',
    },
    {
      field: 'price',
      path: 'priceAdjustments.0.compareAtPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'salePrice',
      path: 'priceAdjustments.0.price.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'perDeliveryPrice',
      path: 'priceAdjustments.0.perDeliveryPrice.amount',
      transform: convertStringToNumber,
    },
  ];

  const tMetafields = TransformMetafields(metafields);

  return {
    ...jsonObjectMapper(productVariantSchema, data),
    metafields: tMetafields,
    image: jsonObjectMapper(productVariantImageSchema, image),
    variantOptions: jsonArrayMapper(['name', 'value'], selectedOptions),
  };
};

export const TransformProductMedia = (data: ShopifyGenerated.Media): IProductVariant | undefined => {
  const {previewImage} = data ?? {};
  const sources = data?.sources ?? [];
  return {
    ...jsonObjectMapper(['alt', 'mediaContentType', 'id', 'host', 'embeddedUrl'], data),
    previewImage: jsonObjectMapper(['id', 'altText', 'height', 'width', 'url'], previewImage),
    sources: jsonArrayMapper(['format', 'height', 'width', 'url', 'mimeType'], sources),
  };
};

export const TransformProductSellingPlan = (data: ShopifyGenerated.SellingPlan): IProductSellingPlan | undefined => {
  const {priceAdjustments, ...psellingPlan} = data ?? {};
  const priceAdjustmentsSchema = [
    'orderCount',
    {
      field: 'sellingPlanPercentagePriceAdjustment',
      path: 'adjustmentValue.adjustmentPercentage',
    },
  ];
  const sellingPlanSchema = ['id', 'name', 'description', 'recurringDeliveries'];

  const result = {
    ...jsonObjectMapper(sellingPlanSchema, psellingPlan),
    priceAdjustments: jsonArrayMapper(priceAdjustmentsSchema, priceAdjustments),
  };
  return result;
};

export const TransformProductSellingPlanGroup = (
  data: ShopifyGenerated.SellingPlanGroup,
): IProductSellingPlanGroup | undefined => {
  const {name, sellingPlans} = data;
  const psellingPlans = flattenConnection(sellingPlans);

  return {
    name,
    sellingPlans: jsonArrayMapperWithCallback(psellingPlans, TransformProductSellingPlan),
  };
};

//   {
//     minPrice: string; //strikeoff price
//     maxPrice: string; //strikeoff price
//     maxSalePrice: string; // discounted sale price != strikeoff price
//     minSalePrice: string; // discounted sale price != strikeoff price
//   }
export const TransformProduct = (
  data: ShopifyGenerated.Maybe<ShopifyGenerated.Product> | undefined,
  context: any,
): IProduct | undefined => {
  if (!data) return;
  const {images} = data;
  const flatImages = flattenConnection(images);
  const productSchema = [
    'id',
    'title',
    'handle',
    'description',
    'descriptionHtml',
    'availableForSale',
    'onlineStoreUrl',
    'productType',
    'createdAt',
    {
      field: 'minPrice',
      path: 'compareAtPriceRange.minVariantPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'maxPrice',
      path: 'compareAtPriceRange.maxVariantPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'maxSalePrice',
      path: 'priceRange.maxVariantPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'minSalePrice',
      path: 'priceRange.minVariantPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayMinPrice',
      path: 'compareAtPriceRange.minVariantPrice.amount',
      transform: formatDisplayPrice(context),
    },
    {
      field: 'displayMaxPrice',
      path: 'compareAtPriceRange.maxVariantPrice.amount',
      transform: formatDisplayPrice(context),
    },
    {
      field: 'displayMinSalePrice',
      path: 'priceRange.minVariantPrice.amount',
      transform: formatDisplayPrice(context),
    },
    {
      field: 'displayMaxSalePrice',
      path: 'priceRange.maxVariantPrice.amount',
      transform: formatDisplayPrice(context),
    },
    'vendor',
    'totalInventory',
  ];

  const productData: IProduct = Object.assign(
    jsonObjectMapper(productSchema, data),
    {featuredImage: flatImages[0]?.url},
  );
  return productData;
};

export const TransformCollection = (
  data: ShopifyGenerated.Maybe<ShopifyGenerated.Product> | undefined,
): ICollection | undefined => {
  if (!data) return;
  const {metafields} = data;

  const collectionSchema = [
    'id',
    'title',
    'handle',
    {
      field: 'featuredImage',
      path: 'image.url',
    },
    'description',
    'descriptionHtml',
  ];

  const collectionData: ICollection = {
    ...jsonObjectMapper(collectionSchema, data),
    metafields: TransformMetafields(metafields),
  };

  return collectionData;
};

export const TransformProductMetaField = (data: ShopifyGenerated.Metafield): IProductMetaFields | undefined => {
  if (!data) return;
  const schema = ['id', 'key', 'value', 'namespace', 'description', 'type'];

  return jsonObjectMapper(schema, data);
};

export const TransformProductFilter = (data: ShopifyGenerated.Filter): IProductFilters | undefined => {
  if (!data) return;

  const {values} = data;

  const schema = ['id', 'label', 'type'];

  const valueSchema = ['id', 'count', 'input', 'label'];

  return {
    ...jsonObjectMapper(schema, data),
    values: values.map(value => jsonObjectMapper(valueSchema, value)),
  };
};
//TODO: Complete this function
export const TransformCollectionWithImages = (
  data: ShopifyGenerated.Maybe<ShopifyGenerated.Product> | undefined,
): ICollection | undefined => {
  if (!data) return;

  const collectionData: ICollectionWithImages = {
    ...TransformCollection(data),
  };

  return collectionData;
};

export const TransformProductDetail = (
  data: ShopifyGenerated.Maybe<ShopifyGenerated.Product> | undefined,
  context: any,
  model?: ImmutableMapType,
): IProductDetail | undefined => {
  if (!data) return;
  const {options, collections, images, media, variants, tags, metafields} = data;
  const productImages = flattenConnection(images);
  const pCollections = flattenConnection(collections);
  const pMedia = flattenConnection(media);
  const pVariants = flattenConnection(variants);

  const productImageSchema = [
    'id',
    'altText',
    'src',
    'thumbnail',
    {
      field: 'src',
      path: 'url',
    },
  ];

  const productData: IProductDetail = Object.assign({}, TransformProduct(data, context), {
    collections: jsonArrayMapper(['id', 'title', 'handle'], pCollections),
    tags: tags,
    images: jsonArrayMapper(productImageSchema, productImages),
    media: jsonArrayMapperWithCallback(pMedia, TransformProductMedia),
    productOptions: jsonArrayMapper(['id', 'name', 'values'], options),
    variants: jsonArrayMapperWithCallback(pVariants, productVariantContextInjector(context)),
    metafields: TransformMetafields(metafields),
  });

  if (model) {
    let cacheController: ProductCacheByHandle = model.get('productCacheController');
    cacheController?.setProductCache(productData?.handle, productData);
  }

  return productData;
};

export const TransformProductQueries: TransformerFunction = (data: ShopifyGenerated.QueryRoot, context, model) => {
  const product = _.get(Object.entries(data), '0.1', {});

  const result = TransformProductDetail(product, context, model);

  return formatQueryReturn(result, product);
};

export const TransformGetProductsQuery: TransformerFunction = (data: ShopifyGenerated.QueryRoot, context, model) => {
  const products = _.get(Object.entries(data), '0.1', {});

  const result = products.map((product: any) => TransformProductDetail(product, context, model));

  return formatQueryReturn(result, products);
};

export const TransformGetCollections: TransformerFunction = (data: ShopifyGenerated.QueryRoot) => {
  const collectionsData = _.get(Object.entries(data), '0.1', {});
  const {hasNextPage, endCursor: after} = _.get(collectionsData, 'pageInfo', {});
  const edges = flattenConnection(collectionsData);
  const result = edges.map((collection: any) => TransformCollectionWithImages(collection));
  return formatQueryReturn(result, data, {after}, hasNextPage);
};

export const TransformSearchCollections: TransformerFunction = (data: ShopifyGenerated.QueryRoot) => {
  const collectionsData = _.get(Object.entries(data), '0.1', {});
  const {hasNextPage, endCursor: after} = _.get(collectionsData, 'pageInfo', {});
  const edges = flattenConnection(collectionsData);
  const result = edges.map((collection: any) => ({...(TransformCollectionWithImages(collection)), products: collection?.products?.edges?.map((product: any) => TransformProductDetail(product?.node, null))}));
  return formatQueryReturn(result, data, {after}, hasNextPage);
};

export const TransformGetProductsPaginatedQuery: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context,
  model,
) => {
  const products = _.get(Object.entries(data), '0.1', {});

  const {hasNextPage, endCursor: after} = _.get(products, 'pageInfo', {});
  const edges = flattenConnection(products);

  const result = edges.map((product: any) => TransformProductDetail(product, context, model));

  return formatQueryReturn(result, data, {after}, hasNextPage);
};

export const TransformGetCollectionProductsQuery: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context: any,
  model,
) => {
  const collectionData = _.get(Object.entries(data), '0.1', {});

  const products = _.get(collectionData, 'products', {});

  const {hasNextPage, endCursor: after} = _.get(products, 'pageInfo', {});
  const edges = flattenConnection(products);

  const result = edges.length > 0 ? edges.map((product: any) => TransformProductDetail(product, context, model)) : [];

  return formatQueryReturn(result, data, {after}, hasNextPage);
};

export const TransformCollectionDetailsByHandle: TransformerFunction = (data: ShopifyGenerated.QueryRoot) => {
  const collectionData = _.get(Object.entries(data), '0.1', {});

  const result = TransformCollectionWithImages(collectionData);

  return formatQueryReturn(result, data);
};

export const TransformProductMetafields: TransformerFunction = (data: ShopifyGenerated.Product) => {
  const product = _.get(Object.entries(data), '0.1', {});
  const {metafields = []} = product;

  const result = metafields.filter(
    (metafield: ShopifyGenerated.Metafield) => metafield && TransformProductMetaField(metafield),
  );

  return formatQueryReturn(result, data);
};

export const TransformProductFilters: TransformerFunction = (data: ShopifyGenerated.QueryRoot) => {
  const {products = {}} = _.get(Object.entries(data), '0.1', {});

  const {filters = []} = products;

  const result = filters.map((filter: ShopifyGenerated.Filter) => TransformProductFilter(filter));

  return formatQueryReturn(result, data);
};

export const TransformGetProductVariantByOptions: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context: any,
) => {
  const products = _.get(Object.entries(data), '0.1', {});

  const {variantBySelectedOptions = {}} = products;

  const result = TransformProductVariant(variantBySelectedOptions, context);

  return formatQueryReturn(result, data);
};

export const TransformGetCollectionMetaFieldImages: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context: any,
) => {
  const imageNodes = _.get(data, 'collectionByHandle.metafield.references', []);
  const tImages: ICollectionMetafieldImage[] = jsonArrayMapperWithCallback(
    flattenConnection(imageNodes),
    previewImage => {
      return jsonObjectMapper(
        [
          {
            field: 'url',
            path: 'image.url',
          },
        ],
        previewImage,
      );
    },
  );
  return formatQueryReturn(tImages, data);
};

export const TransformGetProductVariantsQuery: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context,
  model,
) => {
  const variants = _.get(Object.entries(data), '0.1', {});
  const result = variants.map((variant: any) => TransformProductVariant(variant, context, model));

  return formatQueryReturn(result, variants);
};

export const TransformGetMediaDataFromMediaId: TransformerFunction = (
  data: { nodes: ShopifyGenerated.MediaImage[] },
  context,
  model,
) => {
  const result = _.map(data.nodes, (mediaImage) => {
    return {
      mediaImageId: mediaImage.id,
      alt: mediaImage?.alt,
      imageUrl: mediaImage.image?.url,
      height: mediaImage.image?.height,
      width: mediaImage.image?.width,
      imageSourceId: mediaImage.image?.id,
      imageAltText: mediaImage.image?.altText,
    }
  })
  return formatQueryReturn(result, data);
};

export const TransformGetProductsByHandlesQuery: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context,
  model,
) => {
  const products = _.get(Object.entries(data), '0.1', {});
  
  const edges = flattenConnection(products);
  const result = edges.length > 0 ? edges.map((product: any) => TransformProductDetail(product, context, model)) : [];

  return formatQueryReturn(result, data);
}