import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {ICustomer, ICustomerAccessToken, IShippingAddress} from '../types';
import {
  flattenConnection,
  formatQueryReturn,
  jsonArrayMapperWithCallback,
  JSONMapperSchema,
  jsonObjectMapper,
  ShopifyQueryReturnValue,
} from '../utils/utils';

export const TransformCustomerAddress = (data?: any): IShippingAddress | undefined => {
  if (!data) return;
  const mailingAddressSchema = [
    'id',
    'firstName',
    'lastName',
    'address1',
    'address2',
    'city',
    'company',
    'country',
    'latitude',
    'longitude',
    'phone',
    {
      field: 'state',
      path: 'province',
    },
    'zip',
    'formatted',
  ];

  return jsonObjectMapper(mailingAddressSchema, data);
};

export const TransformGetCustomerDefaultAddress = (data: any) => {
  const addressData = (data && data?.customer) ?? {};
  const {defaultAddress} = addressData ?? {};
  const tData = TransformCustomerAddress(defaultAddress);
  return formatQueryReturn(tData, defaultAddress);
};

export const TransformUpdateCustomerDefaultAddress = (data: any) => {
  const addressData = (data && data?.customer) ?? {};
  const {defaultAddress} = addressData ?? {};
  const tData = TransformCustomerAddress(defaultAddress);
  return formatQueryReturn(tData, defaultAddress);
};

export const TransformGetCustomerAddresses = (data: any) => {
  const addressData = (data && data?.customer) ?? {};

  const pageInfo = addressData?.addresses?.pageInfo ?? {};
  const {hasNextPage, endCursor: after} = pageInfo;

  const addresses = flattenConnection(addressData?.addresses);
  const tAddresses = jsonArrayMapperWithCallback(addresses, TransformCustomerAddress);
  const paginationMeta = {after};

  return formatQueryReturn(tAddresses, addresses, paginationMeta, hasNextPage);
};

export const TransformGetCustomer = (customerData: ShopifyGenerated.Customer) => {
  const customerDataSchema: JSONMapperSchema = [
    'id',
    'firstName',
    'lastName',
    'acceptsMarketing',
    'email',
    'tags',
    'phone',
    {
      field: 'defaultAddress',
      path: 'defaultAddress',
      transform: TransformCustomerAddress,
    },
    {
      field: 'addresses',
      path: 'addresses',
      transform: (addresses: any) => {
        return jsonArrayMapperWithCallback(flattenConnection(addresses), TransformCustomerAddress);
      },
    },
  ];

  const tData = jsonObjectMapper(customerDataSchema, customerData);
  return tData;
};

export const TransformGetCustomerQuery = (
  data: Pick<ShopifyGenerated.QueryRoot, 'node'>,
): ShopifyQueryReturnValue<ICustomer> => {
  const customerData = _.get(Object.entries(data), '0.1', {});
  const tData: ICustomer = TransformGetCustomer(customerData);

  return formatQueryReturn<ICustomer>(tData, customerData);
};

export const TransformGetCustomerMutation = (
  data: Pick<ShopifyGenerated.Mutation, 'customerCreate'>,
): ShopifyQueryReturnValue<ICustomer> => {
  const customerResult = _.get(Object.entries(data), '0.1', {});
  const {customer, customerUserErrors} = customerResult ?? {};
  const tData: ICustomer = customer ? TransformGetCustomer(customer) : {};

  return formatQueryReturn<ICustomer>(tData, customer, {}, false, customerUserErrors);
};

export const TransformCustomerAccessTokenMutations = (
  data: Pick<ShopifyGenerated.Mutation, 'customerReset'>,
): ShopifyQueryReturnValue<ICustomerAccessToken> => {
  const customerResult = _.get(Object.entries(data), '0.1', {});
  const {customerAccessToken, customerUserErrors} = customerResult ?? {};
  const tData = customerAccessToken
    ? jsonObjectMapper(['accessToken', 'expiresAt'], customerAccessToken)
    : ({} as ICustomerAccessToken);
  return formatQueryReturn<ICustomerAccessToken>(tData, customerAccessToken, {}, false, customerUserErrors);
};

export const TransformResetPasswordMutation = (data: Pick<ShopifyGenerated.Mutation, 'customerReset'>) => {
  const customerResult = _.get(Object.entries(data), '0.1', {});
  const {customerUserErrors} = customerResult ?? {};
  return formatQueryReturn(null, null, {}, false, customerUserErrors);
};

export const TransformDeleteCustomerAddress = (data: Pick<ShopifyGenerated.Mutation, 'customerAddressDelete'>) => {
  const deleteAddressRaw = data?.customerAddressDelete;
  const {deletedCustomerAddressId, customerUserErrors} = deleteAddressRaw ?? {};
  return formatQueryReturn({deletedCustomerAddressId}, deleteAddressRaw, {}, false, customerUserErrors);
};

export const TransformCustomerAddressMutation = (
  data: Pick<ShopifyGenerated.Mutation, 'customerAddressCreate' | 'customerAddressUpdate'>,
) => {
  const addressResult = _.get(Object.entries(data), '0.1', {});
  const {customerAddress, customerUserErrors} = addressResult ?? {};

  const tData = (customerAddress ? TransformCustomerAddress(customerAddress) : {}) as IShippingAddress;
  return formatQueryReturn<IShippingAddress>(tData, customerAddress, {}, false, customerUserErrors);
};

export const TransformCustomerActivateByUrlMutation = (
  data: Pick<ShopifyGenerated.Mutation, 'customerActivateByUrl'>,
) => {
  const customerResult = _.get(Object.entries(data), '0.1', {});
  const {customer, customerAccessToken, customerUserErrors} = customerResult ?? {};

  const tData = {
    customer,
    customerAccessToken,
  };
  return formatQueryReturn(tData, customerResult, {}, false, customerUserErrors);
};
