import _ from 'lodash';
import {
  IProductMetaFields,
  IProductMetaFieldsReferenceField,
  IProductMetaFieldsReferencesV2,
  IProductMetaFieldsV2,
} from '../types';
import * as ShopifyGenerated from '../generated/graphql';
import {flattenConnection, formatQueryReturn} from '../utils/utils';
import {toHTML} from './metaobjectTransformer';

function TransformMetafield(metafield: IProductMetaFields) {
  if (!metafield || _.isEmpty(metafield)) return;
  const {id, namespace, key, value, type, reference, references} = metafield;
  let flattenedReferences;
  if (references) flattenedReferences = flattenConnection(references);
  return {
    id,
    namespace,
    key,
    type,
    references: flattenedReferences,
    value: TransformMetafieldValueByType(type, value),
    reference,
  };
}

export function TransformMetafields(metafields: Array<IProductMetaFields>) {
  if (metafields && _.isArray(metafields) && !_.isEmpty(metafields)) {
    return metafields.map(val => TransformMetafield(val));
  }
  return [];
}

export function Transformfield(field: IProductMetaFieldsReferenceField) {
  if (!field || _.isEmpty(field)) return;
  const {key, value, type, reference} = field;
  let transformFieldReference = reference;
  if (transformFieldReference) {
    transformFieldReference = {url: reference.image.url};
  }
  return {key, type, value: type === 'rich_text_field' ? toHTML(value) : value, reference: transformFieldReference};
}

function ReferenceFields(fields: Array<{}>) {
  if (fields && _.isArray(fields) && !_.isEmpty(fields)) {
    return fields.map(field => Transformfield(field));
  }
  return [];
}

function TransformMetafield_V2(metafield: IProductMetaFieldsV2) {
  if (!metafield || _.isEmpty(metafield)) return;
  const {id, key, value, type, reference, references} = metafield;
  let transformedReferences: Array<{}> = [];
  let transformedReference: {} = {};
  if (references) {
    let flattenedReferences = flattenConnection(references) as Array<IProductMetaFieldsReferencesV2>;
    for (let i = 0; i < flattenedReferences.length; i++) {
      let fieldsArr = flattenedReferences[i].fields;
      transformedReferences = [...transformedReferences, {id, type, fields: ReferenceFields(fieldsArr)}];
    }
  }
  if (reference) {
    let fieldsArr = reference.fields;
    transformedReference = {...reference, fields: ReferenceFields(fieldsArr)};
  }
  return {
    id,
    key,
    type,
    value: TransformMetafieldValueByType(type, value),
    reference: transformedReference,
    references: transformedReferences,
  };
}

export function TransformMetafields_V2(metafields: Array<IProductMetaFieldsV2>) {
  if (metafields && _.isArray(metafields) && !_.isEmpty(metafields)) {
    return metafields.map((val, index) => {
      if (val) {
        return TransformMetafield_V2(val);
      } else
        return {
          message: `No data found for metaobject ${index + 1}`,
        };
    });
  }
  return [];
}

function TransformMetafieldValueByType(type: string, value: any) {
  switch (type) {
    case 'dimension':
    case 'json':
    case 'money':
    case 'rating':
    case 'rich_text_field':
    case 'volume':
    case 'weight':
      try {
        const parsedVal = JSON.parse(value);
        return parsedVal;
      } catch (e) {
        return value;
      }
    default:
      return value;
  }
}

export const TransformProductMetafields_V2 = (data: ShopifyGenerated.Product) => {
  const product = _.get(Object.entries(data), '0.1', {});
  const {metafields = []} = product;

  const result = TransformMetafields_V2(metafields);

  return formatQueryReturn(result, data);
};
