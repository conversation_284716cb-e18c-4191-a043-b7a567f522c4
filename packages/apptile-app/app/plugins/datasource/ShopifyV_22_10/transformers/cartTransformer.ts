import {Maybe} from 'graphql/jsutils/Maybe';
import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {
  ICart,
  ICartBuyerIdentity,
  ICartDiscountCode,
  ICartLineItem,
  ICartProductVariant,
  ISubscriptionCartLineItem,
} from '../types';
import {
  convertStringToNumber,
  flattenConnection,
  formatDisplayPrice,
  formatQueryReturn,
  JSONMapperSchema,
  jsonObjectMapper,
} from '../utils/utils';
import {TransformProductVariant} from './productTransformer';

// ======================== Helper transformers ==============================//
// Cart //
export const TransformCartBuyerIdentity = (
  data: ShopifyGenerated.CartBuyerIdentity,
): ICartBuyerIdentity | undefined => {
  const buyerSchema = [
    'countryCode',
    {
      field: 'customerId',
      path: 'customer.id',
    },
    'email',
    'phone',
    'deliveryAddressPreferences',
  ];

  return jsonObjectMapper(buyerSchema, data) as ICartBuyerIdentity;
};

export const TransformCartDiscountCode = (data: ShopifyGenerated.CartDiscountCode): ICartDiscountCode | undefined => {
  const discountSchema = ['applicable', 'code'];

  return jsonObjectMapper(discountSchema, data) as ICartDiscountCode;
};

// TODO: isSubscriptionProduct should be boolean
export const TransformSubscriptionCartLineItem = (
  data: Maybe<ShopifyGenerated.SellingPlanAllocation>,
  context: any,
): ISubscriptionCartLineItem | undefined => {
  if (!data) return;

  const {priceAdjustments, remainingBalanceChargeAmount} = data;

  const subscriptionsSchema = [
    {
      field: 'id',
      path: 'sellingPlan.id',
    },
    {
      field: 'isSubscriptionProduct',
      path: 'sellingPlan.recurringDeliveries',
    },
    {
      field: 'name',
      path: 'sellingPlan.name',
    },
  ];

  const adjustedPrice = priceAdjustments[0]?.perDeliveryPrice?.amount;
  const remainingBalanceAmount = remainingBalanceChargeAmount?.amount;

  return {
    ...jsonObjectMapper(subscriptionsSchema, data),
    adjustedPrice,
    remainingBalance: Number(remainingBalanceAmount),
    displayAdjustedPrice: formatDisplayPrice(context)(adjustedPrice),
    displayRemainingBalance: formatDisplayPrice(context)(remainingBalanceAmount),
  };
};

export const TransformCartProductVariant = (
  data: Maybe<ShopifyGenerated.ProductVariant> | undefined,
  context: any,
): ICartProductVariant | undefined => {
  if (!data) return;

  const {product} = data;
  const {title, handle, totalInventory, id, productType, vendor, tags} = product;
  const transformedProductVariant = TransformProductVariant(data, context);

  return {
    product: {
      title,
      handle,
      id,
      productType,
      tags,
      totalInventory: totalInventory ?? 0,
      vendor,
    },
    ...transformedProductVariant,
  };
};

export const TransformCartLineItem = (
  data: Partial<ShopifyGenerated.CartLine>,
  context: any,
): ICartLineItem | undefined => {
  if (!data) return;

  const {merchandise, sellingPlanAllocation} = data;

  const cartLineSchema = [
    {
      field: 'cartLineId',
      path: 'id',
    },
    'id',
    'quantity',
    {
      field: 'attributes',
      path: 'attributes',
      transform: attributes => {
        return attributes?.map(a => ({
          key: a?.key,
          value: a?.value,
        }));
      },
    },
    {
      field: 'lineItemDiscount',
      path: 'discountAllocations',
      transform: (currentValue: [{discountedAmount: {amount: number}}]) => {
        return _.sumBy(currentValue, function (discountItem) {
          return parseFloat(_.get(discountItem, 'discountedAmount.amount', '0'));
        });
      },
    },
    {
      field: 'displayLineItemDiscount',
      path: 'discountAllocations',
      transform: (currentValue: [{discountedAmount: {amount: number}}]) => {
        return _.sumBy(currentValue, function (discountItem) {
          return parseFloat(_.get(discountItem, 'discountedAmount.amount', '0'));
        });
      },
      formatterFunction: formatDisplayPrice(context),
    },
  ];

  const cartLineData = {
    ...jsonObjectMapper(cartLineSchema, data),
    variant: TransformCartProductVariant(merchandise, context),
    subscriptionProduct: TransformSubscriptionCartLineItem(sellingPlanAllocation, context),
  };

  return cartLineData;
};

export const TransformCheckoutLineItem = (
  data: Partial<ShopifyGenerated.CartLine>,
  context: any,
): ICartLineItem | undefined => {
  if (!data) return;

  const checkoutLineSchema = [
    'id',
    'quantity',
    {
      field: 'variantId',
      path: 'merchandise.id',
    },
  ];

  return jsonObjectMapper(checkoutLineSchema, data);
};

export const TransformAutomaticCartDiscount = (
  discountAllocations: ShopifyGenerated.CartAutomaticDiscountAllocation[] | undefined,
):
  | {
      totalDiscount: number;
      lineItemDiscount: {title: string; amount: number; currencyCode: string}[];
      discountTitle?: string;
    }
  | undefined => {
  if (!discountAllocations || discountAllocations.length === 0) return;

  // Transform the individual discount allocations
  const lineItemDiscount = discountAllocations
    .map(allocation => ({
      title: allocation.title,
      amount: parseFloat(allocation.discountedAmount.amount),
      currencyCode: allocation.discountedAmount.currencyCode,
    }))
    .reverse();

  // Calculate the total discount amount
  const totalDiscount = parseFloat(_.sumBy(lineItemDiscount, 'amount').toFixed(2));

  // Get the first discount title
  const discountTitle = lineItemDiscount[0]?.title;

  return {
    totalDiscount,
    discountTitle,
    lineItemDiscount,
  };
};

// =========================== Master Transformers ==================================//
// Cart //
export const TransformCart = (data: ShopifyGenerated.Cart | undefined, context: any): ICart | undefined => {
  if (!data) return;
  const {discountCodes, lines, discountAllocations} = data;

  //TODO:CurrencyCode, channelId
  const cartSchema: JSONMapperSchema = [
    'id',
    'checkoutUrl',
    'note',
    'createdAt',
    'updatedAt',
    'ready',
    {
      field: 'cartId',
      path: 'id',
    },
    {
      field: 'subtotalAmount',
      path: 'cost.subtotalAmount.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalAmount',
      path: 'cost.totalAmount.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalDutyAmount',
      path: 'cost.totalDutyAmount.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalTaxAmount',
      path: 'cost.totalTaxAmount.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'checkoutChargeAmount',
      path: 'cost.checkoutChargeAmount.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displaySubtotalAmount',
      path: 'cost.subtotalAmount.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalAmount',
      path: 'cost.totalAmount.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalDutyAmount',
      path: 'cost.totalDutyAmount.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalTaxAmount',
      path: 'cost.totalTaxAmount.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayCheckoutChargeAmount',
      path: 'cost.checkoutChargeAmount.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
  ];

  const cartData: ICart = {
    ...jsonObjectMapper(cartSchema, data),
    buyerIdentity: TransformCartBuyerIdentity(data.buyerIdentity),
    discountCodes: discountCodes.map(code => TransformCartDiscountCode(code)),
    lines: flattenConnection(lines)?.map(line => TransformCartLineItem(line, context)),
    checkoutLineItemData: flattenConnection(lines)?.map(line => TransformCheckoutLineItem(line, context)),
    automaticCartDiscount: TransformAutomaticCartDiscount(discountAllocations),
  };

  return cartData;
};

export const TransformCartPayload = (cartPayload: ShopifyGenerated.CartCreatePayload, context: any) => {
  const cartData = cartPayload?.cart ?? undefined;
  const result = TransformCart(cartData, context);
  const errors = cartPayload.userErrors ?? [];
  return formatQueryReturn(result, cartData, {}, false, errors);
};

export const TransformCartMutations = (
  data: Pick<ShopifyGenerated.Mutation, 'cartCreate' | 'cartLinesAdd'>,
  context: any,
) => {
  const cartPayload = _.get(Object.entries(data), '0.1', {});
  return TransformCartPayload(cartPayload, context);
};
