import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {
  ICheckout,
  ICheckoutAppliedGiftCard,
  ICheckoutAvailableShippingOptions,
  ICheckoutLineItem,
  IShippingAddress,
} from '../types';
import {
  convertStringToNumber,
  flattenConnection,
  formatDisplayPrice,
  formatQueryReturn,
  jsonArrayMapperWithCallback,
  JSONMapperSchema,
  jsonObjectMapper,
} from '../utils/utils';
import {TransformCartProductVariant} from './cartTransformer';

const PaymentStateMap: any = {
  AUTHORIZED: 'AUTHORIZED',
  PAID: 'PAID',
  PARTIALLY_PAID: 'PARTIALLY_PAID',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  PENDING: 'PENDING',
  REFUNDED: 'REFUNDED',
  VOIDED: 'VOIDED',
};

const transformDiscountAllocation = (currentValue: [{allocatedAmount: {amount: string}}]) => {
  return _.sumBy(currentValue.map(discount => parseFloat(discount.allocatedAmount.amount)));
};

export const TransformCheckoutAvailableShippingOptions = (
  data: ShopifyGenerated.ShippingRate | undefined,
  context: any,
): ICheckoutAvailableShippingOptions | undefined => {
  if (!data) return;

  const schema = [
    'handle',
    'title',
    {
      field: 'price',
      path: 'price.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayPrice',
      path: 'price.amount',
      transform: formatDisplayPrice(context),
    },
  ];

  return jsonObjectMapper(schema, data);
};

export const TransformShippingAddress = (data?: ShopifyGenerated.MailingAddress): IShippingAddress | undefined => {
  if (!data) return;
  const mailingAddressSchema = [
    'id',
    'firstName',
    'lastName',
    'address1',
    'address2',
    'city',
    'company',
    'country',
    {
      field: 'countryCode',
      path: 'countryCodeV2',
    },
    'latitude',
    'longitude',
    'phone',
    {
      field: 'state',
      path: 'province',
    },
    'zip',
    'formatted',
  ];

  return jsonObjectMapper(mailingAddressSchema, data);
};

export const TransformCheckoutLineItems = (
  data: ShopifyGenerated.CheckoutLineItem,
  context: any = {},
): ICheckoutLineItem | undefined => {
  if (!data) return;

  const {variant} = data;

  const checkoutLineSchema: JSONMapperSchema = [
    'id',
    'quantity',
    'title',
    {
      field: 'checkoutLineId',
      path: 'id',
    },
    {
      field: 'lineItemDiscount',
      path: 'discountAllocations',
      transform: transformDiscountAllocation,
    },
    {
      field: 'displayLineItemDiscount',
      path: 'discountAllocations',
      transform: transformDiscountAllocation,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'unitPrice',
      path: 'unitPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayUnitPrice',
      path: 'unitPrice.amount',
      transform: formatDisplayPrice(context),
    },
  ];

  const tData = {
    ...jsonObjectMapper(checkoutLineSchema, data),
    variant: TransformCartProductVariant(variant, context),
  };
  return tData;
};

export const TransformCheckoutAppliedGiftCard = (
  data: ShopifyGenerated.AppliedGiftCard,
  context: any,
): ICheckoutAppliedGiftCard | undefined => {
  if (!data) return;
  const giftCardSchema = [
    'id',
    {
      field: 'amountUsed',
      path: 'amountUsed.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayAmountUsed',
      path: 'amountUsed.amount',
      transform: formatDisplayPrice(context),
    },
    {
      field: 'balance',
      path: 'balance.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displaybalance',
      path: 'balance.amount',
      transform: formatDisplayPrice(context),
    },
    'lastCharacters',
  ];

  return jsonObjectMapper(giftCardSchema, data);
};

export const TransformCheckoutDiscount = (
  data: Partial<ShopifyGenerated.DiscountApplication>,
  context: any,
): ICheckoutAppliedGiftCard | undefined => {
  if (!data) return;
  const discountSchema = [
    {
      field: 'discountAmount',
      path: 'value.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displayDiscountAmount',
      path: 'value.amount',
      transform: formatDisplayPrice(context),
    },
    {
      field: 'code',
      path: 'code',
    },
    {
      field: 'discountPercentage',
      path: 'value.percentage',
      transform: convertStringToNumber,
    },
    {
      field: 'displayDiscountPercentage',
      path: 'value.percentage',
    },
  ];
  return jsonObjectMapper(discountSchema, data);
};

// Checkout //
export const TransformCheckout = (data: ShopifyGenerated.Checkout | undefined, context: any): ICheckout | undefined => {
  if (!data) return;
  //TODO: Billing address not found

  const {lineItems, appliedGiftCards, discountApplications, availableShippingRates, shippingLine} = data;
  const shippingRates = availableShippingRates?.shippingRates ?? [];

  const checkoutSchema = [
    'id',
    'webUrl',
    'currencyCode',
    {
      field: 'checkoutState',
      path: 'ready',
    },
    'completedAt',
    {
      field: 'orderId',
      path: 'order.id',
    },
    {
      field: 'orderFinancialStatus',
      path: 'order.financialStatus',
      transform: (orderFinancialStatus: string) => {
        return orderFinancialStatus ? _.get(PaymentStateMap, orderFinancialStatus) : undefined;
      },
    },
    'requiresShipping',
    'taxesIncluded',
    {
      field: 'shippingAddress',
      path: 'shippingAddress',
      transform: TransformShippingAddress,
    },
    {
      field: 'subtotalAmount',
      path: 'subtotalPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalDutyAmount',
      path: 'totalDuties.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalAmount',
      path: 'totalPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'lineItemsSubtotalAmount',
      path: 'lineItemsSubtotalPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalTaxAmount',
      path: 'totalTax.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'paymentDue',
      path: 'paymentDue.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displaySubtotalAmount',
      path: 'subtotalPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalDutyAmount',
      path: 'totalDuties.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalAmount',
      path: 'totalPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayLineItemsSubtotalAmount',
      path: 'lineItemsSubtotalPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalTaxAmount',
      path: 'totalTax.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayPaymentDue',
      path: 'paymentDue.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
  ];

  const checkoutObject = {
    ...jsonObjectMapper(checkoutSchema, data),
    lines: jsonArrayMapperWithCallback(flattenConnection(lineItems), TransformCheckoutLineItems, context),
    appliedGiftCards: jsonArrayMapperWithCallback(appliedGiftCards, TransformCheckoutAppliedGiftCard, context),
    discounts: jsonArrayMapperWithCallback(flattenConnection(discountApplications), TransformCheckoutDiscount, context),
    availableShippingOptions: jsonArrayMapperWithCallback(
      shippingRates,
      TransformCheckoutAvailableShippingOptions,
      context,
    ),
    selectedShippingOption: TransformCheckoutAvailableShippingOptions(
      shippingLine as ShopifyGenerated.ShippingRate,
      context,
    ),
  };

  return checkoutObject;
};

export const TransformCheckoutPayload = (checkoutPayload: ShopifyGenerated.CheckoutCreatePayload, context: any) => {
  const checkoutData = checkoutPayload?.checkout ?? undefined;
  const result = TransformCheckout(checkoutData, context);
  const errors = checkoutPayload.checkoutUserErrors ?? [];
  return formatQueryReturn(result, checkoutData, {}, false, errors);
};

export const TransformCheckoutQuery = (checkoutData: Pick<ShopifyGenerated.QueryRoot, 'node'>, context: any) => {
  const result = TransformCheckout(checkoutData.node as ShopifyGenerated.Checkout, context);
  return formatQueryReturn(result, checkoutData.node, {}, false, '');
};

export const TransformCheckoutMutations = (
  data: Pick<ShopifyGenerated.Mutation, 'checkoutCreate' | 'checkoutLineItemsAdd'>,
  context: any,
) => {
  const checkoutQueryPayload = _.get(Object.entries(data), '0.1', {});
  return TransformCheckoutPayload(checkoutQueryPayload, context);
};
