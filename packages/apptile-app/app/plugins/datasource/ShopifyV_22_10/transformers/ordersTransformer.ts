import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {IOrder, IOrderLineItem, Fulfillment} from '../types';
import {flattenConnection, formatDisplayPrice, formatQueryReturn, jsonObjectMapper} from '../utils/utils';
import {TransformCartProductVariant} from './cartTransformer';
import {TransformShippingAddress} from './checkoutTransformer';

// ========================= Helper transformer ==================== //

const TransformOrderLineItem = (
  data: Partial<ShopifyGenerated.OrderLineItem> | undefined,
  context: any,
): IOrderLineItem | undefined => {
  if (!data) return;

  try {
    const {discountAllocations, variant} = data;
    const lineItemDiscountSum =
      discountAllocations && _.sum(discountAllocations.map(discount => parseFloat(discount.allocatedAmount?.amount)));

    const schema = [
      'currentQuantity',
      {
        field: 'discountedTotalPrice',
        path: 'discountedTotalPrice.amount',
      },
      {
        field: 'originalTotalPrice',
        path: 'originalTotalPrice.amount',
      },
      'quantity',
      'title',
      {
        field: 'displayDiscountedTotalPrice',
        path: 'discountedTotalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayOriginalTotalPrice',
        path: 'originalTotalPrice.amount',
        transform: formatDisplayPrice(context),
      },
    ];

    return {
      ...jsonObjectMapper(schema, data),
      lineItemDiscount: lineItemDiscountSum,
      displayLineItemDiscount: formatDisplayPrice(context)(lineItemDiscountSum),
      variant: variant && TransformCartProductVariant(variant, context),
    };
  } catch (err) {
    console.error(err);
  }
};

function TransformFulfillmentInfo(fulfillments: Fulfillment[]) {
  return fulfillments?.map?.(({trackingInfo}) => {
    return trackingInfo?.map(({number, url}) => {
      return {number, url};
    });
  });
}

// ========================== Master Transformer ================== //

export const TransformOrder = (data: ShopifyGenerated.Order, context: any): IOrder | undefined => {
  if (!data) return;

  try {
    //TODO:, //customerId, //completedAt, //bllingAddress, //discounts, //OrderTracking

    const OrderStateMap: any = {
      IN_PROGRESS: 'CONFIRMED',
      OPEN: 'OPEN',
      FULFILLED: 'COMPLETED',
      UNFULFILLED: 'INCOMPLETE',
      CANCELLED: 'CANCELLED',
    };

    const PaymentStateMap: any = {
      AUTHORIZED: 'AUTHORIZED',
      PAID: 'PAID',
      PARTIALLY_PAID: 'PARTIALLY_PAID',
      PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
      PENDING: 'PENDING',
      REFUNDED: 'REFUNDED',
      VOIDED: 'VOIDED',
    };

    const {
      shippingAddress,
      fulfillmentStatus,
      financialStatus,
      lineItems,
      successfulFulfillments = [],
      shippingDiscountAllocations,
    } = data;

    const shippingDiscount =
      shippingDiscountAllocations &&
      _.sum(shippingDiscountAllocations.map(discount => parseFloat(discount.allocatedAmount?.amount)));

    const schema = [
      'id',
      'name',
      'orderNumber',
      'phone',
      {
        field: 'createdAt',
        path: 'processedAt',
      },
      'statusUrl',
      'currencyCode',
      {
        field: 'customerEmail',
        path: 'email',
      },
      {
        field: 'subtotalPrice',
        path: 'subtotalPrice.amount',
      },
      {
        field: 'totalPrice',
        path: 'totalPrice.amount',
      },
      {
        field: 'totalRefunded',
        path: 'totalRefunded.amount',
      },
      {
        field: 'totalShippingPrice',
        path: 'totalShippingPrice.amount',
      },
      {
        field: 'totalTax',
        path: 'totalTax.amount',
      },
      {
        field: 'currentSubtotalPrice',
        path: 'currentSubtotalPrice.amount',
      },
      {
        field: 'currentTotalDuties',
        path: 'currentTotalDuties.amount',
      },
      {
        field: 'displaySubtotalPrice',
        path: 'subtotalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalPrice',
        path: 'totalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalRefunded',
        path: 'totalRefunded.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalShippingPrice',
        path: 'totalShippingPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayTotalTax',
        path: 'totalTax.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayCurrentSubtotalPrice',
        path: 'currentSubtotalPrice.amount',
        transform: formatDisplayPrice(context),
      },
      {
        field: 'displayCurrentTotalDuties',
        path: 'currentTotalDuties.amount',
        transform: formatDisplayPrice(context),
      },
    ];

    return {
      ...jsonObjectMapper(schema, data),
      shippingDiscount,
      shippingAddress: TransformShippingAddress(shippingAddress),
      orderState: OrderStateMap[fulfillmentStatus],
      fulfillmentInfo: TransformFulfillmentInfo(successfulFulfillments),
      paymentState: financialStatus && PaymentStateMap[financialStatus],
      lineItems: flattenConnection(lineItems)?.map(line => TransformOrderLineItem(line, context)),
    };
  } catch (err) {
    console.error(err);
  }
};

// ======================== Transformers ======================= //
export const TransformCustomerOrders = (data: ShopifyGenerated.OrderEdge, context: any) => {
  const customer = _.get(Object.entries(data), '0.1', {});
  const orderEdge = _.get(Object.entries(customer), '0.1', {});

  const {hasNextPage, endCursor: after} = _.get(orderEdge, 'pageInfo', {});

  const orders = flattenConnection(orderEdge)?.map(o => TransformOrder(o, context));

  return formatQueryReturn(orders, orderEdge, {after}, hasNextPage);
};
export const TransformCustomerOrder = (data: ShopifyGenerated.OrderEdge, context: any) => {
  const order = _.get(Object.entries(data), '0.1', {});

  const result = TransformOrder(order, context);

  return formatQueryReturn(result, data);
};
