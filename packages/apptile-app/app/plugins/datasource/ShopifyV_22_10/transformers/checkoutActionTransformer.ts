import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {IApptileCartMergedCheckout} from '../types';
import {
  convertStringToNumber,
  flattenConnection,
  formatDisplayPrice,
  formatQueryReturn,
  jsonArrayMapperWithCallback,
  jsonObjectMapper,
} from '../utils/utils';
import {TransformCheckoutLineItems} from './checkoutTransformer';

export const TransformActionCheckout = (
  data: ShopifyGenerated.Checkout | undefined,
  context: any,
): IApptileCartMergedCheckout | undefined => {
  if (!data) return;

  const checkoutSchema = [
    'id',
    'webUrl',
    'completedAt',
    'ready',
    'currencyCode',
    {
      field: 'checkoutId',
      path: 'id',
    },
    {
      field: 'subtotalAmount',
      path: 'subtotalPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalDutyAmount',
      path: 'totalDuties.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalAmount',
      path: 'totalPrice.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'totalTaxAmount',
      path: 'totalTax.amount',
      transform: convertStringToNumber,
    },
    {
      field: 'displaySubtotalAmount',
      path: 'subtotalPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalDutyAmount',
      path: 'totalDuties.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalAmount',
      path: 'totalPrice.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'displayTotalTaxAmount',
      path: 'totalTax.amount',
      transform: convertStringToNumber,
      formatterFunction: formatDisplayPrice(context),
    },
    {
      field: 'lines',
      path: 'lineItems',
      transform: (lineItems: any) => {
        return jsonArrayMapperWithCallback(flattenConnection(lineItems), TransformCheckoutLineItems, context);
      },
    },
  ];

  const checkoutObject = jsonObjectMapper(checkoutSchema, data);
  return checkoutObject;
};

export const TransformCheckoutPayload = (checkoutPayload: ShopifyGenerated.CheckoutCreatePayload, context: any) => {
  const checkoutData = checkoutPayload?.checkout ?? undefined;
  const result = TransformActionCheckout(checkoutData, context);
  const errors = checkoutPayload.checkoutUserErrors ?? [];
  return formatQueryReturn(result, checkoutData, errors);
};

export const TransformActionCheckoutQuery = (checkoutData: Pick<ShopifyGenerated.QueryRoot, 'node'>, context: any) => {
  const result = TransformActionCheckout(checkoutData.node as ShopifyGenerated.Checkout, context);
  return formatQueryReturn(result, checkoutData.node);
};

export const TransformActionCheckoutMutations = (
  data: Pick<ShopifyGenerated.Mutation, 'checkoutCreate' | 'checkoutLineItemsAdd'>,
  context: any,
) => {
  const checkoutQueryPayload = _.get(Object.entries(data), '0.1', {});
  return TransformCheckoutPayload(checkoutQueryPayload, context);
};
