import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {ICollection, IShopifyMenuWithCollection} from '../types';
import {formatQueryReturn, jsonObjectMapper} from '../utils/utils';

export const _transformMenuItem = (
  data: ShopifyGenerated.Maybe<ShopifyGenerated.MenuItem> | undefined,
): ICollection | undefined => {
  if (!data) return;

  const menuItemSchema = [
    'id',
    {
      field: 'collectionId',
      path: 'resourceId',
    },
    'title',
    'type',
  ];

  const {items: menuItems, ...restMenuItem} = data;

  const transformedMenuItem = {
    ...jsonObjectMapper(menuItemSchema, restMenuItem),
    items: !_.isEmpty(menuItems)
      ? menuItems.map(menuItemV => {
          return _transformMenuItem(menuItemV);
        })
      : [],
  };

  return transformedMenuItem;
};

export const TransformGetMenu = (data: ShopifyGenerated.Menu) => {
  if (!data) return;
  const menuData = _.get(Object.entries(data), '0.1', {}) as any;

  const {items: menuDataItems, ...rest} = menuData;

  const tMenuItems = !_.isEmpty(menuDataItems)
    ? menuDataItems.map(menuItemV => {
        return _transformMenuItem(menuItemV);
      })
    : [];

  const tMenuData: IShopifyMenuWithCollection = {
    ...jsonObjectMapper(['id', 'handle', 'itemsCount', 'title'], rest),
    items: tMenuItems,
  };
  return formatQueryReturn(tMenuData, data, {});
};
