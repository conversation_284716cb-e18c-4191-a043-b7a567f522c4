import {IShopifyMetaObject, IShopifyMetaObjectValues, IShopifyMetaObjects} from '../types';
import { flattenConnection } from '../utils/utils';

export function MetaObjectTransformer(rawData: IShopifyMetaObject) {
  if (!rawData.metaobject) return {data: null};
  const fields = rawData.metaobject?.fields.reduce((arr, x) => {
    arr.push({...x, value: x.type === 'rich_text_field' ? toHTML(x.value) : x.value});
    return arr;
  }, [] as any[]);
  return {data: {...rawData.metaobject, fields: fields}};
}

export function MetaObjectsTransformer(rawData: IShopifyMetaObjects) {
  if(!rawData.metaobjects) return { data: null };
  console.log(rawData)
  const edges: any = flattenConnection(rawData.metaobjects);
  if(!edges.length) return { data: [] };

  const transformed = edges.map((edge: IShopifyMetaObjectValues) => {
    const fields = edge.fields.reduce((arr, field) => {
      arr.push({
        ...field,
      });
      return arr;
    }, [] as any[]);

    return fields;
  });

  console.log(transformed)
  return { data: transformed };
}

//TODO : add type for shopify rich text format
export function toHTML(content: string) {
  let parsed = JSON.parse(content);
  let html = '<div>';
  parsed.children.forEach((node: any) => {
    switch (node.type) {
      case 'heading':
        html += `<h${node.level}>${node.children[0].value}</h${node.level}>`;
        break;
      case 'list':
        html += `<${node.listType === 'unordered' ? 'ul' : 'ol'}>`;
        node.children.forEach((item: any) => {
          html += `<li>${item.children[0].value}</li>`;
        });
        html += `<${node.listType === 'unordered' ? '/ul' : '/ol'}>`;
        break;
      case 'paragraph':
        html += `<p>`;
        node.children.forEach((item: any) => {
          if (item.value) item.value = item?.value.replace(/\n/g, '</br>');
          if (item.type === 'text' && item.bold) {
            html += `<strong>${item.value}</strong>` + ' ';
          } else if (item.type === 'text' && item.italic) {
            html += `<em>${item.value}</em>` + ' ';
          } else if (item.type === 'text') {
            html += `${item.value}` + ' ';
          }
          if (item.type === 'link' && item.bold) {
            html +=
              `<a href="${item.url}" target="${item.target}"><strong>${item.children[0].value}</strong></a>` + ' ';
          } else if (item.type === 'link' && item.italic) {
            html += `<a href="${item.url}" target="${item.target}"><em>${item.children[0].value}</em></a>` + ' ';
          } else if (item.type === 'link') {
            html += `<a href="${item.url}" target="${item.target}">${item.children[0].value}</a>` + ' ';
          }
        });
        html += `</p>`;
        break;
    }
  });
  html += '</div>';
  return html;
}
