import _ from 'lodash';
import {jsonObjectMapper, JSONMapperSchema, formatQueryReturn, flattenConnection} from '../utils/utils';
import {TransformerFunction} from '..';
import * as ShopifyGenerated from '../generated/graphql';
import {TransformProductDetail} from '../transformers/productTransformer';

export const TransformPredictiveSearch = (searchPayload: any, _context: any) => {
  if (!searchPayload) return;

  const searchData = searchPayload?.predictiveSearch ?? undefined;

  const productFeaturedImageSchemaMapper = ['id', 'height', 'width', 'altText', 'url'];
  const articleAutherObjectSchemaMapper = ['bio', 'email', 'firstName', 'lastName', 'name']

  const predictiveSearchSchema: JSONMapperSchema = [
    {
      field: 'queries',
      path: 'queries',
      transform: value => _.map(value, entry => entry?.text),
    },
    {
      field: 'products',
      path: 'products',
      transform: value => {
        return _.map(value, entry => {
          return {
            id: entry?.id,
            handle: entry?.handle,
            title: entry?.title,
            featuredImage: jsonObjectMapper(productFeaturedImageSchemaMapper, entry?.featuredImage),
            minPrice: entry?.priceRange?.minVariantPrice?.amount,
            maxPrice: entry?.priceRange?.maxVariantPrice?.amount,
            vendor: entry?.vendor,
            availableForSale: entry?.availableForSale,
            tags: entry?.tags
          };
        });
      },
    },
    {
      field: 'articles',
      path: 'articles',
      transform: value => {
        return _.map(value, entry => {
          return {
            id: entry?.id,
            handle: entry?.handle,
            excerpt: entry?.excerpt,
            onlineStoreUrl: entry?.onlineStoreUrl,
            title: entry?.title,
            trackingParameters: entry?.trackingParameters,
            author: jsonObjectMapper(articleAutherObjectSchemaMapper, entry?.author),
            authorV2: jsonObjectMapper(articleAutherObjectSchemaMapper, entry?.authorV2),
          };
        });
      },
    },
    {
      field: 'collections',
      path: 'collections',
      transform: value => {
        return _.map(value, entry => {
          return {
            id: entry?.id,
            handle: entry?.handle,
            title: entry?.title,
            trackingParameters: entry?.trackingParameters,
            image: jsonObjectMapper(productFeaturedImageSchemaMapper, entry?.image),
          };
        });
      },
    },
    {
      field: 'pages',
      path: 'pages',
      transform: value => {
        return _.map(value, entry => {
          return {
            id: entry?.id,
            handle: entry?.handle,
            title: entry?.title,
            trackingParameters: entry?.trackingParameters,
            onlineStoreUrl: entry?.onlineStoreUrl,
          };
        });
      },
    },
  ];

  const result = {
    ...jsonObjectMapper(predictiveSearchSchema, searchData),
  };

  return formatQueryReturn(result, searchPayload, {}, false, []);
};

export const TransformSearchPaginatedQuery: TransformerFunction = (
  data: ShopifyGenerated.QueryRoot,
  context,
  model,
) => {
  const searchData = _.get(data, 'search', {});
  const {hasNextPage, endCursor: after} = _.get(searchData, 'pageInfo', {});
  const nodes = _.get(searchData, 'nodes', []);
  const edges = flattenConnection(nodes);

  const result = edges.map((product: any) => TransformProductDetail(product, context, model));
  return formatQueryReturn(result, data, {after}, hasNextPage);
};
