import _ from 'lodash';
import * as ShopifyGenerated from '../generated/graphql';
import {IArticle, IArticleComment} from '../types';
import {flattenConnection, formatQueryReturn, jsonObjectMapper} from '../utils/utils';

export const TransformArticleCategory = (data: ShopifyGenerated.Blog): IArticle | undefined => {
  //TODO: image is not available in shopify
  //TODO: Not able to find a way to get the list of blogs
  //TODO: Description, descritionHTML is not availale in shopify.

  const {articles} = data;

  if (!data) return;

  const articleCategorySchema = ['id', 'title', 'handle', 'description', 'descriptionHtml'];
  return {
    ...jsonObjectMapper(articleCategorySchema, data),
    post_count: flattenConnection(articles).length,
  };
};

export const TransformArticleComment = (
  data: Partial<ShopifyGenerated.Comment> | undefined,
): IArticleComment | undefined => {
  if (!data) return;
  const commentSchema = [
    'id',
    {
      field: 'authorName',
      path: 'author.name',
    },
    {
      field: 'authorEmail',
      path: 'author.email',
    },
    'content',
    'contentHtml',
  ];

  return {
    ...jsonObjectMapper(commentSchema, data),
  };
};

export const TransformArticle = (data: ShopifyGenerated.Article | undefined): IArticle | undefined => {
  //TODO: Author image is not available in shopify
  if (!data) return;

  const imageSchema = [
    'id',
    'altText',
    {
      field: 'src',
      path: 'originalSrc',
    },
  ];

  const {authorV2, blog, image, comments} = data;
  const {firstName, lastName} = authorV2;
  const articleSchema = [
    {
      field: 'AuthorBio',
      path: 'authorV2.bio',
    },
    'content',
    'contentHtml',
    'excerpt',
    'excerptHtml',
    'handle',
    'id',
    'modifiedAt',
    'tags',
    'title',
    'publishedAt',
  ];
  return {
    ...jsonObjectMapper(articleSchema, data),
    comments: flattenConnection(comments)?.map(comment => TransformArticleComment(comment)),
    category: TransformArticleCategory(blog),
    image: jsonObjectMapper(imageSchema, image),
    AuthorName: `${firstName} ${lastName}`,
  };
};

export const TransformGetBlog = (data: ShopifyGenerated.BlogConnection) => {
  if (!data) return;

  const blog = _.get(Object.entries(data), '0.1', {});

  const result = TransformArticleCategory(blog);

  return formatQueryReturn(result, blog);
};

export const TransformGetBlogs = (data: ShopifyGenerated.BlogConnection) => {
  if (!data) return;

  const blogs = _.get(Object.entries(data), '0.1', {});

  const {hasNextPage, endCursor: after} = _.get(blogs, 'pageInfo', {});
  const edges = flattenConnection(blogs);

  const result = edges.map((blog: any) => TransformArticleCategory(blog));

  return formatQueryReturn(result, data, {after}, hasNextPage);
};

export const TransformGetArticle = (data: ShopifyGenerated.BlogConnection) => {
  if (!data) return;

  const blog = _.get(Object.entries(data), '0.1', {});

  const article = _.get(blog, 'articleByHandle', {});

  const result = TransformArticle(article);

  return formatQueryReturn(result, blog);
};

export const TransformGetArticles = (data: ShopifyGenerated.BlogConnection) => {
  if (!data) return;

  const articles = _.get(Object.entries(data), '0.1', {});

  const {hasNextPage, endCursor: after} = _.get(articles, 'pageInfo', {});
  const edges = flattenConnection(articles);

  const result = edges.map((article: any) => TransformArticle(article));

  return formatQueryReturn(result, data, {after}, hasNextPage);
};
