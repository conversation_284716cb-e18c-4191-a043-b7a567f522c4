import {sha256} from 'js-sha256';
import {<PERSON><PERSON><PERSON>} from 'buffer';

export function generateCodeVerifier(): string {
  const rando = generateRandomCode();
  return base64UrlEncode(rando);
}

export function generateCodeChallenge(codeVerifier: string) {
  // const digestOp = await crypto.subtle.digest(
  //   { name: "SHA-256" },
  //   new TextEncoder().encode(codeVerifier)
  // );
  // const hash = convertBufferToString(digestOp);
  // return base64UrlEncode(hash);
  const shaArr = sha256.array(codeVerifier);
  console.log('shaArr', shaArr);
  const hashBuf = Buffer.from(shaArr);
  console.log('hashBuf', hashBuf);
  const hash64 = hashBuf.toString('base64');
  console.log('hash64', hash64);
  return hash64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

function generateRandomCode() {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return String.fromCharCode.apply(null, Array.from(array));
}

function base64UrlEncode(str: string) {
  const base64 = new Buffer(str).toString('base64');
  // This is to ensure that the encoding does not have +, /, or = characters in it.
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

// function convertBufferToString(hash) {
//   const uintArray = new Uint8Array(hash);
//   const numberArray = Array.from(uintArray);
//   return String.fromCharCode(...numberArray);
// }
