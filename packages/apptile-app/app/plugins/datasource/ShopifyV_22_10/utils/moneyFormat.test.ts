import {getMoneyFormatPrice} from './utils';

describe('JS AST Walker tests', () => {
  test('test amount_no_decimals', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount_no_decimals')).toBe('12128127128728');
  });

  test('test amount_with_comma_separator', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount_with_comma_separator')).toBe('12,128,127,128,728.35');
  });

  test('test amount_no_decimals_with_comma_separator', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount_no_decimals_with_comma_separator')).toBe(
      '12,128,127,128,728',
    );
  });

  test('test amount_with_apostrophe_separator', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount_with_apostrophe_separator')).toBe("121'281'271'287.28");
  });

  test('test amount', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount')).toBe('12128127128728.35');
  });

  test('test amount_test', function () {
    expect(getMoneyFormatPrice(12128127128728.34374, 'amount_test')).toBe('12128127128728.35');
  });
});
