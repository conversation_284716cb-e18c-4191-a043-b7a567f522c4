import {getTextFromHtml} from '@/root/app/common/utils/htmlutils';
import _ from 'lodash';
import {DatasourceQueryReturnValue} from '../../../query/index';
import {IQueryDataType, IShopifyDatasourceQueryReturnValue} from '../../datasourceTypes';
export interface GraphQLConnection<T> {
  edges?: {node: T}[];
  nodes?: T[];
}

export interface JSONMapperFieldSchema {
  field: string; // renamed field key
  path: string; // original path of object item where we can pick the value
  transform?: (val: any) => any;
  formatterFunction?: (currentValue: string) => string;
}

export interface ShopifyQueryReturnValue<T> extends DatasourceQueryReturnValue {
  data: T | undefined;
}

export type JSONMapperSchema = Array<JSONMapperFieldSchema | string>;

export function flattenConnection<T>(connection: Partial<GraphQLConnection<T>>): Partial<T>[] {
  if (!connection) return [];

  if (connection.nodes) {
    return connection.nodes as Partial<T>[];
  }

  if (connection.edges) {
    return connection.edges.map(edge => {
      if (!edge?.node) return {};
      return edge.node;
    });
  }
  return connection;
}
function identity(x: any) {
  return x;
}

export function jsonObjectMapper(schema: JSONMapperSchema, json: any) {
  const result = new Array(schema.length);
  for (let index = 0; index < schema.length; ++index) {
    const schemaItem = schema[index];
    let curr: JSONMapperFieldSchema;
    if (typeof schemaItem === 'string') {
      curr = {
        field: schemaItem,
        path: schemaItem,
        transform: identity,
      };
    } else {
      let {transform, ...restSchema} = schemaItem;
      if (!schemaItem.transform) {
        schemaItem.transform = identity;
      }
      curr = schemaItem;
    }
  
    const value = _.get(json, curr.path, null);
    let tValue = curr.transform(value);
    let fValue = curr?.formatterFunction ? curr.formatterFunction(tValue) : tValue;
    result[curr.field] = fValue;
    result[index] = [curr.field, fValue];
  }
  return Object.fromEntries(result);
}



export function jsonObjectMapper_(schema: JSONMapperSchema, json: any) {
  const resultEntries = new Array(schema.length);

  for (let index = 0; index < schema.length; ++index) {
    const schemaItem = schema[index];
    if (typeof schemaItem === 'string') {
      resultEntries[index] = [schemaItem, {
         field: schemaItem,
         path: schemaItem,
         transform: identity,
      }];
    } else {
      if (!schemaItem.transform) {
        schemaItem.transform = identity;
      }
      resultEntries[index] = [schemaItem.field, schemaItem];
    }

    const curr = resultEntries[index][1]; 
    const value = _.get(json, curr.path, null);
    let tValue = curr?.transform ? curr.transform(value) : value;
    let fValue = curr?.formatterFunction ? curr.formatterFunction(tValue) : tValue;
    curr[curr.field] = fValue;
  }

  return Object.fromEntries(resultEntries);
}

export function jsonArrayMapper<T>(schema: JSONMapperSchema, entityArr: Array<T>): any[] {
  return entityArr && entityArr.length > 0 ? entityArr.map(entity => jsonObjectMapper(schema, entity as any)) : [];
}

export function jsonArrayMapperWithCallback<T, R>(
  entityArr: Array<T>,
  tCallback: (data: any, ...args: any[]) => R,
  ...additionalArgs: any[]
): R[] {
  return entityArr && entityArr.length > 0 ? entityArr.map(entity => tCallback(entity, ...additionalArgs)) : [];
}

function getTransformedDataWithRaw(tData: any, rawData: any) {
  if (!tData) return null;
  const tDataAddedRaw = _.isArray(tData)
    ? tData.map((tDataItem, key) => {
        return {...tDataItem, _raw: rawData ? flattenRawResponse(rawData)[key] : null};
      })
    : {...tData, _raw: flattenRawResponse(rawData)};
  return tDataAddedRaw;
}

export function formatQueryReturn<T>(
  tData: T,
  rawData: any,
  paginationMeta = {},
  hasNextPage = false,
  errors: any = null,
): IShopifyDatasourceQueryReturnValue<IQueryDataType<T>> {
  return {
    data: tData ? getTransformedDataWithRaw(tData, rawData) : undefined,
    rawData: rawData,
    paginationMeta: paginationMeta,
    hasNextPage: hasNextPage,
    errors: errors,
    hasError: errors && errors.length > 0 ? true : false,
  };
}

const formatWithDelimiters = (
  amount: number,
  precision: number = 2,
  thousandSeperator: string = ',',
  decimal: string = '.',
) => {
  if (isNaN(amount) || amount == null) {
    return 0;
  }

  let number = (amount / 100.0).toFixed(precision);
  let parts = number.split('.'),
    dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + thousandSeperator),
    cents = parts[1] ? decimal + parts[1] : '';

  return dollars + cents;
};

const currencyFormats: Record<string, string> = {
  '{{amount}}': 'amount',
  '{{amount_no_decimals}}': 'amount_no_decimals',
  '{{amount_with_comma_separator}}': 'amount_with_comma_separator',
  '{{amount_no_decimals_with_comma_separator}}': 'amount_no_decimals_with_comma_separator',
  '{{amount_with_apostrophe_separator}}': 'amount_with_apostrophe_separator',
};

export const getMoneyFormatPrice = (
  amount: number,
  moneyFormat: string = 'amount',
  localeString: string,
): string | number => {
  let formmattedAmount;
  switch (moneyFormat) {
    case 'amount_no_decimals':
      formmattedAmount = _.round(amount, 2).toFixed(0);
      break;
    case 'amount_with_comma_separator':
      formmattedAmount = _.round(amount, 2).toLocaleString(localeString, {maximumFractionDigits: 2});
      break;
    case 'amount_no_decimals_with_comma_separator':
      formmattedAmount = _.round(amount, 2).toLocaleString(localeString, {maximumFractionDigits: 0});
      break;
    case 'amount_with_apostrophe_separator':
      formmattedAmount = formatWithDelimiters(amount, 2, "'", '.');
      break;
    case 'amount':
    default:
      formmattedAmount = _.round(amount, 2).toFixed(2);
      break;
  }
  return formmattedAmount;
};

export const getMoneyFormatterTemplate = (moneyFormat: string = 'amount'): string | undefined => {
  return _.get(
    _.find(_.entries(currencyFormats), ([_, currencyFormatStr]) => currencyFormatStr === moneyFormat),
    '0',
    'amount',
  );
};

const templateCache = {};
const tmplCache = {};

export const formatDisplayPrice = (context: any) => {
  const template = _.get(context, 'moneyFormat', '{{amount}}'); //'${{dollars}}';
  const countryCode = _.get(context, 'countryCode', 'US');
  const languageCode = _.get(context, 'languageCode', 'en');
  const localeString = `${_.isEmpty(languageCode) ? 'en' : languageCode}-${
    _.isEmpty(countryCode) ? 'US' : countryCode
  }`;

  let result = templateCache[template];
  if (!result) {
    const format = template.match(/{{([\s\S]+?)}}/g)[0];

    const moneyFormat = _.get(currencyFormats, format, 'amount');
    const moneyTemplate = _.replace(template, `${format}`, `{{${moneyFormat}}}`);
    const fcn = (price: any) => {
      if (!price) return null;

      let tmpl = tmplCache[moneyTemplate];
      if (!tmpl) {
        tmpl = _.template(moneyTemplate, {interpolate: /{{([\s\S]+?)}}/g});
        tmplCache[moneyTemplate] = tmpl;
      }
      return getTextFromHtml(
        tmpl({
          [moneyFormat]: getMoneyFormatPrice(price, moneyFormat, localeString),
        }),
      );
    };
    templateCache[template] = fcn;
    result = fcn;
  }
  return result;
};

export function convertStringToNumber(price: string) {
  if (price && !isNaN(Number(price))) return Number(price);
  return price;
}

function flattenRawResponse(data: any) {

  let startRef: Record<string, any> = { ans: data };
  let startKey = 'ans';

  const refStack = [startRef];
  const keyStack = [startKey];

  while (refStack.length > 0) {
    const ref = refStack.pop();
    const key = keyStack.pop();

    let currentItem = ref[key];
    if (!currentItem) {
      ref[key] = [];
    } else if (currentItem.nodes) {
      ref[key] = currentItem.nodes;
    } else if (currentItem.edges) {
      ref[key] = currentItem.edges.slice();
      currentItem = ref[key];
      for (let index = 0; index < currentItem.length; ++index) {
        const edge = currentItem[index];
        if (!edge?.node) {
          currentItem[index] = {};
        } else {
          currentItem[index] = edge.node;
        }
      }
    } else {
      // Looks like this copy was useless. It works without it. Maybe dangerous,
      // but also much faster

      if (Array.isArray(currentItem)) {
        ref[key] = currentItem.slice();
      } else if (currentItem && typeof currentItem === "object") {
        ref[key] = Object.assign({}, currentItem);
      }
    }

    currentItem = ref[key];

    if (Array.isArray(currentItem)) {
      for (let index = 0; index < currentItem.length; ++index) {
        if (typeof currentItem[index] === "object") {
          refStack.push(currentItem);
          keyStack.push(index);
        }
      }
    } else if (currentItem && typeof currentItem === 'object') {
      const keys = Object.keys(currentItem);
      for (let index = 0; index < keys.length; ++index) {
        const k = keys[index];
        if (typeof currentItem[k] === "object") {
          refStack.push(currentItem);
          keyStack.push(k);
        }
      }
    } 
  }

  return startRef.ans;
}

