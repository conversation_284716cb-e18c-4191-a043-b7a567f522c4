import {ShopifyQueryDetails} from '..';
import * as customerAPIOrderGqls from '../queries/customerApiOrderQueries';
import * as OrderTransformer from '../customerAPITransformers/orderTransformers';

export const CustomerAPIOrderQueryRecords: Record<string, ShopifyQueryDetails> = {
  GetOrders: {
    queryType: 'query',
    gqlTag: customerAPIOrderGqls.GET_ORDERS,
    isCustomerAPI: true,
    transformer: OrderTransformer.TransformCustomerApiOrders,
    contextInputParams: {
      countryCode: 'countryCode',
    },
    editableInputParams: {
      sortKey: 'PROCESSED_AT',
      reverse: false,
      first: 10,
      after: '',
      customerAccessToken: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },

  GetOrderDetails: {
    queryType: 'query',
    gqlTag: customerAPIOrderGqls.GET_ORDER_DETAILS,
    isCustomerAPI: true,
    transformer: OrderTransformer.TransformCustomerApiOrder,
    contextInputParams: {
      countryCode: 'countryCode',
    },
    editableInputParams: {
      sortKey: 'PROCESSED_AT',
      reverse: false,
      first: 10,
      after: '',
      customerAccessToken: '',
      orderId: '',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },
    headerResolver: params => {
      if (!params?.customerAccessToken) return {};
      return {headers: {headers: {Authorization: params?.customerAccessToken}}};
    },
  },
};
