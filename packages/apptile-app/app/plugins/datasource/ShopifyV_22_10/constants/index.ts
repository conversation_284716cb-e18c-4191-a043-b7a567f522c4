export const SHOPIFY_CONFIG_CART_ITEMS_KEY_FOR_LOCAL_STORAGE = 'currentCartLineItemsLSKey';
export const SHOPIFY_CONFIG_CART_KEY_FOR_LOCAL_STORAGE = 'currentCartLSKey';

export const SHOPIFY_CONFIG_CART_ID_KEY_FOR_LOCAL_STORAGE = 'currentCartIdLSKey';
export const SHOPIFY_CONFIG_CART_ID_KEY_DEFAULT = 'currentCartId';

export const SHOPIFY_MODEL_CART_ID_KEY = 'currentCartId';
export const SHOPIFY_MODEL_CART_ITEMS_KEY = 'currentCartLineItems';
export const SHOPIFY_MODEL_CART_OBJECT_KEY = 'currentCart';
export const SHOPIFY_MODEL_CART_ERROR_KEY = 'currentCartError';
export const SHOPIFY_MODEL_CART_SYNC_KEY = 'syncingCartStatus';

export const SHOPIFY_SUBSCRIPTION_CART_STATUS = 'subscriptionCartStatus';

export const SHOPIFY_CONFIG_CHECKOUT_KEY_FOR_LOCAL_STORAGE = 'currentCheckoutLSKey';
export const SHOPIFY_MODEL_CHECKOUT_OBJECT_KEY = 'currentCheckout';

export const SHOPIFY_MODEL_READY_FOR_CHECKOUT_KEY = 'isCheckoutReady';
export const SHOPIFY_MODEL_CART_WITH_SUBSCRIPTION_ITEM_KEY = 'isSubscriptionCart';

export const SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY = 'applePayOrderId';

export const SHOPIFY_MODEL_APPLE_PAY_AVAILABILTY_ITEM_KEY = 'isApplePayAvailable';
export const SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY = 'recentlyViewedList';
export const SHOPIFY_CONFIG_RECENTYLY_VIEWED_LIST_FOR_LOCAL_STORAGE = 'recentlyViewedListStore';

export const SHOPIFY_MODEL_RECENTYLY_VIEWED_ITEMS_KEY = 'recentlyViewedItems';

export const STOREFRONT_ACCESS_TOKEN = 'storefrontAccessToken';

export const CUSTOMER_ACCOUNT_CODE_VERIFIER_KEY_FOR_LOCAL_STORAGE = 'codeVerifierLSKey';
export const CUSTOMER_ACCOUNT_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE = 'accessTokenDataLSKey';
export const CUSTOMER_ACCOUNT_CUSTOMER_ACCESS_TOKEN_DATA_KEY_FOR_LOCAL_STORAGE = 'customerAccessTokenDataLSKey';
