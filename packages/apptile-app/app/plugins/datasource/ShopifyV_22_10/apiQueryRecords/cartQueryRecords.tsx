import _ from 'lodash';
import {ShopifyQueryDetails} from '..';
import * as CartGqls from '../queries/shoppingCart';
import * as CartTransformer from '../transformers/cartTransformer';

const transformedCartLineItems = (lines: Array<{variationId: string; quantity: number; sellingPlanId?: string}>) => {
  return _.map(lines, v => {
    const {variationId, quantity, ...rest} = v;
    return {quantity, merchandiseId: variationId, ...rest};
  });
};

export const CartQueryRecords: Record<string, ShopifyQueryDetails> = {
  CreateShoppingCart: {
    queryType: 'mutation',
    gqlTag: CartGqls.SHOPPING_CART_CREATE,
    editableInputParams: {
      variationId: '',
      sellingPlanId: '',
      customerAccessToken: '',
      quantity: 0,
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    transformer: CartTransformer.TransformCartMutations,
    inputResolver: (inputVariables, dsModelValues) => {
      const {variationId, customerAccessToken, quantity, sellingPlanId, customAttributes} = inputVariables;
      const customAttributesInput = !_.isEmpty(customAttributes) ? {attributes: customAttributes} : {};

      let accessToken = customerAccessToken;

      const isCustomerAPIEnabled = dsModelValues?.get('useCustomerApi');
      if (isCustomerAPIEnabled) {
        const sfCustomerAccessToken = dsModelValues?.get('storefrontCustomerAccessTokenFromCustomerApi');
        accessToken = sfCustomerAccessToken?.customerAccessToken;
      }

      let cartLines =
        sellingPlanId && variationId
          ? [
              {
                quantity,
                variationId,
                sellingPlanId,
              },
            ]
          : variationId
          ? [
              {
                quantity,
                variationId,
              },
            ]
          : [];

      return {
        input: {
          ...customAttributesInput,
          lines: transformedCartLineItems(cartLines),
          buyerIdentity: !accessToken
            ? {}
            : {
                customerAccessToken: accessToken,
              },
        },
      };
    },
  },
  CreateShoppingCartV1: {
    queryType: 'mutation',
    gqlTag: CartGqls.SHOPPING_CART_CREATE,
    transformer: CartTransformer.TransformCartMutations,
    editableInputParams: {
      lines: [],
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: (inputVariables, dsModelValues) => {
      const {lines, customerAccessToken, customAttributes, countryCode, email} = inputVariables;
      const customAttributesInput = !_.isEmpty(customAttributes) ? {attributes: customAttributes} : {};

      let accessToken = customerAccessToken;

      const isCustomerAPIEnabled = dsModelValues?.get('useCustomerApi');
      if (isCustomerAPIEnabled) {
        const sfCustomerAccessToken = dsModelValues?.get('storefrontCustomerAccessTokenFromCustomerApi');
        accessToken = sfCustomerAccessToken?.customerAccessToken;
      }

      return {
        input: {
          ...customAttributesInput,
          lines: transformedCartLineItems(lines),
          buyerIdentity: _.isEmpty(accessToken)
            ? {countryCode}
            : {
                customerAccessToken: accessToken,
                countryCode,
                email,
              },
        },
      };
    },
    //lines is an array of objects with the following properties: attributes , merchandiseId,quantity, sellingPlanId
  },
  CartDiscountUpdate: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_DISCOUNT_CODE_UPDATE,
    editableInputParams: {
      cartId: '',
      discountCode: '',
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    transformer: CartTransformer.TransformCartMutations,
    inputResolver: inputVariables => {
      const {cartId, discountCode} = inputVariables;
      return {
        cartId: cartId,
        discountCodes: discountCode ? (Array.isArray(discountCode) ? discountCode : [discountCode]) : [],
      };
    },
  },
  GetCartDetails: {
    queryType: 'query',
    gqlTag: CartGqls.GET_CART_DETAILS,
    transformer: CartTransformer.TransformCartPayload,
    editableInputParams: {
      cartId: '',
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {cartId} = inputVariables;
      return !!cartId;
    },
  },
  GetCartLineItems: {
    queryType: 'query',
    gqlTag: CartGqls.GET_CART_LINE_ITEMS,
    transformer: CartTransformer.TransformCartPayload,
    editableInputParams: {
      cartId: '',
      reverse: false,
      first: 10,
      after: '',
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    isPaginated: true,
    paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
      const {after} = paginationMeta;
      return {...inputVariables, after};
    },

    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {cartId} = inputVariables;
      return !!cartId;
    },
  },
  CartLinesUpdate: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_LINES_UPDATE,
    transformer: CartTransformer.TransformCartMutations,
    editableInputParams: {
      cartId: '',
      lines: [],
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      const {cartId, lines} = inputVariables;
      return {
        cartId,
        lines: transformedCartLineItems(lines),
      };
    },
    //lines is an array of objects with the following properties: attributes ,id, merchandiseId,quantity, sellingPlanId
  },
  CartLinesAdd: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_LINES_ADD,
    transformer: CartTransformer.TransformCartMutations,
    editableInputParams: {
      cartId: '',
      lines: [],
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      const {cartId, lines} = inputVariables;
      return {
        cartId,
        lines: transformedCartLineItems(lines),
      };
    },
    //lines is an array of objects with the following properties: attributes , merchandiseId,quantity, sellingPlanId
  },
  CartLinesRemove: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_LINES_REMOVE,
    transformer: CartTransformer.TransformCartMutations,
    editableInputParams: {
      cartId: '',
      lineIds: [],
      //These are the merchandise line ids to remove.
    },
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    inputResolver: inputVariables => {
      return inputVariables;
    },
  },
  CartNoteUpdate: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_NOTE_UPDATE,
    contextInputParams: {
      countryCode: 'countryCode',
      languageCode: 'languageCode',
    },
    transformer: CartTransformer.TransformCartMutations,
    editableInputParams: {
      cartId: '',
      note: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {note} = inputVariables;
      // this is to ensure following shopify warning
      // As part of the GraphQL Storefront API 2024-04 API release, we've updated the `cartNoteUpdate` API to make the `note` argument required. Upgrade your Buy SDK version. View change
      return !_.isEmpty(note);
    },
  },
  CartBuyerIdentityUpdate: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_BUYER_IDENTITY_UPDATE,
    editableInputParams: {
      cartId: '',
      customerAccessToken: '',
      email: '',
    },
    inputResolver: (inputVariables: any, dsModelValues: any) => {
      const {cartId, customerAccessToken, email} = inputVariables;

      let accessToken = customerAccessToken;

      const isCustomerAPIEnabled = dsModelValues?.get('useCustomerApi');
      if (isCustomerAPIEnabled) {
        const sfCustomerAccessToken = dsModelValues?.get('storefrontCustomerAccessTokenFromCustomerApi');
        accessToken = sfCustomerAccessToken?.customerAccessToken;
      }

      return {cartId, buyerIdentity: {customerAccessToken: accessToken, email}};
    },
    transformer: CartTransformer.TransformCartMutations,
  },
  CartBuyerIdentityUpdateV2: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_BUYER_IDENTITY_UPDATE,
    editableInputParams: {
      cartId: '',
      customerAccessToken: '',
      deliveryAddressPreferences: '',
    },
    inputResolver: (inputVariables: any, dsModelValues: any) => {
      const {cartId, customerAccessToken, deliveryAddressPreferences, email} = inputVariables;

      let accessToken = customerAccessToken;

      const isCustomerAPIEnabled = dsModelValues?.get('useCustomerApi');
      if (isCustomerAPIEnabled) {
        const sfCustomerAccessToken = dsModelValues?.get('storefrontCustomerAccessTokenFromCustomerApi');
        accessToken = sfCustomerAccessToken?.customerAccessToken;
      }

      return {cartId, buyerIdentity: {deliveryAddressPreferences, customerAccessToken: accessToken, email}};
    },
    transformer: CartTransformer.TransformCartMutations,
  },
  CartAttributesUpdate: {
    queryType: 'mutation',
    gqlTag: CartGqls.CART_ATTRIBUTES_UPDATE,
    editableInputParams: {
      cartId: '',
      attributes: '',
    },
    inputResolver: (inputVariables: any) => {
      const {cartId, attributes} = inputVariables;
      return {
        cartId,
        attributes: _.isPlainObject(attributes) ? Object.entries(attributes).map(([key, value]) => ({key, value})) : [],
      };
    },
    transformer: CartTransformer.TransformCartMutations,
  },
};
