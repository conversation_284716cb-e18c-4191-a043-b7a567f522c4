import {ShopifyQueryDetails} from '..';
import {REQUEST_RESET_PASSWORD_EMAIL, RESET_PASSWORD_BY_URL} from '../mutations/customerRecovery';
import * as CustomerAddressGqls from '../queries/customerAddress';
import * as customerGqls from '../queries/customerAuthentication';
import * as CustomerTransformer from '../transformers/customerTransformer';

export const CustomerQueryRecords: Record<string, ShopifyQueryDetails> = {
  CustomerAddressCreate: {
    queryType: 'mutation',
    gqlTag: CustomerAddressGqls.CUSTOMER_ADDRESS_CREATE,
    transformer: CustomerTransformer.TransformCustomerAddressMutation,
    editableInputParams: {
      address1: '',
      address2: '',
      city: '',
      company: '',
      country: '',
      firstName: '',
      lastName: '',
      phone: '',
      state: '',
      zip: '',
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, state, ...restAddress} = inputVariables;
      return {address: {...restAddress, province: state}, customerAccessToken};
    },
  },

  CustomerAddressDelete: {
    queryType: 'mutation',
    gqlTag: CustomerAddressGqls.CUSTOMER_ADDRESS_DELETE,
    transformer: CustomerTransformer.TransformDeleteCustomerAddress,
    editableInputParams: {
      id: '',
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, id} = inputVariables;
      return {customerAccessToken, id};
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerAccessToken} = inputVariables;
      return !!customerAccessToken;
    },
  },

  CustomerAddressUpdate: {
    queryType: 'mutation',
    gqlTag: CustomerAddressGqls.CUSTOMER_ADDRESS_UPDATE,
    transformer: CustomerTransformer.TransformCustomerAddressMutation,
    editableInputParams: {
      address1: '',
      address2: '',
      city: '',
      company: '',
      country: '',
      firstName: '',
      lastName: '',
      phone: '',
      state: '',
      zip: '',
      customerAccessToken: '',
      id: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, state, id, ...restAddress} = inputVariables;
      return {address: {...restAddress, province: state}, customerAccessToken, id};
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerAccessToken} = inputVariables;
      return !!customerAccessToken;
    },
  },
  GetCustomerDefaultAddress: {
    queryType: 'query',
    gqlTag: CustomerAddressGqls.GET_CUSTOMER_DEFAULT_ADDRESS,
    transformer: CustomerTransformer.TransformGetCustomerDefaultAddress,
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: (inputVariables: {customerAccessToken: string}) => {
      const {customerAccessToken} = inputVariables;
      return {customerAccessToken};
    },
  },
  UpdateCustomerDefaultAddress: {
    queryType: 'query',
    gqlTag: CustomerAddressGqls.UPDATE_CUSTOMER_DEFAULT_ADDRESS,
    transformer: CustomerTransformer.TransformUpdateCustomerDefaultAddress,
    editableInputParams: {
      customerAccessToken: '',
      addressId: '',
    },
    inputResolver: (inputVariables: {customerAccessToken: string; addressId: string}) => {
      const {customerAccessToken, addressId} = inputVariables;
      return {customerAccessToken, addressId};
    },
  },
  GetCustomerAddresses: {
    queryType: 'query',
    gqlTag: CustomerAddressGqls.GET_CUSTOMER_ADDRESSES,
    transformer: CustomerTransformer.TransformGetCustomerAddresses,
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: (inputVariables: {customerAccessToken: string}) => {
      const {customerAccessToken} = inputVariables;
      return {customerAccessToken};
    },
  },

  LoginCustomer: {
    queryType: 'mutation',
    gqlTag: customerGqls.CUSTOMER_LOGIN,
    transformer: CustomerTransformer.TransformCustomerAccessTokenMutations,
    editableInputParams: {
      email: '',
      password: '',
    },
    inputResolver: inputVariables => {
      return {input: inputVariables};
    },
    inputVariableChecker: inputVariables => {
      const {email, password} = inputVariables;

      return !!email && !!password;
    },
  },

  FetchCustomer: {
    queryType: 'query',
    gqlTag: customerGqls.CUSTOMER_FETCH,
    transformer: CustomerTransformer.TransformGetCustomerQuery,
    editableInputParams: {
      customerAccessToken: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {customerAccessToken} = inputVariables;
      return !!customerAccessToken;
    },
  },

  RegisterCustomer: {
    queryType: 'mutation',
    gqlTag: customerGqls.CUSTOMER_CREATE,
    transformer: CustomerTransformer.TransformGetCustomerMutation,
    editableInputParams: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      acceptsMarketing: false,
      phone: '',
    },
    inputResolver: inputVariables => {
      return {input: inputVariables};
    },
  },
  UpdateCustomer: {
    queryType: 'mutation',
    gqlTag: customerGqls.CUSTOMER_UPDATE,
    transformer: CustomerTransformer.TransformCustomerAccessTokenMutations,
    editableInputParams: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      customerAccessToken: '',
    },
    nullChecker: inputVariables => {
      return inputVariables.customerAccessToken;
    },
    inputResolver: inputVariables => {
      const {customerAccessToken, ...rest} = inputVariables;
      return {customer: rest, customerAccessToken};
    },
  },
  RequestResetPasswordEmail: {
    queryType: 'mutation',
    gqlTag: REQUEST_RESET_PASSWORD_EMAIL,
    transformer: CustomerTransformer.TransformResetPasswordMutation,
    editableInputParams: {
      email: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {email} = inputVariables;
      return !!email;
    },
  },
  ResetPasswordByURL: {
    queryType: 'mutation',
    gqlTag: RESET_PASSWORD_BY_URL,
    transformer: CustomerTransformer.TransformResetPasswordMutation,
    editableInputParams: {
      password: '',
      resetUrl: '',
    },
    inputResolver: inputVariables => {
      const {password, resetUrl} = inputVariables;
      return {password, resetUrl};
    },
  },
  CustomerAccessTokenRenew: {
    queryType: 'mutation',
    gqlTag: customerGqls.CUSTOMER_ACCESS_TOKEN_RENEW,
    transformer: CustomerTransformer.TransformCustomerAccessTokenMutations,
    editableInputParams: {
      customerAccessToken: '',
    },
    inputResolver: inputVariables => {
      const {customerAccessToken} = inputVariables;
      return {customerAccessToken};
    },
  },
  CustomerActivateByUrl: {
    queryType: 'mutation',
    gqlTag: customerGqls.CUSTOMER_ACTIVATE_BY_URL,
    transformer: CustomerTransformer.TransformCustomerActivateByUrlMutation,
    editableInputParams: {
      activationUrl: '',
      password: '',
    },
    inputResolver: inputVariables => {
      const {activationUrl, password} = inputVariables;
      return {activationUrl, password};
    },
  },
};
