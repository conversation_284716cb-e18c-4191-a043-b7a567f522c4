import {ShopifyQueryDetails} from '..';
import {
  GET_COLLECTIONS_BY_IDS,
  GET_COLLECTIONS_BY_IDS_WITH_META_FIELD,
  GET_COLLECTION_METAFIELD_IMAGES,
} from '../queries/productCollection';
import {TransformGetCollectionMetaFieldImages, TransformGetCollections} from '../transformers/productTransformer';

export const collectionQueryRecords: Record<string, ShopifyQueryDetails> = {
  GetCollectionsByIds: {
    queryType: 'query',
    gqlTag: GET_COLLECTIONS_BY_IDS,
    transformer: TransformGetCollections,
    contextInputParams: {
      collectionMetafields: 'collectionMetafields',
    },
    editableInputParams: {
      collectionIds: '',
    },
    inputResolver: (inputVariables: Record<string, any>) => {
      const {collectionIds} = inputVariables;
      return {collectionIds};
    },
  },
  // FIXME: DEPRECATE
  GetCollectionMetafieldImages: {
    queryType: 'query',
    gqlTag: GET_COLLECTION_METAFIELD_IMAGES,
    transformer: TransformGetCollectionMetaFieldImages,
    editableInputParams: {
      collectionHandle: '',
      metafieldkey: '',
      metafieldNamespace: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {collectionHandle, metafieldkey, metafieldNamespace} = inputVariables;
      return !!collectionHandle && !!metafieldkey && !!metafieldNamespace;
    },
  },
  // FIXME: DEPRECATE
  GetCollectionsByIdsWithMetaField: {
    queryType: 'query',
    gqlTag: GET_COLLECTIONS_BY_IDS_WITH_META_FIELD,
    transformer: TransformGetCollections,
    editableInputParams: {
      collectionIds: '',
      identifiers: '',
    },
    inputResolver: (inputVariables: Record<string, any>) => {
      const {collectionIds, identifiers} = inputVariables;
      return {collectionIds, identifiers};
    },
  },
};
