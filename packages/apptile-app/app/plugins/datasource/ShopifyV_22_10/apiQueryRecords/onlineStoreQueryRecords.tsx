import {ShopifyQueryDetails} from '..';
import {GET_MENU} from '../queries/menu';
import {TransformGetMenu} from '../transformers/onlineStoreTransformer';

export const onlineStoreRecords: Record<string, ShopifyQueryDetails> = {
  GetMenu: {
    queryType: 'query',
    gqlTag: GET_MENU,
    transformer: TransformGetMenu,
    editableInputParams: {
      menuHandle: '',
    },
    inputResolver: (inputVariables: any) => {
      const {menuHandle} = inputVariables;
      return {handle: menuHandle};
    },
  },
};
