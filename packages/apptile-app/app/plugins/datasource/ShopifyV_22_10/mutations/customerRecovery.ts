import gql from 'graphql-tag';
import {CART_DETAIL} from '../queries/shoppingCart';

export const REQUEST_RESET_PASSWORD_EMAIL = gql`
  mutation customerRecover($email: String!) {
    customerRecover(email: $email) {
      customerUserErrors {
        code
        field
        message
      }
    }
  }
`;

export const RESET_PASSWORD_BY_URL = gql`
  mutation customerResetByUrl($password: String!, $resetUrl: URL!) {
    customerResetByUrl(password: $password, resetUrl: $resetUrl) {
      customerUserErrors {
        code
        field
        message
      }
    }
  }
`;

export const UPDATE_CART_BUYER_IDENTITY_FOR_SHIPPING_ADDRESS = `mutation cartBuyerIdentityUpdate($buyerIdentity: CartBuyerIdentityInput!, $cartId: ID!) {
    cartBuyerIdentityUpdate(buyerIdentity: $buyerIdentity, cartId: $cartId) {
      cart ${CART_DETAIL}
      userErrors {
        field
        message
      }
    }
  }
`;
