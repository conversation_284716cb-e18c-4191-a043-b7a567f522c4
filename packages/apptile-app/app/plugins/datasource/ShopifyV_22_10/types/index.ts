import {MoneyV2} from '../generated/graphql';

export interface ITransformedData {
  // It store the flatten object retreived from Datasource in raw format.
  //  We will only used this field for purpose build tiles and these will never go in tile library
  _raw: any;
}

export interface IDisplayImage {
  id?: string;
  altText?: string;
  src: string;
  thumbnail?: string;
}

export interface IWithImageList {
  images?: Array<IDisplayImage>;
}

export interface IProduct extends ITransformedData {
  id: string;
  handle: string;
  currencyCode: string;
  title: string;
  description: string;
  descriptionHtml: string;
  availableForSale: boolean;
  productType?: string;
  tags?: object;
  featuredImage: string; // first image url from images
  minPrice: number; //strikeoff price
  maxPrice: number; //strikeoff price
  maxSalePrice: number; // discounted sale price != strikeoff price
  minSalePrice: number; // discounted sale price != strikeoff price
  displayMinPrice: string; // $4
  displayMaxPrice: string; // $8
  displayMinSalePrice: string; // $3.5
  displayMaxSalePrice: string; // $7.5
  vendor?: string;
  totalInventory?: number;
}

export interface IProductDetail extends IProduct, IWithImageList {
  collections?: Array<{
    id: number;
    name: string;
    handle: string;
  }>;
  tags: string[];
  media?: Array<{
    alt: string;
    mediaContentType: string;
    previewImage: {
      id: string;
      altText: string;
      height: number;
      width: number;
      url: string;
    };
  }>;
  productOptions: Array<{
    id: string;
    name: string;
    values: string[];
  }>;
  variants: IProductVariant[];
  // TODO:  revisit shopify specific and also need to rename
  // Remove Subscription from standard Product use it from raw response ƒ
  // sellingPlanGroups: SellingPlanGroup[];
  // RAW_FIELD TODO:  revisit shopify specific and also need to rename
  // RAW_FIELD TODO: requiresSellingPlan?: boolean;
  reviewCount?: number;
  reviewRatings?: number;
}

export interface IProductVariant extends ITransformedData {
  id: string;
  availableForSale: boolean;
  sku: string;
  featuredImage: string; // first image url from images field
  image: IDisplayImage;
  title: string;
  weight: number;
  weightUnit: string;
  isInStock: boolean;
  quantityAvailableForSale: number;
  // Selected options renamed as variantOptions
  variantOptions: Array<{
    name: string;
    value: string;
  }>;
  // TODO:  revisit shopify specific and also need to rename
  //RAW_FIELD: sellingPlanAllocations: SellingPlanAllocation[];
  price: number; //strikeoff price
  salePrice: number; // discounted sale price != strikeoff price
  displayPrice: string; // $4
  displaySalePrice: string; // $3.5
  metafields?: any[];
}

export type ICollectionImage = {
  altText: string;
  id: string;
  src: string;
};

export interface ICollection extends ITransformedData {
  id: string;
  title: string;
  handle: string;
  featuredImage: ICollectionImage; // image field renamed with featuredImage
  description: string;
  descriptionHtml: string;
  updatedAt: string;
  onlineStoreUrl: string;
  metafields?: any[];
}

export interface ICollectionWithImages extends ICollection, IWithImageList {}

export type IArticle = {
  AuthorBio?: string;
  AuthorName: string;
  AuthorImage?: IDisplayImage;

  category?: IArticleCategory;
  //TODO: Why not further broken up ?
  comments?: IArticleComment;
  content: string;
  contentHtml: string;
  excerpt?: string;
  excerptHtml?: string;
  handle: string;
  id: string;
  //TODO: Discuss why this not further broken up ?
  image?: IDisplayImage;
  modifiedAt?: string;
  tags?: string[];
  title: string;
  publishedAt: string;
};

export interface IArticleCategory {
  id: string;
  title: string;
  handle: string;
  image?: IDisplayImage;
  articles: IArticle[];
  description: string;
  descriptionHtml: string;
  post_count: string;
}

export interface IArticleComment {
  id: string;
  authorName: string;
  authorEmail: string;
  content: string;
  contentHtml: string;
}

//cart, check-out and orders schema
export type ICartDiscountCode = {
  applicable: boolean;
  code: string;
};

export type ICartBuyerIdentity = {
  countryCode?: string;
  customerId?: string; // path: customer.id
  email?: string;
  phone?: string;
};

export type ICartProductVariant = Partial<IProductVariant> & {
  product: Pick<IProduct, 'title' | 'handle' | 'totalInventory' | 'id' | 'productType' | 'vendor' | 'tags'>;
};

// subtotalAmount: "", //subtotalAmount
// 	totalAmount: "" //totalAmount
// 	totalTaxAmount: "" //totalTaxAmount
// 	totalDutyAmount: "" //totalDutyAmount
// 	checkoutChargeAmount: "" //checkoutChargeAmount
export interface ICart {
  id: string;
  checkoutUrl: string;
  currencyCode: string;
  channelId: string;
  buyerIdentity: ICartBuyerIdentity; // Need to be discussed
  // deliveryGroups: CartDeliveryGroupConnection;
  discountCodes: Array<ICartDiscountCode>;
  lines: ICartLineItem;
  note?: string;
  createdAt: string;
  updatedAt: string;

  subtotalAmount: number; // cost.subtotalAmount.amount
  totalAmount: number;
  totalDutyAmount?: number;
  totalTaxAmount?: number;

  displaySubtotalAmount: string; // cost.subtotalAmount.amount
  displayTotalAmount: string;
  displayTotalDutyAmount?: string;
  displayTotalTaxAmount?: string;
}

export interface ISubscriptionCartLineItem {
  id: string;
  isSubscriptionProduct: boolean;
  adjustedPrice: string; // sellingPlanAllocation.priceAdjustments[0].perDeliveryPrice.amount
  displayAdjustedPrice: string; // CurrencyCode(sellingPlanAllocation.priceAdjustments[0].perDeliveryPrice.amount)
  name: string; // sellingPlanAllocation.sellingPlan.name
  remainingBalance: number;
  displayRemainingBalance: number;
}

export interface ICartLineItem {
  cartLineId: string;
  quantity: number;
  variant: ICartProductVariant;
  lineItemDiscount: number; // discountAllocations
  displayLineItemDiscount: string;
  subscriptionProduct?: ISubscriptionCartLineItem; //originally sellingPlanAllocation
}

export interface ICheckoutLineItem {
  checkoutLineId: string;
  quantity: number;
  variant: ICartProductVariant;
  lineItemDiscount: number; // Sum of all the discount in checkout discountAllocations
  displayLineItemDiscount: string; // Sum of all the discount in checkout discountAllocations
  // unitPrice?: string;   do not exists in cart
  // displayUnitPrice?: string; do not exists in cart
}

/** Represents a mailing address for customers and shipping. */
export type IShippingAddress = {
  id: string;
  /** The first name of the customer. */
  firstName?: string;
  /** The last name of the customer. */
  lastName?: string;
  address1?: string;
  /**
   * The second line of the address. Typically the number of the apartment, suite, or unit.
   *
   */
  address2?: string;
  /**
   * The name of the city, district, village, or town.
   *
   */
  city?: string;
  /**
   * The name of the customer's company or organization.
   *
   */
  company?: string;
  /**
   * The name of the country.
   *
   */
  country?: string;
  /**
   * The two-letter code for the country of the address.
   *
   * For example, US.
   */
  countryCode?: string;

  /** The latitude coordinate of the customer address. */
  latitude?: string;
  /** The longitude coordinate of the customer address. */
  longitude?: string;
  phone?: string;
  /** The region of the address, such as the province, state, or district. */
  state?: string;
  /** The zip or postal code of the address. */
  zip?: string;
};

/** Represents available shipping options and cost */
export type ICheckoutAvailableShippingOptions = {
  handle: string; // handle
  title?: string; // title
  price: number; //price
  displayPrice: string;
  // missing in shopify
  transitTime?: string;
  description?: string;
  type?: string;
};

export type ICheckoutAppliedGiftCard = {
  id: string;
  amountUsed: number;
  displayAmountUsed: string;
  balance: number;
  displaybalance: number;
  lastCharacters: string;
};

export type ICheckoutDiscountTargetType = 'LINE_ITEM' | 'SHIPPING_LINE';

export interface ICheckoutDiscount {
  discountAmount: number;
  displayDiscountAmount: string;
  targetType: ICheckoutDiscountTargetType;
}

// subtotalAmount: "", //subtotalPriceV2
// totalAmount: "" //totalPriceV2
// paymentDue: "" //paymentDueV2
// lineItemsSubtotalPrice: "" //lineItemsSubtotalPrice
// totalTaxAmount: "" //totalTaxV2
// totalDutyAmount: "" //totalDuties
/** A container for all the information required to checkout items and pay. */
export interface ICheckout {
  /**
   * What status is the Checkout object in, for example:
   * true - The checkout can be updated and ordered. It is the default state.
   * false - The checkout was ordered. No further operations on the cart are allowed.
   */
  id: string;
  /** The url pointing to the checkout accessible from the web. */
  webUrl: string;
  checkoutState: boolean; // ready
  /** The date and time when the checkout was completed. */
  completedAt?: string;
  orderId: string; // order.id
  orderFinancialStatus: IOrderPaymentState;
  /** The shipping address to where the line items will be shipped. */
  shippingAddress?: IShippingAddress;
  /** The billing address */
  billingAddress?: IShippingAddress;

  /** States whether or not the fulfillment requires shipping. */
  requiresShipping: boolean;
  /**
   * The available shipping rates for this Checkout.
   * Should only be used when checkout `requiresShipping` is `true` and
   * the shipping address is valid.
   *
   */
  lines: Array<ICheckoutLineItem>;
  availableShippingOptions?: Array<ICheckoutAvailableShippingOptions>; // availableShippingRates
  selectedShippingOption?: ICheckoutAvailableShippingOptions;
  /** The gift cards used on the checkout. */
  appliedGiftCards: Array<ICheckoutAppliedGiftCard>;
  /** Discounts that have been applied on the checkout. */
  discounts: Array<ICheckoutDiscount>; //discountApplication
  taxesIncluded: boolean;

  subtotalPrice: number;
  totalDuties: number;
  totalPrice: number;
  lineItemsSubtotalPrice: number;
  totalTax: number;
  paymentDue: number;

  displaySubtotalPrice: string;
  displayTotalDuties: string;
  displayTotalPrice: string;
  displayLineItemsSubtotalPrice: string;
  displayTotalTax: string;
  displayPaymentDue: string;
}

//IApptileMergedCartAndCheckoutLineItems only usefull for cart page cards.
// Checkout and Cart object may or may not differ on other screens
export type IApptileMergedCartAndCheckoutLineItems = Pick<
  ICartLineItem,
  'cartLineId' | 'quantity' | 'subscriptionProduct' | 'variant'
> &
  Pick<ICheckoutLineItem, 'checkoutLineId' | 'lineItemDiscount' | 'displayLineItemDiscount'>;

// checkoutUrl: string;
// currencyCode: string;
// subtotalAmount: number; // cost.subtotalAmount.amount
// totalAmount: number;
// totalDutyAmount?: number;
// totalTaxAmount?: number;
// displaySubtotalAmount: string; // cost.subtotalAmount.amount
// displayTotalAmount: string;
// displayTotalDutyAmount?: string;
// displayTotalTaxAmount?: string;

// Cart Merged with Checkout for shopify Script Discount
export type IApptileCartMergedCheckout = {
  cartId: string;
  checkoutId: string;
  checkoutUrl: string;
  lines: IApptileMergedCartAndCheckoutLineItems;
  subtotalAmount?: number;
  totalAmount?: number;
  totalDutyAmount?: number;
  totalTaxAmount?: number;
  displaySubtotalAmount?: string;
  displayTotalAmount?: string;
};

export type IOrderPaymentState =
  | 'AUTHORIZED'
  | 'PAID'
  | 'PARTIALLY_PAID'
  | 'PARTIALLY_REFUNDED'
  | 'PENDING'
  | 'REFUNDED'
  | 'VOIDED';

// OrderState.CONFIRMED  = IOrderFulfillmentStatus.IN_PROGRESS
// OrderState.OPEN  = IOrderFulfillmentStatus.OPEN
// OrderState.COMPLETED  = IOrderFulfillmentStatus.FULFILLED
// OrderState.CANCELLED  = Order.canceledAt
export type IOrderState = 'CONFIRMED' | 'OPEN' | 'COMPLETED' | 'CANCELLED';

export type IOrder = {
  id: string;
  name: string;
  customerEmail?: string;
  orderNumber: number;
  customerId?: string; // ?
  phone?: string;
  createdAt: string; // processedAt
  completedAt?: string; // commercetool API
  shippingAddress?: IShippingAddress;
  billingAddress?: IShippingAddress;
  statusUrl: string;
  orderState: IOrderState;
  paymentState?: IOrderPaymentState;
  fulfillmentInfo: Array<FulfillmentTrackingInfo>;
  // Use from rawValue as discountApplications calculation is complex and dependent on some use cases
  discountApplications?: Array<{
    applicable: boolean;
    code: string;
    discountAmount: string;
    displayDiscountAmount: string;
  }>;

  /** List of the order’s line items. */
  lineItems: Array<IOrderLineItem>;

  currencyCode: string;
  subtotalPrice: number;
  totalPrice?: number;
  totalRefunded: number;
  totalShippingPrice: number;
  totalTax?: number;
  currentSubtotalPrice: number;
  currentTotalDuties?: number;

  displaySubtotalPrice: string;
  displayTotalPrice?: string;
  displayTotalRefunded: string;
  displayTotalShippingPrice: string;
  displayTotalTax?: string;
  displayCurrentSubtotalPrice: string;
  displayCurrentTotalDuties?: string;
};

export type FulfillmentTrackingInfo = {
  /** The tracking number of the fulfillment. */
  number: string;
  /** The URL to track the fulfillment. */
  url: string;
};

export type Fulfillment = {
  /** List of the fulfillment's line items. */
  /** The name of the tracking company. */
  trackingCompany: string;
  /**
   * Tracking information associated with the fulfillment,
   * such as the tracking number and tracking URL.
   *
   */
  trackingInfo: Array<FulfillmentTrackingInfo>;
};

export interface IOrderLineItem {
  /** The number of entries associated to the line item minus the items that have been removed. */
  currentQuantity: number;
  /** The discounts that have been allocated onto the order line item by discount applications. */
  lineItemDiscount: number; // Sum of all the discount in checkout discountAllocations
  /** The total price of the line item, including discounts, and displayed in the presentment currency. */
  discountedTotalPrice: number;
  /** The total price of the line item, not including any discounts. The total price is calculated using the original unit price multiplied by the quantity, and it is displayed in the presentment currency. */
  originalTotalPrice: number;
  /** The number of products variants associated to the line item. */
  quantity: number;
  /** The title of the product combined with title of the variant. */
  title: string;
  /** The product variant object associated to the line item. */
  variant: ICartProductVariant;
  /** The discounts that have been allocated onto the order line item by discount applications. */
  displayLineItemDiscount: string; // Sum of all the discount in checkout discountAllocations
  /** The total price of the line item, including discounts, and displayed in the presentment currency. */
  displayDiscountedTotalPrice: string;
  /** The total price of the line item, not including any discounts. The total price is calculated using the original unit price multiplied by the quantity, and it is displayed in the presentment currency. */
  displayOriginalTotalPrice: string;
  /** The number of products variants associated to the line item. */
}

export interface ICustomerAccessToken {
  accessToken: string;
  expiresAt: string;
}

export interface ICustomer {
  id: string;
  firstName: string;
  lastName: string;
  acceptsMarketing: boolean;
  email: string;
  phone: string;
  tags: Array<String>;
  defaultAddress?: IShippingAddress;
  addresses?: IShippingAddress[];
}
export interface IProductMetaFields {
  id: string;
  key: string;
  value: string;
  namespace: string;
  description: any;
  type: string;
  reference: IProductMetaFieldsReferences | null;
  references: any | null;
}

export interface IProductMetaFieldsV2 {
  id: string;
  key: string;
  value: string;
  namespace: string;
  description: any;
  type: string;
  reference: IProductMetaFieldsReferencesV2 | null;
  references: any | null;
}

export interface IProductMetaFieldsReferencesV2 {
  handle: string;
  id: string;
  type: string;
  fields: Array<IProductMetaFieldsReferenceField>;
}

export interface IProductMetaFieldsReferences {
  url: string;
}

export interface IProductMetaFieldsReferenceField {
  key: string;
  reference: any;
  type: string;
  value: string;
}

export interface IProductFilters {
  id: string;
  label: string;
  type: string;
  values: Array<{
    id: string;
    count: number;
    input: string;
    label: string;
  }>;
}

export interface IProductSellingPlanAdjustment {
  orderCount?: number;
  percentagePriceAdjustment?: number;
}

export interface IProductSellingPlanOption {
  name: string;
  value: string;
}

export interface IProductSellingPlan {
  id: string;
  name: string;
  description?: string;
  recurringDeliveries?: boolean;
  priceAdjustments?: Array<IProductSellingPlanAdjustment | ISellingPlanFixedPriceAdjustment>;
  options?: IProductSellingPlanOption[];
}

export interface IProductSellingPlanGroup {
  name: string;
  sellingPlans: IProductSellingPlan[];
}

export interface ISellingPlanFixedPriceAdjustment {
  price: MoneyV2;
  compareAtPrice: MoneyV2;
  perDeliveryPrice: MoneyV2;
  unitPrice: null;
}

export interface ICollectionMetafieldImage {
  url: string;
}

export interface IShopifyMenuWithCollection {
  id: string;
  handle: string;
  itemsCount: number;
  title: string;
  items: IShopifyMenuCollection[];
}

export interface IShopifyMenuCollection {
  id: string;
  collectionId: string;
  title: string;
  type: string;
  items: IShopifyMenuCollection[];
}

export interface IShopifyMetafieldInput {
  namespace: string;
  key: string;
}

export interface IShopifyMetaObject {
  metaobject: IShopifyMetaObjectValues;
}
export interface IShopifyMetaObjectValues {
  handle: string;
  id: string;
  onlineStoreUrl: string;
  type: string;
  fields: IShopifyMetaObjectField[];
}

export interface IShopifyMetaObjectField {
  key: string;
  type: string;
  value: string;
  reference?: IReference | null
}

// * Add more references if needed
type IReference = IProductReference | ICollectionReference;
interface IProductReference {
  __typename: "Product";
  id: string;
  handle: string;
  title: string;
  featuredImage: {
    url: string;
    altText: string | null;
  } | null;
}
interface ICollectionReference {
  __typename: "Collection";
  id: string;
  handle: string;
  title: string;
  image: {
    url: string;
    altText: string | null;
  } | null;
}
export interface IShopifyMetaObjects {
  metaobjects: IShopifyMetaObjectValues[]
}
