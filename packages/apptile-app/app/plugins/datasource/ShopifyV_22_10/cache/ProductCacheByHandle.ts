import _ from 'lodash';
import {IProductDetail} from '../types';

export class ProductCacheByHandle {
  private _cacheMap: Record<string, Partial<IProductDetail>>;
  constructor() {
    this._cacheMap = {};
  }

  setProductCache(productHandle: string, productData: Partial<IProductDetail>) {
    if (!productData) return;
    const existingCache = this._cacheMap[productHandle];
    if (existingCache) {
      this._cacheMap[productHandle] = _.merge({}, existingCache, productData);
    } else {
      this._cacheMap[productHandle] = productData;
    }
  }

  public getCacheMap(): Record<string, Partial<IProductDetail>> {
    return this._cacheMap;
  }
}
