import {
  APPTILE_GLOBAL_PLUGIN_ID,
  ActionHandler,
  AppConfig,
  AppModelType,
  LocalStorage,
  PluginConfig,
  PluginEditorsConfig,
  Selector,
  TriggerActionIdentifier,
  modelUpdateAction,
  triggerAction,
} from 'apptile-core';
import checkoutActions from './checkoutAction';

export interface ICountrySwitchActionsDatasourcePluginConfigType {
  changeCountryCode: string;
}

export const countrySwitchActionsDatasourcePluginConfig: ICountrySwitchActionsDatasourcePluginConfigType = {
  changeCountryCode: TriggerActionIdentifier,
};

export const countrySwitchActionDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [],
};

export interface ICountrySwitchActionsInterface {
  changeCountryCode: ActionHandler;
}

class CountrySwitchActions implements ICountrySwitchActionsInterface {
  changeCountryCode = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: AppConfig,
    appModel: AppModelType,
  ) => {
    const payload = params;
    const {countryCode} = payload;

    await checkoutActions.clearLocalCartState(dispatch, config, model, selector, params);
    await LocalStorage.setValue('countryCode', countryCode);

    const countryCodeSelector = selector.concat(['countryCode']);
    const softRefreshPayload = {
      pluginConfig: appConfig.getPlugin(APPTILE_GLOBAL_PLUGIN_ID),
      pluginModel: appModel.getPluginModel('', APPTILE_GLOBAL_PLUGIN_ID),
      pluginSelector: [APPTILE_GLOBAL_PLUGIN_ID],
      eventModelJS: {
        value: 'softRefresh',
        params: {},
      },
    };

    const modelUpdates = [
      {
        selector: countryCodeSelector,
        newValue: countryCode,
      },
    ];
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
    dispatch(triggerAction(softRefreshPayload));
  };
}

const countrySwitchActions = new CountrySwitchActions();
export default countrySwitchActions;
