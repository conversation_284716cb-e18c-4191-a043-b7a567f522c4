import {Buffer} from 'buffer';
import _ from 'lodash';

import {modelUpdateAction} from 'apptile-core';
import ApplePayService from '@/root/app/common/ApplePay/ApplePayService';
import {
  ApplePayContact,
  ApplePayContactUpdateResponse,
  ApplePaySessionPayload,
  ApplePayShippingMethod,
  ApptilePaymentResponse,
} from '@/root/app/common/ApplePay/ApplePayServiceTypes';
import {Selector} from 'apptile-core';
import {GetRegisteredPluginInfo} from 'apptile-core';
import {processShopifyGraphqlQueryResponse} from '../../utils';
import {SHOPIFY_MODEL_APPLE_PAY_AVAILABILTY_ITEM_KEY, SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY} from '../constants';

interface initApplePayParams {
  checkoutId: string;
}

export const resetApplePayStatus = async (dispatch, config, model, selector: Selector, params: any) => {
  const modelUpdate = [
    {
      selector: selector.concat([SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY]),
      newValue: '',
    },
  ];
  dispatch(modelUpdateAction(modelUpdate, undefined, true));
};

export const canMakeApplePayment = async (dispatch, config, model, selector: Selector, params: any) => {
  let applePayAvailable = await ApplePayService.canMakePayment();
  const modelUpdate = [
    {
      selector: selector.concat([SHOPIFY_MODEL_APPLE_PAY_AVAILABILTY_ITEM_KEY]),
      newValue: applePayAvailable,
    },
  ];
  dispatch(modelUpdateAction(modelUpdate, undefined, true));
};

export const initApplePay = async (dispatch, config, model, selector: Selector, params: any) => {
  const shopifyDatasouceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
  const queries = shopifyDatasouceConfig?.plugin?.getQueries();
  const initParams = params as initApplePayParams;
  const queryRunner = model.get('queryRunner');
  const shop = model.get('shop');
  const customerAccessToken = model.get('customerAccessToken');
  const applePayMerchantId = model.get('applePayMerchantId');

  if (!(shop && customerAccessToken && applePayMerchantId && initParams)) {
    logger.info('Apple Pay Can not be used.Please configure the shopify datasource properly');
  }

  const GetCheckoutDetails = async (cid: string) => {
    const CheckoutDetailsQuery = queries?.GetCheckoutDetails;
    var checkoutDetails;
    await queryRunner
      .runQuery('query', CheckoutDetailsQuery.gqlTag, {checkoutId: cid, customerAccessToken}, {})
      .then(response => {
        const {transformedData} = processShopifyGraphqlQueryResponse(response, CheckoutDetailsQuery);
        checkoutDetails = transformedData;
      });
    return checkoutDetails;
  };
  const GetCheckoutShippingMethods = async (cid: string) => {
    const ShippingMethodsQuery = queries?.GetCheckoutShippingRates;
    var shippingMethods;
    var retryCounts = 15;
    do {
      retryCounts--;
      await queryRunner
        .runQuery('query', ShippingMethodsQuery.gqlTag, {checkoutId: cid, customerAccessToken}, {})
        .then(response => {
          const {transformedData} = processShopifyGraphqlQueryResponse(response, ShippingMethodsQuery);
          shippingMethods = transformedData;
        });
    } while (
      !shippingMethods?.availableShippingOptions?.length &&
      !shippingMethods?.requiresShipping &&
      retryCounts > 0
    );
    return shippingMethods;
  };
  const UpdateShippingLine = async (cid: string, srid: string) => {
    const UpdateShippingLineQuery = queries?.UpdateCheckoutShippingLine;
    await queryRunner
      .runQuery('mutation', UpdateShippingLineQuery.gqlTag, {checkoutId: cid, shippingRateHandle: srid}, {})
      .then(response => {
        const {transformedData} = processShopifyGraphqlQueryResponse(response, UpdateShippingLineQuery);
        updateResult = transformedData;
      });
    return updateResult;
  };

  const UpdateEmailAddress = async (cid: string, emailAddress: string) => {
    const emailUpdateQuery = queries?.UpdateCheckoutEmail;
    const response = await queryRunner.runQuery(
      'mutation',
      emailUpdateQuery.gqlTag,
      {checkoutId: cid, email: emailAddress},
      {},
    );
    const {transformedData} = processShopifyGraphqlQueryResponse(response, emailUpdateQuery);
    return transformedData;
  };

  const VerifyCheckoutPayment = async (cid: string) => {
    const checkoutDetailsQuery = queries?.GetCheckoutDetails;
    var checkoutData = null;
    var retryCounts = 15;
    do {
      retryCounts--;
      const response = await queryRunner.runQuery(
        'query',
        checkoutDetailsQuery.gqlTag,
        {checkoutId: cid, customerAccessToken},
        {},
      );
      const {transformedData} = processShopifyGraphqlQueryResponse(response, checkoutDetailsQuery);
      checkoutData = transformedData;
    } while (!checkoutData?.orderId && retryCounts > 0);
    return checkoutData;
  };

  if (initParams?.checkoutId) {
    var checkoutDetails = await GetCheckoutDetails(initParams?.checkoutId);
    var applePaySession: ApplePaySessionPayload = {
      checkoutId: checkoutDetails.id,
      payeeName: shop?.name,
      currencyCode: checkoutDetails?.currencyCode,
      paymentDue: _.toNumber(checkoutDetails?.paymentDue),
      countryCode: shop?.paymentSettings?.countryCode,
      supportedNetworks: ['VISA', 'MASTERCARD', 'AMERICAN_EXPRESS', 'DISCOVER', 'DINERS_CLUB'],
      lineItems: getApplePayLineItemsFromCheckout(checkoutDetails, shop) ?? [],
      requiresShipping: checkoutDetails?.requiresShipping,
    };

    const onAddressUpdate = async (contact: ApplePayContact): Promise<ApplePayContactUpdateResponse> => {
      const UpdateAddressQuery = queries?.UpdateCheckoutShippingAddress;
      await queryRunner.runQuery(
        'mutation',
        UpdateAddressQuery.gqlTag,
        {
          checkoutId: initParams?.checkoutId,
          customerAccessToken,
          shippingAddress: {
            address1: contact.addressLine1,
            address2: contact.addressLine2,
            city: contact.city,
            company: '',
            country: contact.country,
            firstName: contact.firstName,
            lastName: contact.lastName,
            phone: contact.phone,
            province: contact.state,
            zip: contact.postalCode,
          },
        },
        {},
      );
      // .then(response => {
      //   const {transformedData} = processShopifyGraphqlQueryResponse(response, UpdateAddressQuery);
      //   checkoutDetails = transformedData;
      // });

      logger.info('Apple Pay fetch available shipping method init', initParams?.checkoutId);
      const availableShippingMethods = await GetCheckoutShippingMethods(initParams?.checkoutId);
      logger.info('Apple Pay fetch available shipping method completed', JSON.stringify(availableShippingMethods));

      const shippingMethods = getShippingMethodsFromQueryResult(availableShippingMethods) ?? [];
      if (shippingMethods.length) {
        await UpdateShippingLine(initParams?.checkoutId, shippingMethods[0]?.id);
      }
      var checkoutDetails = await GetCheckoutDetails(initParams?.checkoutId);

      logger.info('Apple Pay onAddressUpdate completed', checkoutDetails);
      return {
        lineItems: getApplePayLineItemsFromCheckout(checkoutDetails, shop) ?? [],
        shippingMethods,
      };
    };

    const updateApplePayOrderStatus = (orderId: string) => {
      const modelUpdates = [
        {
          selector: selector.concat([SHOPIFY_MODEL_APPLE_PAY_STATUS_ITEM_KEY]),
          newValue: orderId,
        },
      ];
      dispatch(modelUpdateAction(modelUpdates, undefined, true));
    };

    const completeCheckoutPayment = async (paymentResponse: ApptilePaymentResponse) => {
      const {billingContact, token, shippingContact} = paymentResponse;
      const checkoutId = initParams?.checkoutId;
      var checkoutDetails = await GetCheckoutDetails(checkoutId);
      const idempotencyKey = new Date().getTime().toString(36) + new Date().getUTCMilliseconds();
      const decodedToken = decodePaymentToken(token?.paymentData);

      if (shippingContact?.email) {
        await UpdateEmailAddress(initParams?.checkoutId, shippingContact?.email);
      }

      const completeCheckoutPaymentQuery = queries?.CheckoutCompletePaymentApplePay;
      const response = await queryRunner.runQuery(
        'mutation',
        completeCheckoutPaymentQuery.gqlTag,
        // {checkoutId: checkoutId, shippingRateHandle: srid},
        completeCheckoutPaymentQuery.inputResolver({
          checkoutId: checkoutId,
          idempotencyKey: idempotencyKey,
          paymentAmount: checkoutDetails?.paymentDue,
          currencyCode: checkoutDetails?.currencyCode,
          paymentData: decodedToken,
          testMode: false,
          address1: billingContact?.addressLine1,
          address2: billingContact?.addressLine2,
          city: billingContact?.city,
          company: '',
          country: billingContact?.country,
          firstName: billingContact?.firstName,
          lastName: billingContact?.lastName,
          phone: billingContact?.phone,
          state: billingContact?.state,
          zip: billingContact?.postalCode,
        }),
        {},
      );
      const {transformedData} = processShopifyGraphqlQueryResponse(response, completeCheckoutPaymentQuery);
      return transformedData;
    };

    const onPaymentAuthorized = async (paymentResponse: ApptilePaymentResponse): Promise<boolean> => {
      logger.info('Apple Pay onPaymentAuthorized init', paymentResponse);
      const checkoutId = initParams?.checkoutId;
      try {
        const checkoutCompleteResponse = await completeCheckoutPayment(paymentResponse);
        logger.info('Apple Pay checkoutCompleteResponse response', checkoutCompleteResponse);

        let checkoutPayment = await VerifyCheckoutPayment(checkoutId);
        logger.info('Apple Pay VerifyCheckoutPayment response', checkoutPayment);

        if (checkoutPayment?.orderId) {
          logger.info('Apple Pay orderPlaced orderId', checkoutPayment?.orderId);
          updateApplePayOrderStatus(checkoutPayment?.orderId);
          return checkoutPayment;
        } else {
          logger.error('Apple Pay Unable to process the payment checkoutPayment:', checkoutPayment);
          return false;
        }
      } catch (error) {
        logger.error('Apple Pay onPaymentAuthorized failed with an error:', error);
        return false;
      }
    };

    const onShippingMethodUpdated = async (payShippingMethod: ApplePayShippingMethod): Promise<any> => {
      logger.info('Apple Pay onShippingMethodUpdated init payShippingMethod:', payShippingMethod);
      if (payShippingMethod?.identifier) {
        checkoutDetails = await UpdateShippingLine(initParams?.checkoutId, payShippingMethod?.identifier);
        return getApplePayLineItemsFromCheckout(checkoutDetails, shop) ?? [];
      }
      return [];
    };

    logger.info('Apple Pay Pay initSession:', JSON.stringify(applePaySession), applePayMerchantId);

    ApplePayService.initSession(
      applePayMerchantId,
      applePaySession,
      onAddressUpdate,
      onShippingMethodUpdated,
      onPaymentAuthorized,
    );
  }
};

function getApplePayShippingLineItemFromCheckout(checkout: any) {
  let lineItem;
  if (checkout?.availableShippingOptions) {
    lineItem = {label: 'SHIPPING', amount: _.toNumber(checkout?.selectedShippingOption?.price ?? 0)};
  }
  return lineItem;
}
function getApplePayLineItemsFromCheckout(checkout: any, shop: any) {
  let lines = [];
  lines.push({label: 'CART TOTAL', amount: _.toNumber(checkout?.lineItemsSubtotalAmount)});
  if (checkout?.availableShippingOptions) {
    let shippingLI = getApplePayShippingLineItemFromCheckout(checkout);
    if (shippingLI) lines.push(shippingLI);
  }
  lines.push({label: shop?.name ?? 'TOTAL', amount: _.toNumber(checkout?.paymentDue)});
  return lines;
}

function getShippingMethodsFromQueryResult(data: any): ApplePayShippingMethod[] {
  return data.availableShippingOptions
    ? data?.availableShippingOptions?.map(rate => {
        return {
          label: rate?.title,
          amount: _.toNumber(rate?.price),
          id: rate?.handle,
        };
      })
    : [];
}

function decodePaymentToken(token: string): string {
  const plain = Buffer.from(token, 'base64').toString('utf8');
  return JSON.stringify(JSON.parse(plain));
}
