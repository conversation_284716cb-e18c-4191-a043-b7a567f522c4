import {modelUpdateAction} from 'apptile-core';
import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {ModelChange, Selector} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {PluginPropertySettings, TriggerActionIdentifier} from 'apptile-core';
import {ActionHandler} from '../../../triggerAction';
import Ajax<PERSON>ueryRunner from '../../AjaxWrapper/model';

const SHOPIFY_PRODUCT_GID_MATCH_REGEX = /^gid:\/\/shopify\/Customer\/\d+/; // matches CustomerId with pattern gid://shopify/Customer/{any id}
const SHOPIFY_PRODUCT_GID_PREFIX = 'gid://shopify/Customer/';

export interface ITagCustomerActionsDatasourcePluginConfigType {
  shopManagerApiUrl: string;
  appId: string;
  tagCustomerLoading: boolean;
  addTagsToCustomerCompleted: boolean;
  removeTagsFromCustomerCompleted: boolean;
  addTagsToCustomer: string;
  removeTagsFromCustomer: string;
  clearTagCustomerStatus: string;
}

export const tagCustomerActionsDatasourcePluginConfig: ITagCustomerActionsDatasourcePluginConfigType = {
  shopManagerApiUrl: 'https://api.apptile.io/shopify-shop-manager',
  appId: '',
  tagCustomerLoading: false,
  addTagsToCustomerCompleted: false,
  removeTagsFromCustomerCompleted: false,
  addTagsToCustomer: TriggerActionIdentifier,
  removeTagsFromCustomer: TriggerActionIdentifier,
  clearTagCustomerStatus: TriggerActionIdentifier,
};

export const tagCustomerActionsDatasourceEditor: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'shopManagerApiUrl',
      props: {
        label: 'Shop Manage API URL',
        placeholder: 'https://dev-api.apptile.io/shopify-shop-manager',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'App ID',
        placeholder: 'Apptile app id',
      },
    },
  ],
};

export const tagCustomerActionsDatasourcePropertySettings: PluginPropertySettings = {
  addTagsToCustomer: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return tagCustomerActions.addTagsToCustomer;
    },
    actionMetadata: {
      editableInputParams: {
        tags: '{{["tag1"]}}',
        successMessage: '',
      },
    },
  },
  removeTagsFromCustomer: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return tagCustomerActions.removeTagsFromCustomer;
    },
    actionMetadata: {
      editableInputParams: {
        tags: '{{["tag1"]}}',
        successMessage: '',
      },
    },
  },
  clearTagCustomerStatus: {
    type: TriggerActionIdentifier,
    getValue(model, renderedValue, selector) {
      return tagCustomerActions.clearTagCustomerStatus;
    },
  },
};

export interface ITagCustomerPayload {
  tags: string[];
  successMessage?: string;
}

export interface ITagCustomerActionsInterface {
  addTagsToCustomer: ActionHandler;
  removeTagsFromCustomer: ActionHandler;
}

class TagCustomerActions implements ITagCustomerActionsInterface {
  private resolveNumericCustomerId = (customerGid: string): string => {
    if (SHOPIFY_PRODUCT_GID_MATCH_REGEX.test(`${customerGid}`)) {
      return _.replace(`${customerGid}`, SHOPIFY_PRODUCT_GID_PREFIX, '');
    }
    return `${customerGid}`.toString();
  };

  /**
   * tagCustomerQuery
   * @param model
   * @param tags
   * @param platformUserId
   * @returns
   */
  private AddTagToCustomerQuery = async (model: any, tags: string[]) => {
    const tagCustomerUrl = model.get('shopManagerApiUrl');

    if (!tagCustomerUrl) {
      logger.warn(`invalid tagCustomerUrl ${tagCustomerUrl}`);
      return;
    }

    const appId = model.get('appId');
    if (!appId) {
      logger.warn(`invalid appId ${appId}`);
    }

    const platformUserId = model.get('loggedInUser')?.id;
    const customerAccessToken = model.get('loggedInUserAccessToken')?.accessToken;

    const customerId = this.resolveNumericCustomerId(platformUserId);

    if (!platformUserId && !customerId) {
      logger.warn(`invalid customerId platformUserId: ${platformUserId} customerId: ${customerId}`);
      return;
    }

    const tagCustomerQueryRunner = AjaxQueryRunner();
    tagCustomerQueryRunner.initClient(tagCustomerUrl, config => {
      return config;
    });
    const tagCustomerQueryResponse = await tagCustomerQueryRunner.runQuery(
      'put',
      `/customers/${customerId}/tags`,
      {tags},
      {
        headers: {
          'X-App-Id': appId,
          'x-shopify-customer-access-token': customerAccessToken,
        },
      },
    );
    return tagCustomerQueryResponse;
  };

  /**
   * tagCustomerUrl
   * @param model
   * @param tags
   * @param platformUserId
   * @returns
   */
  private removeTagCustomerQuery = async (model: any, tags: string[]) => {
    const tagCustomerUrl = model.get('shopManagerApiUrl');

    if (!tagCustomerUrl) {
      logger.warn(`invalid tagCustomerUrl ${tagCustomerUrl}`);
      return;
    }

    const appId = model.get('appId');
    if (!appId) {
      logger.warn(`invalid appId ${appId}`);
    }

    const platformUserId = model.get('loggedInUser')?.id;
    const customerAccessToken = model.get('loggedInUserAccessToken')?.accessToken;

    const customerId = this.resolveNumericCustomerId(platformUserId);

    if (!platformUserId && !customerId) {
      logger.warn(`invalid customerId platformUserId: ${platformUserId} customerId: ${customerId}`);
      return;
    }

    const tagCustomerQueryRunner = AjaxQueryRunner();
    tagCustomerQueryRunner.initClient(tagCustomerUrl, config => {
      return config;
    });
    const tagCustomerQueryResponse = await tagCustomerQueryRunner.runQuery(
      'delete',
      `/customers/${customerId}/tags`,
      {tags},
      {
        headers: {
          'X-App-Id': appId,
          'x-shopify-customer-access-token': customerAccessToken,
        },
      },
    );
    return tagCustomerQueryResponse;
  };

  private async setAddTagsReturnStatus(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
    errorExists: boolean = false,
  ) {
    const {successMessage} = params as ITagCustomerPayload;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['tagCustomerLoading']),
      newValue: false,
    });

    newModelUpdates.push({
      selector: selector.concat(['addTagsToCustomerCompleted']),
      newValue: true,
    });

    if (successMessage && !errorExists) {
      toast.show(successMessage, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    if (errorExists) {
      toast.show('An error occured while tagging user', {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    setTimeout(() => dispatch(modelUpdateAction(newModelUpdates, undefined, true)), 1);
  }

  private async setRemoveTagsReturnStatus(
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
    errorExists: boolean = false,
  ) {
    const {successMessage} = params as ITagCustomerPayload;

    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['tagCustomerLoading']),
      newValue: false,
    });

    newModelUpdates.push({
      selector: selector.concat(['removeTagsFromCustomerCompleted']),
      newValue: true,
    });

    if (successMessage && !errorExists) {
      toast.show(successMessage, {
        type: 'success',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }

    if (errorExists) {
      toast.show('An error occured while removing tag', {
        type: 'error',
        placement: 'bottom',
        duration: 2000,
        style: {marginBottom: 80},
      });
    }
    setTimeout(() => dispatch(modelUpdateAction(newModelUpdates, undefined, true)), 1);
  }

  addTagsToCustomer = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates: ModelChange[] = [
      {
        selector: selector.concat(['tagCustomerLoading']),
        newValue: true,
      },
    ];
    try {
      const payload = params as ITagCustomerPayload;
      const {tags} = payload;
      if (_.isEmpty(tags) || tags.length < 1) {
        toast.show('Please a provide a valid list of tags', {
          type: 'error',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
        logger.warn(`Invalid tags supplied ${tags}`);
        return;
      }

      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const tagCustomerQueryResponse = await this.AddTagToCustomerQuery(model, tags);
      logger.info(`tagCustomerQueryResponse ${tagCustomerQueryResponse}`);
      this.setAddTagsReturnStatus(dispatch, config, model, selector, params, appConfig, appModel);
    } catch (error) {
      logger.error(`An Error occured while updating ${error}`);
      this.setAddTagsReturnStatus(dispatch, config, model, selector, params, appConfig, appModel, true);
    }
  };

  removeTagsFromCustomer = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    const loadingModelUpdates: ModelChange[] = [
      {
        selector: selector.concat(['tagCustomerLoading']),
        newValue: true,
      },
    ];
    try {
      const payload = params as ITagCustomerPayload;
      const {tags} = payload;
      if (_.isEmpty(tags) || tags.length < 1) {
        toast.show('Please a provide a valid list of tags', {
          type: 'error',
          placement: 'bottom',
          duration: 2000,
          style: {marginBottom: 80},
        });
        logger.warn(`Invalid tags supplied ${tags}`);
        return;
      }

      dispatch(modelUpdateAction(loadingModelUpdates, undefined, true));

      const tagCustomerQueryResponse = await this.removeTagCustomerQuery(model, tags);
      logger.info(`tagCustomerQueryResponse ${tagCustomerQueryResponse}`);
      this.setRemoveTagsReturnStatus(dispatch, config, model, selector, params, appConfig, appModel);
    } catch (error) {
      logger.error(`An Error occured while updating ${error}`);
      this.setRemoveTagsReturnStatus(dispatch, config, model, selector, params, appConfig, appModel, true);
    }
  };

  clearTagCustomerStatus = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig: any,
    appModel: any,
  ) => {
    let newModelUpdates: ModelChange[] = [];
    newModelUpdates.push({
      selector: selector.concat(['tagCustomerLoading']),
      newValue: false,
    });

    newModelUpdates.push({
      selector: selector.concat(['addTagsToCustomerCompleted']),
      newValue: false,
    });

    newModelUpdates.push({
      selector: selector.concat(['removeTagsFromCustomerCompleted']),
      newValue: false,
    });

    dispatch(modelUpdateAction(newModelUpdates, undefined, true));
  };
}

const tagCustomerActions = new TagCustomerActions();
export default tagCustomerActions;
