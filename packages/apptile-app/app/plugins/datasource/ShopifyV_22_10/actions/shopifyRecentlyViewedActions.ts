import {modelUpdateAction} from 'apptile-core';
import {Selector} from 'apptile-core';
import {
  SHOPIFY_CONFIG_RECENTYLY_VIEWED_LIST_FOR_LOCAL_STORAGE,
  SHOPIFY_MODEL_RECENTYLY_VIEWED_ITEMS_KEY,
  SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY,
} from '../constants';
import { LocalStorage } from 'apptile-core';
import _ from 'lodash';
import {PluginConfig} from 'apptile-core';
import {processShopifyGraphqlQueryResponse} from '../../utils';
import {GetRegisteredPluginInfo} from 'apptile-core';

interface AddToRecentlyViewedParams {
  productId: string;
}

class RecentlyViewedAction {
  addToRecentlyViewed = async (dispatch, config: PluginConfig, model, selector: Selector, params: any) => {
    const payload = params as AddToRecentlyViewedParams;
    const {productId} = payload;
    const modelUpdates = [];

    const recentlyViewedList = model?.get(SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY);
    const recentlyViewedListSelector = selector.concat([SHOPIFY_MODEL_RECENTYLY_VIEWED_LIST_KEY]);

    const updatedList = _.without(recentlyViewedList, productId);
    updatedList.unshift(productId);
    const newRecentlyViewedList = updatedList.slice(0, 10);

    modelUpdates.push({
      selector: recentlyViewedListSelector,
      newValue: newRecentlyViewedList,
    });
    LocalStorage.setValue(SHOPIFY_CONFIG_RECENTYLY_VIEWED_LIST_FOR_LOCAL_STORAGE, newRecentlyViewedList);
    dispatch(modelUpdateAction(modelUpdates, undefined, true));
    this.populateRecentlyViewedItems(dispatch, config, model, selector);
  };

  getRecentlyViewedList = async () => {
    return (await LocalStorage.getValue(SHOPIFY_CONFIG_RECENTYLY_VIEWED_LIST_FOR_LOCAL_STORAGE)) || [];
  };

  _resolveRecentlyViewedItems = async model => {
    const productCacheByHandleRaw = model.get('productCacheByHandle');
    const productCacheByHandle = _.values(productCacheByHandleRaw);

    const recentlyViewedList = (await LocalStorage.getValue(
      SHOPIFY_CONFIG_RECENTYLY_VIEWED_LIST_FOR_LOCAL_STORAGE,
    )) as any[];

    let recentlyViewedCachedItems = _.filter(productCacheByHandle, (product: any) =>
      _.includes(recentlyViewedList, product?.id),
    );

    const productNotInCache = _.filter(recentlyViewedList, productId => {
      return _.findIndex(recentlyViewedCachedItems, cachedProduct => cachedProduct?.id === productId) < 0;
    });

    const getRecentlyViewedProductsFromShopify = async (productNotInCache: string[]) => {
      const shopifyDatasouceConfig = GetRegisteredPluginInfo('shopifyV_22_10');
      const queries = shopifyDatasouceConfig?.plugin?.getQueries();
      const queryRunner = model.get('queryRunner');

      const GetProductByIdsQuery = queries?.GetProductByIds;
      const response = await queryRunner.runQuery(
        'query',
        GetProductByIdsQuery.gqlTag,
        {productIds: productNotInCache},
        {},
      );
      const {transformedData} = processShopifyGraphqlQueryResponse(response, GetProductByIdsQuery);
      const productsFromQuery = transformedData;
      return productsFromQuery;
    };

    try {
      if (productNotInCache.length > 0) {
        const productsFromQuery = await getRecentlyViewedProductsFromShopify(productNotInCache);
        recentlyViewedCachedItems = recentlyViewedCachedItems.concat(productsFromQuery);
      }

      recentlyViewedCachedItems = _.sortBy(recentlyViewedCachedItems, item => {
        return _.indexOf(recentlyViewedList, item.id);
      });

    } catch (error) {
      console.warn('productNotInCache', error);
    }
    return recentlyViewedCachedItems;
  };

  populateRecentlyViewedItems = async (dispatch, config: PluginConfig, model, selector: Selector) => {
    const modelUpdates = [];
    const recentlyViewedItemsSelector = selector.concat([SHOPIFY_MODEL_RECENTYLY_VIEWED_ITEMS_KEY]);
    const recentlyViewedItems = await this._resolveRecentlyViewedItems(model);

    modelUpdates.push({
      selector: recentlyViewedItemsSelector,
      newValue: recentlyViewedItems,
    });

    dispatch(modelUpdateAction(modelUpdates, undefined, true));
  };
}

const recentlyViewedAction = new RecentlyViewedAction();
export default recentlyViewedAction;
