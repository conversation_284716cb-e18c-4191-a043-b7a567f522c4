import {softRestartConfig} from '@/root/web/actions/editorActions';
import {
  APPTILE_GLOBAL_PLUGIN_ID,
  AppConfig,
  AppModelType,
  LocalStorage,
  PluginConfig,
  Selector,
  modelUpdateAction,
  triggerAction,
} from 'apptile-core';
import {restartAppConfig} from 'apptile-core';
import {Platform} from 'react-native';
class CurrencyActions {
  setCurrency = async (
    dispatch: any,
    config: PluginConfig,
    model: any,
    selector: Selector,
    params: any,
    appConfig?: AppConfig,
    appModel?: AppModelType,
  ) => {
    const {currencyCode} = params;
    if (currencyCode) {
      await LocalStorage.setValue('selectedCurrency', currencyCode);
      const countryCode = model
        .get('shopifyCurrencyList')
        ?.find((e: any) => e?.get('code') === currencyCode)
        ?.get('countryCode');
      await LocalStorage.setValue('countryCode', countryCode);
      if (appConfig && appModel) {
        const currencySelector = selector.concat(['selectedCurrency']);
        const countryCodeSelector = selector.concat(['countryCode']);

        const modelUpdates = [
          {
            selector: currencySelector,
            newValue: currencyCode,
          },
          {
            selector: countryCodeSelector,
            newValue: countryCode,
          },
        ];
        dispatch(modelUpdateAction(modelUpdates, undefined, true));
        // dispatch(
        //   Platform.select({
        //     web: softRestartConfig,
        //     default: restartAppConfig,
        //   }),
        // );
        setTimeout(
          () =>
            dispatch(
              Platform.select({
                web: softRestartConfig,
                default: restartAppConfig,
              })(),
            ),
          500,
        );
      }
    }
    // If you need to update any other state or trigger additional logic, add it here.
  };
}

const currencyActions = new CurrencyActions();
export default currencyActions;
