import {DatasourcePluginConfig, IntegrationPlatformType, IApptileDiscountsCredentials} from '../../datasourceTypes';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../../query';

export type IEditableParams = Record<string, any>;

export type TransformerFunction<P, Q> = (
  data: P,
  paginationMeta?: any,
) => {data: Q; hasNext?: boolean; paginationMeta?: any};

export type ApptileDiscountsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

export interface ApptileDiscountsPluginConfigType extends DatasourcePluginConfig {
  apiBaseUrl: string;
  shopifyDS: string;
}

export type DiscountNodes = {
  edges: Edge[];
}

export type Edge = {
  node: Node;
}

export type Node = {
  id:       string;
  discount: Discount;
}

export type Discount = {
  title:   string;
  status:  string;
  summary?: string;
}
