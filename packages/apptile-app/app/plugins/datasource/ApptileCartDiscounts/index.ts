import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {IApptileCartDiscountsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {ApptileDiscountsPluginConfigType, ApptileDiscountsQueryDetails} from './types';
import {TransformDiscounts} from './transformers';
// import {} from './transformer';

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseQuerySpec: Partial<ApptileDiscountsQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    apiBaseUrl: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return {};
  },
  transformer: (data: any) => {
    return data;
  },
};

const apptileCartDiscountsApiRecords: Record<string, ApptileDiscountsQueryDetails> = {
  GetDiscounts: {
    ...baseQuerySpec,
    queryType: 'get',
    endpoint: '/v1/discounts',
    transformer: TransformDiscounts,
  },
};

const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'apptileCartDiscounts',
  type: 'datasource',
  name: 'Apptile Cart Discounts',
  description: 'Apptile Cart Discounts integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const ApptileDiscountsEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'API Base Url',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'shopifyDS',
      props: {
        label: 'shopifyDS',
        placeholder: '{{shopify}}',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'apptileCartDiscounts',
  config: {
    ...baseDatasourceConfig,
    apiBaseUrl: 'https://api.apptile.io/apptile-shopify-discount-manager',
    shopifyDS: '{{shopify}}',
    queryRunner: 'queryRunner',
  } as ApptileDiscountsPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<ApptileDiscountsPluginConfigType>,
    dsModelValues: any,
  ) => {
    const queryRunner = AjaxQueryRunner();
    const shopifyDSModel = dsModelValues?.get('shopifyDS');

    queryRunner.initClient(dsConfig.config.get('apiBaseUrl'), config => {
      config.headers = {
        ...config.headers,
        ...{
          'x-shopify-app-id': shopifyDSModel?.appId,
          'x-shopify-customer-access-token': shopifyDSModel?.storefrontAccessToken,
        },
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return apptileCartDiscountsApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      apptileCartDiscountsApiRecords && apptileCartDiscountsApiRecords[queryName]
        ? apptileCartDiscountsApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IApptileCartDiscountsCredentials,
  ): Partial<ApptileDiscountsPluginConfigType> | boolean {
    const {apiBaseUrl} = credentials;
    if (!apiBaseUrl) return false;
    return {
      apiBaseUrl,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['apiBaseUrl'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'apptileCartDiscounts';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = apptileCartDiscountsApiRecords[queryName];
      if (!queryDetails) return;
      let {endpointResolver, endpoint} = queryDetails ?? {};

      let typedInputVariables, typedDataVariables;
      if (queryDetails && queryDetails.editableInputParams) {
        typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      }

      const {getNextPage, paginationMeta} = options;
      logger.info('isPaginated', queryDetails?.isPaginated, getNextPage);
      logger.info('isPaginated', paginationMeta || queryDetails.paginationMeta);
      if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
        typedInputVariables = queryDetails.paginationResolver(
          typedInputVariables,
          paginationMeta || queryDetails.paginationMeta,
        );
      }

      endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables);

      typedDataVariables = queryDetails.inputResolver
        ? queryDetails.inputResolver(typedInputVariables)
        : typedInputVariables;

      const queryRunner = dsModelValues.get('queryRunner');

      const queryResponse = yield call(queryRunner.runQuery, queryDetails.queryType, endpoint, {
        ...typedDataVariables,
      });
      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = rawData;
      let queryHasNextPage, paginationDetails;
      if (queryDetails && queryDetails.transformer) {
        const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, typedInputVariables);

        transformedData = data;
        queryHasNextPage = hasNextPage;
        paginationDetails = pageData;
      }

      return yield {
        rawData,
        data: transformedData,
        hasNextPage: queryHasNextPage,
        paginationMeta: paginationDetails,
        errors: [],
        hasError: false,
      };
    } catch (error) {
      logger.error(error);
      return yield {
        rawData: {},
        data: {},
        hasNextPage: false,
        paginationMeta: {},
        errors: [error],
        hasError: true,
      };
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: ApptileDiscountsEditors,
});
