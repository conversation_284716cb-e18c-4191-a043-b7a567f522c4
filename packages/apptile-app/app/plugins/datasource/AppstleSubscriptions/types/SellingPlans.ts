export type AppstleSellingPlan = {
  frequencyCount: number;
  frequencyInterval: string;
  billingFrequencyCount: number;
  billingFrequencyInterval: string;
  frequencyName: string;
  frequencyDescription: string;
  discountOffer: number;
  discountOffer2: number;
  afterCycle1: number;
  afterCycle2: number;
  discountType: string;
  discountType2: string;
  discountEnabled: boolean;
  discountEnabled2: boolean;
  discountEnabledMasked: boolean;
  discountEnabled2Masked: boolean;
  id: string;
  frequencyType: string;
  specificDayValue: null;
  specificMonthValue: null;
  specificDayEnabled: boolean;
  maxCycles: null;
  minCycles: null;
  cutOff: number;
  prepaidFlag: string;
  idNew: string;
  planType: string;
  deliveryPolicyPreAnchorBehavior: string;
  freeTrialEnabled: boolean;
  freeTrialCount: null;
  freeTrialInterval: null;
  memberOnly: boolean;
  nonMemberOnly: boolean;
  memberInclusiveTags: null;
  memberExclusiveTags: string;
  formFieldJson: string;
  upcomingOrderEmailBuffer: null;
  frequencySequence: number;
  groupName: string;
  groupId: number;
  inventoryPolicyReserve: string;
  appstleCycles: any[];
};

export type AppstleSellingPlansResponse = AppstleSellingPlan[];

export type TransformedSellingPlan = Pick<
  AppstleSellingPlan,
  | 'frequencyCount'
  | 'frequencyInterval'
  | 'billingFrequencyCount'
  | 'billingFrequencyInterval'
  | 'frequencyName'
  | 'id'
  | 'frequencyType'
  | 'planType'
  | 'idNew'
  | 'groupName'
  | 'groupId'
  | 'frequencySequence'
>;

export interface AppstleProductSellingPlan {
  __typename: string;
  id: string;
  sellingPlanGroups: SellingPlanGroupConnection;
  handle: string;
}

interface SellingPlanGroupConnection {
  __typename: string;
  edges: SellingPlanGroupEdge[];
}

interface SellingPlanGroupEdge {
  __typename: string;
  node: SellingPlanGroup;
}

interface SellingPlanGroup {
  __typename: string;
  sellingPlans: SellingPlanConnection;
}

interface SellingPlanConnection {
  __typename: string;
  edges: SellingPlanEdge[];
}

interface SellingPlanEdge {
  __typename: string;
  node: SellingPlan;
}

interface SellingPlan {
  __typename: string;
  id: string;
  billingPolicy: SellingPlanRecurringBillingPolicy;
  deliveryPolicy: SellingPlanRecurringDeliveryPolicy;
  name: string;
}

interface SellingPlanRecurringBillingPolicy {
  __typename: string;
  interval: string;
  intervalCount: number;
  anchors: any[];
  maxCycles: any | null;
  minCycles: any | null;
}

interface SellingPlanRecurringDeliveryPolicy {
  __typename: string;
  interval: string;
  intervalCount: number;
  anchors: any[];
  cutoff: number;
}

export type TransformedProductSellingPlans = {
  handle: string;
  sellingPlans: {
    frequencyCount: number;
    frequencyInterval: string;
    billingFrequencyCount: number;
    billingFrequencyInterval: string;
    frequencyName: string;
    id: string;
  }[];
};
export type AppstleProductSellingPlanResponse = AppstleProductSellingPlan[];
export type TransformedProductSellingPlansResponse = TransformedProductSellingPlans[];

export type TransformedSellingPlansResponse = TransformedSellingPlan[];
