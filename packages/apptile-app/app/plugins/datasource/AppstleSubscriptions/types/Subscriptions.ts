export type SubscriptionContracts = {
  __typename: string;
  edges: SubscriptionContractsEdge[];
  pageInfo: {__typename: string; hasNextPage: boolean; endCursor: string};
};

export type SubscriptionContractsEdge = {
  __typename: string;
  node: SubscriptionContractsNode;
};

export type SubscriptionContractsNode = {
  __typename: string;
  id: string;
  createdAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: {
    __typename: string;
    amount: string;
  };
  lastPaymentStatus: null | string;
  billingPolicy: BillingPolicy;
  lines: Lines;
  customerPaymentMethod: CustomerPaymentMethod;
  originOrder: {__typename: string; id: string; name: string} | null;
  customer: {
    __typename: string;
    id: string;
  };
};

export type Lines = {
  __typename: string;
  edges: LinesEdge[];
};

export type LinesEdge = {
  __typename: string;
  node: LinesNode;
};

export type LinesNode = {
  __typename: string;
  id: string;
  sellingPlanId: string;
  sellingPlanName: string;
  productId: string;
  sku: string;
  title: string;
  variantId: string;
  quantity: number;
  customAttributes: any[];
  lineDiscountedPrice: DeliveryPrice;
  variantImage: VariantImage;
  variantTitle: string;
  currentPrice: DeliveryPrice;
  discountAllocations?: DiscountAllocation[];
  pricingPolicy?: PricingPolicy;
  taxable?: boolean;
};

export type Price = {
  __typename: string;
  amount: string;
  currencyCode: string;
};

export type BillingPolicy = {
  __typename: string;
  interval: string;
  intervalCount: number;
  anchors?: any[];
  maxCycles?: null;
  minCycles?: null;
};

export type CustomAttribute = {
  __typename: string;
  key: string;
  value: string;
};

export type Customer = {
  __typename: string;
  id: string;
  email: string;
  displayName: string;
  firstName: string;
  lastName: string;
  phone: null;
};

export type CustomerPaymentMethod = {
  __typename: string;
  id?: string;
  instrument: Instrument;
  revokedAt?: null;
  revokedReason?: null;
};

export type Instrument = {
  __typename: string;
  billingAddress?: BillingAddress;
  brand: string;
  expiresSoon: boolean;
  expiryMonth: number;
  expiryYear: number;
  firstDigits: string;
  lastDigits: string;
  maskedNumber: string;
  name: string;
  revocable: boolean;
};

export type BillingAddress = {
  __typename: string;
  address1: string;
  city: string;
  country: string;
  countryCode: string;
  province: string;
  provinceCode: string;
  zip: string;
};

export type DeliveryMethod = {
  __typename: string;
  address: Address;
  shippingOption: ShippingOption;
};

export type Address = {
  __typename: string;
  address1: string;
  address2: null;
  city: string;
  company: null;
  country: string;
  countryCode: string;
  firstName: string;
  lastName: string;
  name: string;
  phone: string;
  province: string;
  provinceCode: string;
  zip: string;
};

export type ShippingOption = {
  __typename: string;
  title: string;
  presentmentTitle: string;
  description: null;
  code: string;
};

export type DeliveryPolicy = {
  __typename: string;
  interval: string;
  intervalCount: number;
  anchors: any[];
};

export type DeliveryPrice = {
  __typename: string;
  amount: string;
  currencyCode: 'string';
};

export type Discounts = {
  __typename: string;
  edges: DiscountsEdge[];
};

export type DiscountsEdge = {
  __typename: string;
  node: DiscountsNode;
};

export type DiscountsNode = {
  __typename: string;
  id: string;
  title: string;
  value: Value;
  recurringCycleLimit: null;
  targetType: string;
  type: string;
  rejectionReason: null;
};

export type Value = {
  __typename: string;
  percentage: number;
};

export type DiscountAllocation = {
  __typename: string;
  amount: DeliveryPrice;
  discount: Discount;
};

export type Discount = {
  __typename: string;
  id: string;
};

export type PricingPolicy = {
  __typename: string;
  basePrice: DeliveryPrice;
  cycleDiscounts: CycleDiscount[];
};

export type CycleDiscount = {
  __typename: string;
  afterCycle: number;
  computedPrice: DeliveryPrice;
  adjustmentType: string;
  adjustmentValue: Value;
};

export type VariantImage = {
  __typename: string;
  transformedSrc: string;
};

export type OriginOrder = {
  __typename: string;
  id: string;
  name: string;
  fulfillmentOrders: FulfillmentOrders;
};

export type FulfillmentOrders = {
  __typename: string;
  edges: FulfillmentOrdersEdge[];
};

export type FulfillmentOrdersEdge = {
  __typename: string;
  node: TentacledNode;
};

export type TentacledNode = {
  __typename: string;
  fulfillAt: Date;
  id: string;
  status: string;
};

export type AppstleSubscriptionCustomersResponse = {
  __typename: string;
  id: string;
  productSubscriberStatus: string;
  tags: string[];
  subscriptionContracts: SubscriptionContracts;
};

export type TransfomedSubscriptionCustomersResponse = {
  id: string;
  productSubscriberStatus: string;
  tags: string[];
  subscriptionContracts: Array<{
    id: string;
    createdAt: Date;
    nextBillingDate: Date;
    status: string;
    deliveryPrice: string;
    billingPolicy: {
      interval: string;
      intervalCount: number;
    };
    lines: Array<{
      id: string;
      sellingPlanName: string;
      productId: string;
      sku: string;
      title: string;
      variantId: string;
      quantity: number;
      lineDiscountedPrice: string;
      variantImageSrc: string;
      variantTitle: null | string;
      currentPrice: string;
    }>;
    originOrder: {
      id: string | null;
      name: string | null;
    };
    customer: {
      id: string;
    };
  }>;
};

export type TransformedLatestSubscriptionByCustomerIdResponse = {
  id: string;
  productSubscriberStatus: string;
  tags: string[];
  subscriptionContract: {
    id: string;
    createdAt: Date;
    nextBillingDate: Date;
    status: string;
    deliveryPrice: string;
    billingPolicy: {
      interval: string;
      intervalCount: number;
    };
    lines: Array<{
      id: string;
      sellingPlanName: string;
      productId: string;
      sku: string;
      title: string;
      variantId: string;
      quantity: number;
      lineDiscountedPrice: string;
      variantImageSrc: string;
      variantTitle: null | string;
      currentPrice: string;
    }>;
    originOrder: {
      id: string | null;
      name: string | null;
    };
    customer: {
      id: string;
    };
  } | null;
};

export type AppstleUpdateSubscriptionIntervalResponse = {
  __typename: string;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: DeliveryPrice;
  lastPaymentStatus: string;
  billingPolicy: BillingPolicy;
  deliveryPolicy: DeliveryPolicy;
  lines: Lines;
  customerPaymentMethod: CustomerPaymentMethod;
  deliveryMethod: DeliveryMethod;
  originOrder: OriginOrder;
  customer: Customer;
  discounts: Discounts;
  note: null;
  customAttributes: CustomAttribute[];
};

export type TransformedUpdateSubscriptionIntervalResponse = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: {
    amount: string;
  };
  deliveryPolicy: {
    interval: string;
    intervalCount: number;
  };
  billingPolicy: {
    interval: string;
    intervalCount: number;
  };
  originOrder: {
    id: string;
    name: string;
  };
  customer_id: string;
  customer_name: string;
  customAttributes: Array<{
    key: string;
    value: string;
  }>;
};

export type AppstleReplaceSubscriptionItemsResponse = {
  __typename: string;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: DeliveryPrice;
  lastPaymentStatus: string;
  billingPolicy: BillingPolicy;
  deliveryPolicy: DeliveryPolicy;
  lines: Lines;
  customerPaymentMethod: CustomerPaymentMethod;
  deliveryMethod: DeliveryMethod;
  originOrder: OriginOrder;
  customer: Customer;
  discounts: Discounts;
  note: null;
  customAttributes: CustomAttribute[];
};

export type TransformedReplaceSubscriptionItemsResponse = {
  id: string;
  createdAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: string;
  deliveryPolicy: {
    interval: string;
    intervalCount: number;
  };
  billingPolicy: {
    interval: string;
    intervalCount: number;
  };
  lines: Array<{
    id: string;
    sellingPlanName: string;
    productId: string;
    sku: string;
    title: string;
    variantId: string;
    quantity: number;
    lineDiscountedPrice: string;
    variantImageSrc: string;
    variantTitle: null | string;
    currentPrice: string;
  }>;
  originOrder: {
    id: string;
    name: string;
  };
  customer: {
    id: string;
  };
  customAttributes: Array<{
    key: string;
    value: string;
  }>;
};

export type AppstleFetchSubscriptionDetailsResponse = {
  __typename: string;
  id: string;
  createdAt: Date;
  updatedAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: DeliveryPrice;
  lastPaymentStatus: string;
  billingPolicy: BillingPolicy;
  deliveryPolicy: DeliveryPolicy;
  lines: Lines;
  customerPaymentMethod: CustomerPaymentMethod;
  deliveryMethod: DeliveryMethod;
  originOrder: OriginOrder;
  customer: Customer;
  discounts: Discounts;
  note: null;
  customAttributes: CustomAttribute[];
};

export type TransformedFetchSubscriptionDetailsResponse = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  nextBillingDate: Date;
  status: string;
  deliveryPrice: string;
  lastPaymentStatus: string;
  deliveryPolicy: {
    interval: string;
    intervalCount: number;
  };
  billingPolicy: {
    interval: string;
    intervalCount: number;
  };
  lines: Array<{
    id: string;
    sellingPlanName: string;
    productId: string;
    sku: string;
    title: string;
    variantId: string;
    quantity: number;
    lineDiscountedPrice: string;
    variantImageSrc: string;
    variantTitle: string;
    currentPrice: string;
  }>;
  originOrder: {
    id: string;
    name: string;
  };
  customer: {
    id: string;
  };
  customAttributes: Array<{
    key: string;
    value: string;
  }>;
};
