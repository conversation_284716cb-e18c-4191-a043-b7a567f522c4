export type AppstleSkipUpcomingOrderResponse = {
  id: number;
  shop: string;
  billingAttemptId: string;
  status: string;
  billingDate: string;
  contractId: number;
  attemptCount: number;
  attemptTime: string;
  graphOrderId: string;
  orderId: number;
  orderAmount: number;
  orderName: string;
  retryingNeeded: boolean;
  transactionFailedEmailSentStatus: string;
  upcomingOrderEmailSentStatus: string;
  applyUsageCharge: boolean;
  recurringChargeId: number;
  transactionRate: number;
  usageChargeStatus: string;
  transactionFailedSmsSentStatus: string;
  upcomingOrderSmsSentStatus: string;
  billingAttemptResponseMessage: string;
  progressAttemptCount: number;
  orderNote: string;
  variantList: {
    variantId: number;
    quantity: number;
    title: string;
    image: string;
    productTitle: string;
    productId: string;
    variantTitle: string;
  }[];
  securityChallengeSentStatus: string;
  orderAmountUSD: number;
  orderCancelReason: string;
  orderCancelledAt: string;
  orderClosed: boolean;
  orderClosedAt: string;
  orderConfirmed: boolean;
  orderDisplayFinancialStatus: string;
  orderDisplayFulfillmentStatus: string;
  orderProcessedAt: string;
  lastShippingUpdatedAt: string;
};

export type TransformedSkipUpcomingOrderResponse = Pick<
  AppstleSkipUpcomingOrderResponse,
  'id' | 'shop' | 'status' | 'billingDate' | 'contractId' | 'orderId'
>;
