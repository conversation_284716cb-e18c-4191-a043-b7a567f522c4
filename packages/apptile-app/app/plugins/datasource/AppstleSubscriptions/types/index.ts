import {DatasourcePluginConfig, IntegrationPlatformType, IAppstleSubscriptionsCredentials} from '../../datasourceTypes';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../../query';

export type IEditableParams = Record<string, any>;

export type TransformerFunction<P, Q> = (
  data: P,
  paginationMeta?: any,
) => {data: Q; hasNext?: boolean; paginationMeta?: any};

export type AppstleSubscriptionsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

export interface AppstleSubscriptionsPluginConfigType extends DatasourcePluginConfig {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
}

export type {
  AppstleSellingPlan,
  AppstleSellingPlansResponse,
  TransformedSellingPlan,
  TransformedSellingPlansResponse,
} from './SellingPlans';

export type {AppstleSkipUpcomingOrderResponse, TransformedSkipUpcomingOrderResponse} from './SkipUpcomingOrder';

export type {
  AppstleSubscriptionCustomersResponse,
  TransfomedSubscriptionCustomersResponse,
  AppstleReplaceSubscriptionItemsResponse,
  AppstleUpdateSubscriptionIntervalResponse,
  AppstleFetchSubscriptionDetailsResponse,
  TransformedLatestSubscriptionByCustomerIdResponse,
  TransformedReplaceSubscriptionItemsResponse,
  TransformedUpdateSubscriptionIntervalResponse,
  TransformedFetchSubscriptionDetailsResponse,
} from './Subscriptions';
