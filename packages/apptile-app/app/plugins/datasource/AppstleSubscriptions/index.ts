import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import _ from 'lodash';
import {call} from 'redux-saga/effects';
import {IAppstleSubscriptionsCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginPropertySettings} from 'apptile-core';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/';
import AjaxQueryRunner from '../AjaxWrapper/model';
import {baseDatasourceConfig} from '../base';
import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {AppstleSubscriptionsPluginConfigType, AppstleSubscriptionsQueryDetails} from './types';
import {
  TransformedFetchSubscriptionDetails,
  TransformLatestSubscriptionByCustomerId,
  TransformSellingPlans,
  TransformSkipUpcomingOrder,
  TransformSubscriptionsByCustomerId,
  TransformUpdateSubscriptionInterval,
} from './transformers';
// import {} from './transformer';

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {
    customerAccessToken: '';
    [key: string]: any;
  },
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

const baseAppstleQuerySpec: Partial<AppstleSubscriptionsQueryDetails> = {
  isPaginated: false,
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    return {};
  },
  endpointResolver: (endpoint, inputVariables) => {
    return endpoint;
  },
  inputResolver: (inputVariables: any) => {
    return {};
  },
  transformer: (data: any) => {
    return data;
  },
};

const getIdFromShopifyGid = (gid: string) => String(gid.split('/').pop());

const appstleSubscriptionsApiRecords: Record<string, AppstleSubscriptionsQueryDetails> = {
  FetchSubscriptionsByCustomerId: {
    ...baseAppstleQuerySpec,
    queryType: 'get',
    endpoint: '/api/external/v2/subscription-customers/',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      customerId: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {customerId} = inputVariables;
      const resolvedEndpoint = `${endpoint}${getIdFromShopifyGid(customerId)}`;
      return resolvedEndpoint;
    },
    transformer: TransformSubscriptionsByCustomerId,
  },
  FetchLatestSubscriptionByCustomerId: {
    ...baseAppstleQuerySpec,
    queryType: 'get',
    endpoint: '/api/external/v2/subscription-customers/',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      customerId: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {customerId} = inputVariables;
      const resolvedEndpoint = `${endpoint}${getIdFromShopifyGid(customerId)}`;
      return resolvedEndpoint;
    },
    transformer: TransformLatestSubscriptionByCustomerId,
  },
  FetchSubscriptionDetailsById: {
    ...baseAppstleQuerySpec,
    queryType: 'get',
    endpoint: '/api/external/v2/subscription-contracts/contract-external/',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId} = inputVariables;
      const resolvedEndpoint = `${endpoint}${getIdFromShopifyGid(subscriptionId)}`;
      return resolvedEndpoint;
    },
    transformer: TransformedFetchSubscriptionDetails,
  },
  SkipUpcomingOrder: {
    ...baseAppstleQuerySpec,
    queryType: 'put',
    endpoint: '/api/external/v2/subscription-billing-attempts/skip-upcoming-order',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId} = inputVariables;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString({
        subscriptionContractId: getIdFromShopifyGid(subscriptionId),
      })}`;
      return resolvedEndpoint;
    },
    transformer: TransformSkipUpcomingOrder,
  },
  UpdateStatus: {
    ...baseAppstleQuerySpec,
    queryType: 'put',
    endpoint: '/api/external/v2/subscription-contracts-update-status',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
      status: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId, status} = inputVariables;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString({
        status,
        contractId: getIdFromShopifyGid(subscriptionId),
      })}`;
      return resolvedEndpoint;
    },
  },
  UpdateSubscriptionDeliveryInterval: {
    ...baseAppstleQuerySpec,
    queryType: 'put',
    endpoint: '/api/external/v2/subscription-contracts-update-delivery-interval',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
      sellingPlan: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId, sellingPlan} = inputVariables;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString({
        deliveryIntervalCount: sellingPlan.frequencyCount,
        deliveryInterval: sellingPlan.frequencyInterval,
        contractId: getIdFromShopifyGid(subscriptionId),
      })}`;
      return resolvedEndpoint;
    },
    transform: TransformUpdateSubscriptionInterval,
  },
  UpdateSubscriptionBillingInterval: {
    ...baseAppstleQuerySpec,
    queryType: 'put',
    endpoint: '/api/external/v2/subscription-contracts-update-billing-interval',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
      sellingPlan: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId, sellingPlan} = inputVariables;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString({
        intervalCount: sellingPlan.billingFrequencyCount,
        interval: sellingPlan.billingFrequencyInterval,
        contractId: getIdFromShopifyGid(subscriptionId),
      })}`;
      return resolvedEndpoint;
    },
    transform: TransformUpdateSubscriptionInterval,
  },
  UpdateBillingDate: {
    ...baseAppstleQuerySpec,
    queryType: 'put',
    endpoint: '/api/external/v2/subscription-contracts-update-billing-date',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
      nextBillingDate: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId, nextBillingDate} = inputVariables;
      const resolvedEndpoint = `${endpoint}${jsonToQueryString({
        nextBillingDate,
        contractId: getIdFromShopifyGid(subscriptionId),
      })}`;
      return resolvedEndpoint;
    },
  },
  CancelSubscription: {
    ...baseAppstleQuerySpec,
    queryType: 'delete',
    endpoint: '/api/external/v2/subscription-contracts/',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      subscriptionId: '',
      cancellationFeedback: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const {subscriptionId, cancellationFeedback} = inputVariables;
      const resolvedEndpoint = `${endpoint}${getIdFromShopifyGid(subscriptionId)}${jsonToQueryString({
        cancellationFeedback,
      })}`;
      return resolvedEndpoint;
    },
  },
  ReplaceSubscriptionItems: {
    ...baseAppstleQuerySpec,
    queryType: 'post',
    endpoint: '/api/external/v2/subscription-contract-details/replace-variants-v3/',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
      shop: '',
      subscriptionId: '',
      currentSubscriptionItems: '',
      currentCartLineItems: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      const resolvedEndpoint = `${endpoint}`;
      return resolvedEndpoint;
    },
    inputResolver: inputVariables => {
      const {currentCartLineItems, subscriptionId, currentSubscriptionItems, shop} = inputVariables;

      const itemsToAdd = currentCartLineItems.filter(
        item => !currentSubscriptionItems.some(i => i.variantId === item.merchandiseId),
      );
      const itemsToRemove = currentSubscriptionItems.filter(
        item => !currentCartLineItems.some(i => i.merchandiseId === item.variantId),
      );
      const itemsToUpdate = currentCartLineItems.filter(item =>
        currentSubscriptionItems.some(i => i.variantId === item.merchandiseId && i.quantity !== item.newQuantity),
      );

      return {
        shop,
        contractId: Number(getIdFromShopifyGid(subscriptionId)),
        oldVariants: [...itemsToRemove, ...itemsToUpdate].map(i => getIdFromShopifyGid(i.variantId || i.merchandiseId)),
        newVariants: [...itemsToAdd, ...itemsToUpdate].reduce(
          (obj, item) => ({...obj, [getIdFromShopifyGid(item.merchandiseId)]: item.newQuantity}),
          {},
        ),
      };
    },
  },
  FetchAllSellingPlans: {
    ...baseAppstleQuerySpec,
    queryType: 'get',
    endpoint: '/api/external/v2/subscription-groups/all-selling-plans',
    isPaginated: false,
    editableInputParams: {
      customerAccessToken: '',
    },
    endpointResolver: (endpoint, inputVariables) => {
      return endpoint;
    },
    transformer: TransformSellingPlans,
  },
};

const propertySettings: PluginPropertySettings = {};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'appstleSubs',
  type: 'datasource',
  name: 'Appstle Subscriptions',
  description: 'Appstle Subscriptions integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const AppstleSubscriptionsEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'proxyUrl',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      defultValue: '',
      props: {
        label: 'App ID',
        placeholder: 'Apptile app id',
      },
    },
  ],
};

export default wrapDatasourceModel({
  name: 'Appstle Subscriptions',
  config: {
    ...baseDatasourceConfig,
    queryRunner: 'queryRunner',
  } as AppstleSubscriptionsPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<AppstleSubscriptionsPluginConfigType>,
    dsModelValues: any,
  ) => {
    const queryRunner = AjaxQueryRunner();

    queryRunner.initClient(dsConfig.config.get('proxyUrl'), config => {
      config.headers = {
        ...config.headers,
        'x-shopify-app-id': dsConfig.config?.get('appId') ?? '',
        Authorization: 'null',
        Accept: '*/*',
      };
      return config;
    });
    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return appstleSubscriptionsApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      appstleSubscriptionsApiRecords && appstleSubscriptionsApiRecords[queryName]
        ? appstleSubscriptionsApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IAppstleSubscriptionsCredentials,
  ): Partial<AppstleSubscriptionsPluginConfigType> | boolean {
    const {proxyUrl, appId} = credentials;
    if (!proxyUrl || !appId) return false;
    return {
      proxyUrl: proxyUrl,
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['proxyUrl', 'appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'appstleSubs';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    try {
      const queryDetails = appstleSubscriptionsApiRecords[queryName];
      if (!queryDetails) return;
      let {endpointResolver, endpoint} = queryDetails ?? {};

      let typedInputVariables, typedDataVariables;
      if (queryDetails && queryDetails.editableInputParams) {
        typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);
      }

      const {getNextPage, paginationMeta} = options;
      logger.info('isPaginated', queryDetails?.isPaginated, getNextPage);
      logger.info('isPaginated', paginationMeta || queryDetails.paginationMeta);
      if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
        typedInputVariables = queryDetails.paginationResolver(
          typedInputVariables,
          paginationMeta || queryDetails.paginationMeta,
        );
      }

      endpoint = endpointResolver && endpointResolver(endpoint, typedInputVariables);

      typedDataVariables = queryDetails.inputResolver
        ? queryDetails.inputResolver(typedInputVariables)
        : typedInputVariables;

      const queryRunner = dsModelValues.get('queryRunner');
      const queryResponse = yield call(
        queryRunner.runQuery,
        queryDetails.queryType,
        endpoint,
        {
          ...typedDataVariables,
        },
        {
          headers: {
            'Content-Type': queryDetails?.headerType || 'application/json',
            'x-shopify-customer-access-token': typedInputVariables?.customerAccessToken,
          },
        },
      );
      const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
      let transformedData = rawData;
      let queryHasNextPage, paginationDetails;
      if (queryDetails && queryDetails.transformer) {
        const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData, typedInputVariables);

        transformedData = data;
        queryHasNextPage = hasNextPage;
        paginationDetails = pageData;
      }

      return yield {
        rawData,
        data: transformedData,
        hasNextPage: queryHasNextPage,
        paginationMeta: paginationDetails,
        errors: [],
        hasError: false,
      };
    } catch (error) {
      logger.error(error);
      return yield {
        rawData: {},
        data: {},
        hasNextPage: false,
        paginationMeta: {},
        errors: [error],
        hasError: true,
      };
    }
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: AppstleSubscriptionsEditors,
});
