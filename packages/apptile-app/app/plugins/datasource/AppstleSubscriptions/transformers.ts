import {
  AppstleSellingPlansResponse,
  AppstleSkipUpcomingOrderResponse,
  AppstleSubscriptionCustomersResponse,
  TransformedSellingPlansResponse,
  TransformedSkipUpcomingOrderResponse,
  TransfomedSubscriptionCustomersResponse,
  TransformerFunction,
  AppstleReplaceSubscriptionItemsResponse,
  AppstleUpdateSubscriptionIntervalResponse,
  TransformedReplaceSubscriptionItemsResponse,
  TransformedUpdateSubscriptionIntervalResponse,
  TransformedLatestSubscriptionByCustomerIdResponse,
  AppstleFetchSubscriptionDetailsResponse,
  TransformedFetchSubscriptionDetailsResponse,
} from './types';
import {
  AppstleProductSellingPlanResponse,
  TransformedProductSellingPlans,
  TransformedProductSellingPlansResponse,
} from './types/SellingPlans';

export const TransformSellingPlans: TransformerFunction<
  AppstleSellingPlansResponse,
  TransformedSellingPlansResponse
> = (data, paginationMeta?: any) => {
  return {
    data: data.map(plan => {
      return {
        frequencyCount: plan.frequencyCount,
        frequencyInterval: plan.frequencyInterval,
        billingFrequencyCount: plan.billingFrequencyCount,
        billingFrequencyInterval: plan.billingFrequencyInterval,
        frequencyName: plan.frequencyName,
        id: plan.id,
        frequencyType: plan.frequencyType,
        planType: plan.planType,
        idNew: plan.idNew,
        groupName: plan.groupName,
        groupId: plan.groupId,
        frequencySequence: plan.frequencySequence,
      };
    }),
  };
};

export const TransformProductSellingPlans: TransformerFunction<
  AppstleProductSellingPlanResponse,
  TransformedProductSellingPlansResponse
> = data => {
  let tranformedData: TransformedProductSellingPlansResponse = [];
  for (let i = 0; i < data.length; i++) {
    console.log('debug data', data);
    let productSellingPlanData: TransformedProductSellingPlans = {
      handle: '',
      sellingPlans: [],
    };
    productSellingPlanData.handle = data[i].handle;
    for (let j = 0; j < data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges.length; j++) {
      let sellingPlan = {
        id: '',
        billingFrequencyCount: 0,
        billingFrequencyInterval: '',
        frequencyCount: 0,
        frequencyInterval: '',
        frequencyName: '',
      };
      sellingPlan.id = data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.id;
      sellingPlan.billingFrequencyCount =
        data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.billingPolicy.intervalCount;
      sellingPlan.billingFrequencyInterval =
        data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.billingPolicy.interval;
      sellingPlan.frequencyCount =
        data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.deliveryPolicy.intervalCount;
      sellingPlan.frequencyInterval =
        data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.deliveryPolicy.interval;
      sellingPlan.frequencyName = data[i].sellingPlanGroups.edges[0].node.sellingPlans.edges[j].node.name;
      productSellingPlanData.sellingPlans[j] = sellingPlan;
    }
    tranformedData[i] = productSellingPlanData;
  }
  return {
    data: tranformedData,
  };
};

export const TransformSkipUpcomingOrder: TransformerFunction<
  AppstleSkipUpcomingOrderResponse,
  TransformedSkipUpcomingOrderResponse
> = (data, paginationMeta?: any) => {
  return {
    data: {
      id: data.id,
      shop: data.shop,
      status: data.status,
      billingDate: data.billingDate,
      contractId: data.contractId,
      orderId: data.orderId,
    },
  };
};

export const TransformSubscriptionsByCustomerId: TransformerFunction<
  AppstleSubscriptionCustomersResponse,
  TransfomedSubscriptionCustomersResponse
> = (data, paginationMeta?: any) => {
  return {
    data: {
      id: data.id,
      productSubscriberStatus: data.productSubscriberStatus,
      tags: data.tags,
      subscriptionContracts:
        data.subscriptionContracts.edges
          ?.map(edge => ({
            id: edge.node.id,
            createdAt: edge.node.createdAt,
            nextBillingDate: edge.node.nextBillingDate,
            status: edge.node.status,
            deliveryPrice: edge.node.deliveryPrice.amount,
            billingPolicy: {
              interval: edge.node.billingPolicy.interval,
              intervalCount: edge.node.billingPolicy.intervalCount,
            },
            lines:
              edge.node.lines.edges?.map(lineEdge => ({
                id: lineEdge.node.id,
                sellingPlanName: lineEdge.node.sellingPlanName,
                productId: lineEdge.node.productId,
                sku: lineEdge.node.sku,
                title: lineEdge.node.title,
                variantId: lineEdge.node.variantId,
                quantity: lineEdge.node.quantity,
                lineDiscountedPrice: lineEdge.node.lineDiscountedPrice.amount,
                variantImageSrc: lineEdge.node.variantImage.transformedSrc,
                variantTitle: lineEdge.node.variantTitle,
                currentPrice: lineEdge.node.currentPrice.amount,
              })) || [],
            originOrder: {
              id: edge.node.originOrder?.id || null,
              name: edge.node.originOrder?.name || null,
            },
            customer: {
              id: edge.node.customer.id,
            },
          }))
          .filter(subscription => subscription.status !== 'CANCELLED') || [],
    },
  };
};

export const TransformLatestSubscriptionByCustomerId: TransformerFunction<
  AppstleSubscriptionCustomersResponse,
  TransformedLatestSubscriptionByCustomerIdResponse | null
> = (data, paginationMeta?: any) => {
  const subscriptionContract = data.subscriptionContracts.edges
    ?.filter(edge => edge.node.status !== 'CANCELLED')
    // @ts-ignore
    ?.reduce((prev, current) => (new Date(prev.node.createdAt) > current.node.createdAt ? prev : current), {
      node: {createdAt: 0},
    })?.node;

  return {
    data: subscriptionContract
      ? {
          id: data.id,
          productSubscriberStatus: data.productSubscriberStatus,
          tags: data.tags,
          subscriptionContract: subscriptionContract
            ? {
                id: subscriptionContract.id,
                createdAt: subscriptionContract.createdAt,
                nextBillingDate: subscriptionContract.nextBillingDate,
                status: subscriptionContract.status,
                deliveryPrice: subscriptionContract.deliveryPrice.amount,
                billingPolicy: {
                  interval: subscriptionContract.billingPolicy.interval,
                  intervalCount: subscriptionContract.billingPolicy.intervalCount,
                },
                lines:
                  subscriptionContract.lines.edges?.map(lineEdge => ({
                    id: lineEdge.node.id,
                    sellingPlanName: lineEdge.node.sellingPlanName,
                    productId: lineEdge.node.productId,
                    sku: lineEdge.node.sku,
                    title: lineEdge.node.title,
                    variantId: lineEdge.node.variantId,
                    quantity: lineEdge.node.quantity,
                    lineDiscountedPrice: lineEdge.node.lineDiscountedPrice.amount,
                    variantImageSrc: lineEdge.node.variantImage.transformedSrc,
                    variantTitle: lineEdge.node.variantTitle,
                    currentPrice: lineEdge.node.currentPrice.amount,
                  })) || [],
                originOrder: {
                  id: subscriptionContract.originOrder?.id || null,
                  name: subscriptionContract.originOrder?.name || null,
                },
                customer: {
                  id: subscriptionContract.customer.id,
                },
              }
            : null,
        }
      : null,
  };
};

export const TransformUpdateSubscriptionInterval: TransformerFunction<
  AppstleUpdateSubscriptionIntervalResponse,
  TransformedUpdateSubscriptionIntervalResponse
> = (data, paginationMeta?: any) => {
  return {
    data: {
      id: data.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      nextBillingDate: data.nextBillingDate,
      status: data.status,
      customer_id: data.customer.id,
      customer_name: data.customer.displayName,
      deliveryPrice: {
        amount: data.deliveryPrice.amount,
        currencyCode: data.deliveryPrice.currencyCode,
      },
      deliveryPolicy: {
        interval: data.deliveryPolicy.interval,
        intervalCount: data.deliveryPolicy.intervalCount,
      },
      billingPolicy: {
        interval: data.billingPolicy.interval,
        intervalCount: data.billingPolicy.intervalCount,
      },
      originOrder: {
        id: data.originOrder.id,
        name: data.originOrder.name,
      },
      customAttributes: data.customAttributes.map(attribute => ({
        key: attribute.key,
        value: attribute.value,
      })),
    },
  };
};

export const TransformReplaceSubscriptionItems: TransformerFunction<
  AppstleReplaceSubscriptionItemsResponse,
  TransformedReplaceSubscriptionItemsResponse
> = (data, paginationMeta?: any) => {
  return {
    data: {
      id: data.id,
      createdAt: data.createdAt,
      nextBillingDate: data.nextBillingDate,
      status: data.status,
      deliveryPrice: data.deliveryPrice.amount,
      deliveryPolicy: {
        interval: data.deliveryPolicy.interval,
        intervalCount: data.deliveryPolicy.intervalCount,
      },
      billingPolicy: {
        interval: data.billingPolicy.interval,
        intervalCount: data.billingPolicy.intervalCount,
      },
      lines: data.lines.edges?.map(lineEdge => ({
        id: lineEdge.node.id,
        sellingPlanName: lineEdge.node.sellingPlanName,
        productId: lineEdge.node.productId,
        sku: lineEdge.node.sku,
        title: lineEdge.node.title,
        variantId: lineEdge.node.variantId,
        quantity: lineEdge.node.quantity,
        lineDiscountedPrice: lineEdge.node.lineDiscountedPrice.amount,
        variantImageSrc: lineEdge.node.variantImage.transformedSrc,
        variantTitle: lineEdge.node.variantTitle,
        currentPrice: lineEdge.node.currentPrice.amount,
      })),
      originOrder: {
        id: data.originOrder.id,
        name: data.originOrder.name,
      },
      customer: {
        id: data.customer.id,
        displayName: data.customer.displayName,
      },
      customAttributes: data.customAttributes.map(attribute => ({
        key: attribute.key,
        value: attribute.value,
      })),
    },
  };
};

export const TransformedFetchSubscriptionDetails: TransformerFunction<
  AppstleFetchSubscriptionDetailsResponse,
  TransformedFetchSubscriptionDetailsResponse | null
> = (data, paginationMeta?: any) => {
  return {
    data:
      data.status === 'CANCELLED'
        ? null
        : {
            id: data.id,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
            nextBillingDate: data.nextBillingDate,
            status: data.status,
            deliveryPrice: data.deliveryPrice.amount,
            lastPaymentStatus: data.lastPaymentStatus,
            deliveryPolicy: {
              interval: data.deliveryPolicy.interval,
              intervalCount: data.deliveryPolicy.intervalCount,
            },
            billingPolicy: {
              interval: data.billingPolicy.interval,
              intervalCount: data.billingPolicy.intervalCount,
            },
            lines: data.lines.edges?.map(lineEdge => ({
              id: lineEdge.node.id,
              sellingPlanName: lineEdge.node.sellingPlanName,
              productId: lineEdge.node.productId,
              sku: lineEdge.node.sku,
              title: lineEdge.node.title,
              variantId: lineEdge.node.variantId,
              quantity: lineEdge.node.quantity,
              lineDiscountedPrice: lineEdge.node.lineDiscountedPrice.amount,
              variantImageSrc: lineEdge.node.variantImage.transformedSrc,
              variantTitle: lineEdge.node.variantTitle,
              currentPrice: lineEdge.node.currentPrice.amount,
            })),
            address: {
              address1: data.deliveryMethod.address.address1,
              address2: data.deliveryMethod.address.address2,
              city: data.deliveryMethod.address.city,
              company: data.deliveryMethod.address.company,
              country: data.deliveryMethod.address.country,
              countryCode: data.deliveryMethod.address.countryCode,
              firstName: data.deliveryMethod.address.firstName,
              lastName: data.deliveryMethod.address.lastName,
              name: data.deliveryMethod.address.name,
              phone: +data.deliveryMethod.address.phone,
              province: data.deliveryMethod.address.province,
              provinceCode: data.deliveryMethod.address.provinceCode,
              zip: data.deliveryMethod.address.zip,
            },
            originOrder: {
              id: data.originOrder.id,
              name: data.originOrder.name,
            },
            customer: {
              id: data.customer.id,
            },
            customAttributes: data.customAttributes.map(attribute => ({
              key: attribute.key,
              value: attribute.value,
            })),
          },
  };
};
