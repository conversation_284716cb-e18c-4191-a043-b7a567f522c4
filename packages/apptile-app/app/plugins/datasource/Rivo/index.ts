import {PluginEditorsConfig} from '@/root/app/common/EditorControlTypes';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from '../../plugin';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from '../../query/index';
import AjaxQueryRunner from '../AjaxWrapper/model';
// import {jsonToQueryString} from '../RestApi/utils';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {
  TransformCouponCode,
  TransformEarningRules,
  TransformPointsHistory,
  TransformRedemptionHistory,
  TransformSingleCsutomer,
  TransformSingleVipTier,
  TransformVipTiers,
  TransfromAllRewards,
} from './transformer';
import _ from 'lodash';
import {IntegrationPlatformType} from '../datasourceTypes';

export interface RivoRewardsPluginConfigType extends IRivoRewardsCredentials {
  proxyUrl: string;
  appId: string;
  queryRunner: any;
}

type IEditableParams = Record<string, any>;

type RivoRewardsQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any, inputParams: any) => any;
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
  headers?: Record<string, any>;
  checkInputVariabes?: (inputVariables: Record<string, any>) => boolean;
};
export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

const baseRivoRewardsQuerySpec: Partial<RivoRewardsQueryDetails> = {
  isPaginated: false,
  contextInputParams: {
    proxyUrl: '',
    platformType: '',
  },
  paginationResolver: (inputVariables: Record<string, any>, paginationMeta: any): Record<string, any> => {
    const {startIndex} = paginationMeta ?? {};
    return startIndex ? {...inputVariables, startIndex} : inputVariables;
  },
};

const RivoRewardsApiRecords: Record<string, Partial<RivoRewardsQueryDetails>> = {
  getWaysToEarn: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/earning-rules',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {per_page, page} = inputParams;
      let nerPage = page;
      if (paginationMeta) {
        nerPage = Number.parseInt(paginationMeta?.nextPage);
      }

      const resolvedEndpoint = `${endpoint}?pagination[per_page]=${per_page}&pagination[page]=${nerPage}`;

      return resolvedEndpoint;
    },
    transformer: TransformEarningRules,

    isPaginated: true,

    editableInputParams: {
      per_page: 100,
      page: 1,
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {per_page, page} = inputVariables;
      return !!per_page && !!page;
    },
  },
  getWaysToReedem: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/rewards',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {per_page, page, sort_by, sort_type} = inputParams;
      let nerPage = page;
      let resolvedEndpoint;
      if (paginationMeta) {
        nerPage = Number.parseInt(paginationMeta?.nextPage);
      }

      if (sort_type !== undefined) {
        resolvedEndpoint = `${endpoint}?pagination[per_page]=${per_page}&pagination[page]=${nerPage}&sort[order_direction]=${sort_by}&sort[order_type]=${sort_type}`;
      } else {
        resolvedEndpoint = `${endpoint}?pagination[per_page]=${per_page}&pagination[page]=${nerPage}&sort[order_direction]=${sort_by}`;
      }

      return resolvedEndpoint;
    },
    transformer: TransfromAllRewards,
    isPaginated: true,

    editableInputParams: {
      per_page: 100,
      page: 1,
      sort_by: 'asc or desc',
      sort_type: 'name or points_amount or redeemed_count or dont fill thi field',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {per_page, page, sort_by} = inputVariables;
      return !!per_page && !!page && !!sort_by;
    },
  },
  generateCouponCode: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'post',
    endpoint: '/api/points-redemptions/create',
    endpointResolver: endpoint => {
      return endpoint;
    },
    editableInputParams: {
      shopifyCustomerId: '',
      rewardsId: 1234,
      pointsAmount: '',
      creditsAmount: '',
    },
    transformer: TransformCouponCode,
    inputResolver: (inputVariables: any) => {
      const {shopifyCustomerId, rewardsId, pointsAmount, creditsAmount} = inputVariables;
      return {
        shopifyCustomerId,
        rewardsId,
        pointsAmount,
        creditsAmount,
      };
    },
  },
  getCustomerDetails: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/customers',
    endpointResolver: (endpoint, inputParams) => {
      const {shopifyCustomerId} = inputParams;

      let resolvedEndpoint = `${endpoint}/${shopifyCustomerId}`;
      return resolvedEndpoint;
    },
    transformer: TransformSingleCsutomer,

    editableInputParams: {
      shopifyCustomerId: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {shopifyCustomerId} = inputVariables;
      return !!shopifyCustomerId;
    },
  },
  updateCustomer: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'put',
    endpoint: '/api/customers',
    endpointResolver: (endpoint, inputParams) => {
      const {shopifyCustomerId} = inputParams;
      console.log(shopifyCustomerId, inputParams, 'xxx');

      let resolvedEndpoint = `${endpoint}/${shopifyCustomerId}/update`;
      return resolvedEndpoint;
    },
    transformer: TransformSingleCsutomer,
    editableInputParams: {
      shopifyCustomerId: '',
      month: '',
      day: '',
    },
    inputResolver: (inputVariables: any) => {
      const {month, day, shopifyCustomerId} = inputVariables;
      return {
        month: Number(month),
        day: Number(day),
        shopifyCustomerId,
      };
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {shopifyCustomerId} = inputVariables;
      return !!shopifyCustomerId;
    },
  },
  fetchCoupons: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/points-redemptions',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {per_page, page, sort_by, sort_type, source, reward_id, code, used, referred_email, shopifyCustomerId} =
        inputParams;

      let nerPage = page;
      if (paginationMeta) {
        nerPage = Number.parseInt(paginationMeta?.nextPage);
      }

      // Start constructing the query string
      const queryParams: Record<string, any> = {
        'filters[customer_identifier]': shopifyCustomerId,
        'pagination[per_page]': per_page,
        'pagination[page]': nerPage,
        'sort[order_direction]': sort_by,
      };

      // Add optional parameters if they are not empty or undefined
      if (sort_type !== undefined && sort_type !== '') {
        queryParams['sort[order_type]'] = sort_type;
      }
      if (source !== undefined && source !== '') {
        queryParams['filters[source]'] = source;
      }
      if (reward_id !== undefined && reward_id !== '') {
        queryParams['filters[reward_id]'] = reward_id;
      }
      if (code !== undefined && code !== '') {
        queryParams['filters[code]'] = code;
      }
      if (used !== undefined && used !== '') {
        queryParams['filters[used]'] = used;
      }
      if (referred_email !== undefined && referred_email !== '') {
        queryParams['filters[referred_email]'] = referred_email;
      }

      // Build the query string
      const queryString = Object.keys(queryParams)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
        .join('&');

      // Final resolved endpoint with query parameters
      const resolvedEndpoint = `${endpoint}?${queryString}`;
      return resolvedEndpoint;
    },
    transformer: TransformRedemptionHistory,
    isPaginated: true,
    editableInputParams: {
      shopifyCustomerId: '',
      source: '',
      reward_id: '',
      code: '',
      used: '',
      referred_email: '',
      per_page: 100,
      page: 1,
      sort_by: 'asc or desc',
      sort_type: '',
    },
  },
  fetchTransactions: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/points-events',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {per_page, page, sort_by, sort_type, source, shopifyCustomerId} = inputParams;

      let nerPage = page;
      if (paginationMeta) {
        nerPage = Number.parseInt(paginationMeta?.nextPage);
      }

      // Start constructing the query string
      const queryParams: Record<string, any> = {
        'filters[customer_identifier]': shopifyCustomerId,
        'pagination[per_page]': per_page,
        'pagination[page]': nerPage,
        'sort[order_direction]': sort_by,
      };

      // Add optional parameters if they are not empty or undefined
      if (sort_type !== undefined && sort_type !== '') {
        queryParams['sort[order_type]'] = sort_type;
      }
      if (source !== undefined && source !== '') {
        queryParams['filters[source]'] = source;
      }

      // Build the query string
      const queryString = Object.keys(queryParams)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
        .join('&');

      // Final resolved endpoint with query parameters
      const resolvedEndpoint = `${endpoint}?${queryString}`;
      return resolvedEndpoint;
    },
    transformer: TransformPointsHistory,
    isPaginated: true,
    editableInputParams: {
      shopifyCustomerId: '',
      source: '',
      per_page: 100,
      page: 1,
      sort_by: 'asc or desc',
      sort_type: '',
    },
  },
  getALLVipTiers: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/vip-tiers',
    endpointResolver: (endpoint, inputParams, paginationMeta) => {
      const {per_page, page, sort_by, sort_type} = inputParams;
      let nerPage = page;
      if (paginationMeta) {
        nerPage = Number.parseInt(paginationMeta?.nextPage);
      }
      let resolvedEndpoint;
      if (sort_type !== undefined) {
        resolvedEndpoint = `${endpoint}?pagination[per_page]=${per_page}&pagination[page]=${nerPage}&&sort[order_direction]=${sort_by}&sort[order_type]=${sort_type}`;
      } else {
        resolvedEndpoint = `${endpoint}?pagination[per_page]=${per_page}&pagination[page]=${nerPage}&&sort[order_direction]=${sort_by}`;
      }

      return resolvedEndpoint;
    },
    transformer: TransformVipTiers,

    isPaginated: true,

    editableInputParams: {
      per_page: 100,
      page: 1,
      sort_by: '',
      sort_type: 'name or threshold,or perks',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {per_page, page} = inputVariables;
      return !!per_page && !!page;
    },
  },
  getSingleVipTier: {
    ...baseRivoRewardsQuerySpec,
    queryType: 'get',
    endpoint: '/api/vip-tiers',
    endpointResolver: (endpoint, inputParams) => {
      const {vipTierId} = inputParams;

      let resolvedEndpoint = `${endpoint}/${vipTierId}`;
      return resolvedEndpoint;
    },
    transformer: TransformSingleVipTier,
    editableInputParams: {
      vipTierId: '',
    },
    checkInputVariabes: (inputVariables: Record<string, any>) => {
      const {vipTierId} = inputVariables;
      return !!vipTierId;
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'rivo',
  type: 'datasource',
  name: 'Rivo',
  description: 'Rivo: Loyalty & Referrals',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const RivoRewardsEditors: PluginEditorsConfig<any> = {
  basic: [
    {
      type: 'codeInput',
      name: 'proxyUrl',
      props: {
        label: 'Proxy Base Url',
        placeholder: 'https://api.apptile.io/rivo-rewards',
      },
    },
    {
      type: 'codeInput',
      name: 'appId',
      props: {
        label: 'Apptile App Id',
        placeholder: '',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string} | undefined) => {
  return (dsConfig: PluginConfigType<RivoRewardsPluginConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, RivoRewardsPluginConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(key)) {
        return {...acc, [key]: dsPluginConfig.get(key)};
      }
      if (dsModelValues && dsModelValues.get(key)) {
        return {...acc, [key]: dsModelValues.get(key)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    } else {
      return value
        ? {
            ...acc,
            [key]: value,
          }
        : acc;
    }
  }, {});
};

export default wrapDatasourceModel({
  name: 'rivo',
  config: {
    proxyUrl: 'https://api.apptile.io/rivo-rewards',
    appId: '',
    queryRunner: 'queryrunner',
  } as RivoRewardsPluginConfigType,

  initDatasource: function* (
    dsModel: any,
    dsConfig: PluginConfigType<RivoRewardsPluginConfigType>,
    dsModelValues: any,
  ) {
    const queryRunner = AjaxQueryRunner();
    const appId = dsModelValues?.get('appId') ?? null;

    queryRunner.initClient(dsModelValues.get('proxyUrl'), config => {
      config.headers = {
        'Content-Type': 'application/json',
        'x-shopify-app-id': appId,
      };
      return config;
    });

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'queryRunner'],
          newValue: queryRunner,
        },
      ],
    };
  },

  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return RivoRewardsApiRecords;
  },

  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      RivoRewardsApiRecords && RivoRewardsApiRecords[queryName] ? RivoRewardsApiRecords[queryName] : null;

    return queryDetails?.editableInputParams ? queryDetails?.editableInputParams : {};
  },

  resolveCredentialConfigs: function (credentials): Partial<RivoRewardsPluginConfigType> | boolean {
    const {proxyUrl, appId} = credentials;
    if (!proxyUrl && !appId) return false;
    return {
      proxyUrl,
      appId,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'rivo';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ): DatasourceQueryReturnValue {
    const queryDetails = RivoRewardsApiRecords[queryName];

    if (!queryDetails) return;

    const {getNextPage, paginationMeta} = options;

    let {endpointResolver, endpoint, contextInputParams} = queryDetails ?? {};
    const contextInputParamsResolver = makeInputParamsResolver(contextInputParams);
    const dsConfigVariables = contextInputParamsResolver(dsConfig, dsModelValues);

    let isReadyToRun = true;
    let typedInputVariables, typedDataVariables;

    if (queryDetails && queryDetails.editableInputParams) {
      typedInputVariables = makeInputVariablesTypeCompatible(inputVariables, queryDetails.editableInputParams);

      if (queryDetails?.checkInputVariabes) {
        isReadyToRun = queryDetails.checkInputVariabes(typedInputVariables);
      }
    }

    if (queryDetails?.isPaginated && queryDetails.paginationResolver && getNextPage) {
      typedInputVariables = queryDetails.paginationResolver(typedInputVariables as Record<string, any>, paginationMeta);
    }

    typedDataVariables = queryDetails.inputResolver
      ? queryDetails.inputResolver(typedInputVariables, dsConfigVariables)
      : typedInputVariables;

    endpoint =
      endpointResolver && endpointResolver(endpoint, {...dsConfigVariables, ...typedDataVariables}, paginationMeta);

    const queryRunner = dsModelValues.get('queryRunner');
    let queryResponse;

    if (!isReadyToRun) {
      queryResponse = {
        errors: {message: 'Missing input variables'},
        data: null,
      };
    } else {
      try {
        queryResponse = yield call(queryRunner.runQuery, queryDetails.queryType, endpoint, typedDataVariables, {
          ...options,
        });
      } catch (error) {
        logger.error('error', JSON.stringify(error));
      }
    }

    const rawData = queryResponse && queryResponse.data ? queryResponse.data : {};
    let transformedData = rawData;
    let queryHasNextPage, paginationDetails;
    if (queryDetails && queryDetails.transformer) {
      const {data, hasNextPage, paginationMeta: pageData} = queryDetails.transformer(rawData);

      transformedData = data;
      queryHasNextPage = hasNextPage;
      paginationDetails = pageData;
    }

    return yield {
      rawData,
      data: transformedData,
      hasNextPage: queryHasNextPage,
      paginationMeta: paginationDetails,
      errors: [],
      hasError: false,
    };
  },
  options: {
    propertySettings,
    pluginListing,
  },
  editors: RivoRewardsEditors,
});
