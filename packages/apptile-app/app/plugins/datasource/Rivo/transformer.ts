import _ from 'lodash';
import {
  AllRewardsTransformedResponse,
  CustomerTransformed,
  PaginationLinks,
  PointsHistoryTransformedResponse,
  RedemptionHistoryTransformedResponse,
  RewardTransformed,
  TransformedResponse,
  VipTierTransform,
} from './types';

const transformRewardData = (data: any): AllRewardsTransformedResponse => {
  const links: PaginationLinks = {
    self: data.links.self,
    next: data.links.next,
    last: data.links.last,
  };

  // Loop through each reward and transform it
  const rewards = data.data.map((item: any) => ({
    id: item.attributes.id,
    name: item.attributes.name,
    enabled: item.attributes.enabled,
    pointsAmount: item.attributes.points_amount,
    pointsType: item.attributes.points_type,
    rewardType: item.attributes.reward_type,
    source: item.attributes.source,
    displayText: item.attributes.pretty_display_rewards,
    minOrderValueInCents: item.attributes.min_order_value_in_cents,
    expiryMonths: item.attributes.expiry_months,
    rewardValue: item.attributes.reward_value,
    redeemedCount: item.attributes.redeemed_count,
    createdAt: item.attributes.created_at,
    updatedAt: item.attributes.updated_at,
    termsOfService: {
      rewardType: item.attributes.terms_of_service.reward_type,
      appliesTo: item.attributes.terms_of_service.applies_to,
      expiryMonths: item.attributes.terms_of_service.expiry_months,
      showTos: item.attributes.terms_of_service.show_tos,
    },
  }));

  return {
    links,
    rewards,
  };
};

const transformRedemptionData = (data: any, id: number): RewardTransformed => {
  return {
    id: id,
    name: data.name,
    pointsAmount: data.points_amount,
    creditsAmount: parseFloat(data.credits_amount),
    appliedAt: data.applied_at,
    code: data.code,
    expiresAt: data.expires_at,
    usedAt: data.used_at,
    rewardDetails: {
      id: data.reward.id,
      name: data.reward.name,
      pointsRequired: data.reward.points_amount,
      rewardType: data.reward.reward_type,
      displayText: data.reward.pretty_display_rewards,
      expiryMonths: data.reward.expiry_months,
      rewardValue: data.reward.reward_value,
      createdAt: data.reward.created_at,
      updatedAt: data.reward.updated_at,
    },
    customerDetails: {
      id: data.customer.id,
      email: data.customer.email,
      fullName: `${data.customer.first_name} ${data.customer.last_name}`,
      loyaltyStatus: data.customer.loyalty_status,
      pointsTally: data.customer.points_tally,
      referralCode: data.customer.referral_code,
      referralUrl: data.customer.referral_url,
      vipTier: data.customer.vip_tier,
    },
  };
};

function extractPaginationParams(url: string) {
  // Step 1: Extract the query string part (after the ?)
  let queryString = url.split('?')[1];

  // Step 2: Split the query string into key-value pairs
  let queryParams = queryString.split('&');

  // Step 3: Initialize an object to store the filters
  let filters = {};

  // Step 4: Loop through the key-value pairs and extract the filters
  queryParams.forEach(param => {
    let [key, value] = param.split('=');

    // Decode key and value if needed (handling special characters)
    key = decodeURIComponent(key);
    value = decodeURIComponent(value);

    // Step 5: Assign values to the filters object, handling nested filters like pagination[]
    if (key.includes('pagination[')) {
      let filterKey = key.match(/pagination\[(.*?)\]/)[1]; // Extract the inner part of the brackets
      filters[filterKey] = value;
    }
  });

  return filters;
}
const transformCustomerData = (data: any): CustomerTransformed => {
  const attributes = data.attributes;

  return {
    id: attributes.id,
    email: attributes.email,
    firstName: attributes.first_name,
    lastName: attributes.last_name,
    acceptsMarketing: attributes.accepts_marketing,
    ordersCount: attributes.orders_count,
    verifiedEmail: attributes.verified_email,
    totalSpent: attributes.total_spent,
    shopifyTags: attributes.shopify_tags,
    loyaltyStatus: attributes.loyalty_status,
    pointsTally: attributes.points_tally,
    creditsTally: attributes.credits_tally,
    dob: attributes.dob,
    dobLastUpdatedAt: attributes.dob_last_updated_at,
    referralUrl: attributes.referral_url,
    referralCode: attributes.referral_code,
    vipTier: attributes.vip_tier,
    pointsExpireAt: attributes.points_expire_at,
  };
};

const transformSingleVipTier = (data: any) => {
  return {
    id: data.attributes.id,
    name: data.attributes.name,
    threshold: data.attributes.threshold,
    perks: data.attributes.perks,
  };
};

const transformEarningRulesData = (data: any): TransformedResponse => {
  const links: PaginationLinks = {
    self: data.links.self,
    next: data.links.next,
    last: data.links.last,
  };

  // Loop over the `data` array and transform each object
  const earningRules = data.data.map((item: any) => ({
    id: item.attributes.id,
    title: item.attributes.title,
    status: item.attributes.status,
    trigger: item.attributes.trigger,
    pointsAmount: item.attributes.points_amount,
    creditsAmount: item.attributes.credits_amount,
    balanceAmount: item.attributes.balance_amount,
    description: item.attributes.description ?? "",
    url: item.attributes.url ?? "",
    prettyEarningsText: item.attributes.pretty_earnings_text,
    pointsType: item.attributes.points_type,
    currencyBaseAmount: item.attributes.currency_base_amount,
    multipliers: item.attributes.multipliers,
    multiBalanceSettingsByTiers: item.attributes.multi_balance_settings_by_tiers,
  }));

  return {
    links,
    earningRules,
  };
};

const transformVipTiers = (data: any): VipTierTransform => {
  const links: PaginationLinks = {
    self: data.links.self,
    next: data.links.next,
    last: data.links.last,
  };

  // Loop over the `data` array and transform each object
  const vipTiers = data.data.map((item: any) => ({
    id: item.attributes.id,
    name: item.attributes.name,
    threshold: item.attributes.threshold,
    perks: item.attributes.perks,
  }));

  return {
    links,
    vipTiers,
  };
};

const TransformRedemptionHistoryData = (data: any): RedemptionHistoryTransformedResponse => {
  const links: PaginationLinks = {
    self: data.links.self,
    next: data.links.next,
    last: data.links.last,
  };
  const redemptionHistory = data.data.map((redemption: any) => {
    const attributes = redemption.attributes;

    return {
      id: attributes.id,
      pointsAmount: attributes.points_amount,
      creditsAmount: attributes.credits_amount,
      appliedAt: attributes.applied_at,
      name: attributes.name,
      code: attributes.code,
      expiresAt: attributes.expires_at,
      usedAt: attributes.used_at,
      refundedAt: attributes.refunded_at,
      revokedAt: attributes.revoked_at,
      reward: {
        id: attributes.reward.id,
        name: attributes.reward.name,
        enabled: attributes.reward.enabled,
        pointsAmount: attributes.reward.points_amount,
        pointsType: attributes.reward.points_type,
        rewardType: attributes.reward.reward_type,
        source: attributes.reward.source,
        prettyDisplayRewards: attributes.reward.pretty_display_rewards,
        minOrderValueInCents: attributes.reward.min_order_value_in_cents,
        expiryMonths: attributes.reward.expiry_months,
        rewardValue: attributes.reward.reward_value,
        redeemedCount: attributes.reward.redeemed_count,
        createdAt: attributes.reward.created_at,
        updatedAt: attributes.reward.updated_at,
        termsOfService: {
          rewardType: attributes.reward.terms_of_service.reward_type,
          appliesTo: attributes.reward.terms_of_service.applies_to,
          expiryMonths: attributes.reward.terms_of_service.expiry_months,
          showTos: attributes.reward.terms_of_service.show_tos,
        },
      },
      customer: {
        id: attributes.customer.id,
        email: attributes.customer.email,
        firstName: attributes.customer.first_name,
        lastName: attributes.customer.last_name,
        acceptsMarketing: attributes.customer.accepts_marketing,
        ordersCount: attributes.customer.orders_count,
        verifiedEmail: attributes.customer.verified_email,
        totalSpent: attributes.customer.total_spent,
        shopifyTags: attributes.customer.shopify_tags,
        loyaltyStatus: attributes.customer.loyalty_status,
        pointsTally: attributes.customer.points_tally,
        creditsTally: attributes.customer.credits_tally,
        dob: attributes.customer.dob,
        dobLastUpdatedAt: attributes.customer.dob_last_updated_at,
        referralUrl: attributes.customer.referral_url,
        referralCode: attributes.customer.referral_code,
        vipTier: attributes.customer.vip_tier,
        pointsExpireAt: attributes.customer.points_expire_at,
      },
    };
  });

  return {
    links,
    redemptionHistory,
  };
};

const TransformPointsEvents = (data: any): PointsHistoryTransformedResponse => {
  const links: PaginationLinks = {
    self: data.links.self,
    next: data.links.next,
    last: data.links.last,
  };
  const pointsHistory = data.data.map((event: any) => {
    const attributes = event.attributes;

    return {
      id: attributes.id,
      pointsAmount: attributes.points_amount,
      creditsAmount: attributes.credits_amount,
      pointsDiff: attributes.points_diff,
      appliedAt: attributes.applied_at,
      internalNote: attributes.internal_note,
      externalNote: attributes.external_note,
      source: attributes.source,
      customer: {
        id: attributes.customer.id,
        email: attributes.customer.email,
        firstName: attributes.customer.first_name,
        lastName: attributes.customer.last_name,
        acceptsMarketing: attributes.customer.accepts_marketing,
        ordersCount: attributes.customer.orders_count,
        verifiedEmail: attributes.customer.verified_email,
        totalSpent: attributes.customer.total_spent,
        shopifyTags: attributes.customer.shopify_tags,
        loyaltyStatus: attributes.customer.loyalty_status,
        pointsTally: attributes.customer.points_tally,
        creditsTally: attributes.customer.credits_tally,
        dob: attributes.customer.dob,
        dobLastUpdatedAt: attributes.customer.dob_last_updated_at,
        referralUrl: attributes.customer.referral_url,
        referralCode: attributes.customer.referral_code,
        vipTier: attributes.customer.vip_tier,
        pointsExpireAt: attributes.customer.points_expire_at,
      },
    };
  });

  return {
    links,
    pointsHistory,
  };
};

export const TransformEarningRules = (response: any) => {
  const tresformedData = transformEarningRulesData(response);

  const currentPaginationParams = extractPaginationParams(tresformedData.links.self);
  const nextPaginationParams = extractPaginationParams(tresformedData.links.next);
  const lastPaginationParams = extractPaginationParams(tresformedData.links.last);

  const hasNextPage = lastPaginationParams.page > currentPaginationParams.page;
  const paginationMeta = {
    currentPage: currentPaginationParams.page,
    totalPages: lastPaginationParams.page,
    nextPage: hasNextPage ? nextPaginationParams.page : lastPaginationParams.page,
  };

  return {
    data: tresformedData.earningRules,
    hasNextPage,
    paginationMeta,
  };
};

export const TransformVipTiers = (response: any) => {
  const tresformedData = transformVipTiers(response);

  const currentPaginationParams = extractPaginationParams(tresformedData.links.self);
  const nextPaginationParams = extractPaginationParams(tresformedData.links.next);
  const lastPaginationParams = extractPaginationParams(tresformedData.links.last);

  const hasNextPage = lastPaginationParams.page > currentPaginationParams.page;
  const paginationMeta = {
    currentPage: currentPaginationParams.page,
    totalPages: lastPaginationParams.page,
    nextPage: hasNextPage ? nextPaginationParams.page : lastPaginationParams.page,
  };

  return {
    data: tresformedData.vipTiers,
    hasNextPage,
    paginationMeta,
  };
};

export const TransformCouponCode = (response: any) => {
  const {data} = response;
  const transformedData = transformRedemptionData(data.attributes, data.id);
  return {data: transformedData};
};

export const TransformSingleCsutomer = (response: any) => {
  const {data} = response;
  const transformedData = transformCustomerData(data);
  return {data: transformedData};
};

export const TransformSingleVipTier = (response: any) => {
  const {data} = response;
  const transformedData = transformSingleVipTier(data);
  return {data: transformedData};
};

export const TransfromAllRewards = (response: any) => {
  const tresformedData = transformRewardData(response);

  const currentPaginationParams = extractPaginationParams(tresformedData.links.self);
  const nextPaginationParams = extractPaginationParams(tresformedData.links.next);
  const lastPaginationParams = extractPaginationParams(tresformedData.links.last);

  const hasNextPage = lastPaginationParams.page > currentPaginationParams.page;
  const paginationMeta = {
    currentPage: currentPaginationParams.page,
    totalPages: lastPaginationParams.page,
    nextPage: hasNextPage ? nextPaginationParams.page : lastPaginationParams.page,
  };

  return {
    data: tresformedData.rewards,
    hasNextPage,
    paginationMeta,
  };
};

export const TransformRedemptionHistory = (response: any) => {
  const tresformedData = TransformRedemptionHistoryData(response);

  const currentPaginationParams = extractPaginationParams(tresformedData.links.self);
  const nextPaginationParams = extractPaginationParams(tresformedData.links.next);
  const lastPaginationParams = extractPaginationParams(tresformedData.links.last);

  const hasNextPage = lastPaginationParams.page > currentPaginationParams.page;
  const paginationMeta = {
    currentPage: currentPaginationParams.page,
    totalPages: lastPaginationParams.page,
    nextPage: hasNextPage ? nextPaginationParams.page : lastPaginationParams.page,
  };

  return {
    data: tresformedData.redemptionHistory,
    hasNextPage,
    paginationMeta,
  };
};

export const TransformPointsHistory = (response: any) => {
  const tresformedData = TransformPointsEvents(response);

  const currentPaginationParams = extractPaginationParams(tresformedData.links.self);
  const nextPaginationParams = extractPaginationParams(tresformedData.links.next);
  const lastPaginationParams = extractPaginationParams(tresformedData.links.last);

  const hasNextPage = lastPaginationParams.page > currentPaginationParams.page;
  const paginationMeta = {
    currentPage: currentPaginationParams.page,
    totalPages: lastPaginationParams.page,
    nextPage: hasNextPage ? nextPaginationParams.page : lastPaginationParams.page,
  };

  return {
    data: tresformedData.pointsHistory,
    hasNextPage,
    paginationMeta,
  };
};
