export interface EarningRuleTransformed {
  id: number;
  title: string;
  status: string;
  trigger: string;
  pointsAmount: number;
  creditsAmount: number;
  balanceAmount: number;
  description: string;
  prettyEarningsText: string;
  pointsType: string;
  currencyBaseAmount: number;
  multipliers: any[];
  multiBalanceSettingsByTiers: object;
  url? : string;
}

export interface PaginationLinks {
  self: string;
  next: string;
  last: string;
}

export interface TransformedResponse {
  links: PaginationLinks;
  earningRules: EarningRuleTransformed[];
}

export interface RewardObject {
  id: number;
  name: string;
  pointsRequired: number;
  rewardType: string;
  displayText: string;
  expiryMonths: number;
  rewardValue: number;
  createdAt: string;
  updatedAt: string;
}
export interface CustomerObj {
  id: number;
  email: string;
  fullName: string;
  loyaltyStatus: string;
  pointsTally: number;
  referralCode: string;
  referralUrl: string;
  vipTier: any;
}
export interface RewardTransformed {
  id: number;
  name: string;
  pointsAmount: number;
  creditsAmount: number;
  appliedAt: string;
  code: string;
  expiresAt: string;
  usedAt: string | null;
  rewardDetails: RewardObject;
  customerDetails: CustomerObj;
}

interface AllRewardTransformed {
  id: number;
  name: string;
  enabled: boolean;
  pointsAmount: number;
  pointsType: string;
  rewardType: string;
  source: string;
  displayText: string;
  minOrderValueInCents: number | null;
  expiryMonths: number | null;
  rewardValue: number;
  redeemedCount: number;
  createdAt: string;
  updatedAt: string;
  termsOfService: {
    rewardType: string;
    appliesTo: string;
    expiryMonths: number | null;
    showTos: boolean;
  };
}

export interface AllRewardsTransformedResponse {
  links: PaginationLinks;
  rewards: AllRewardTransformed[];
}
export interface CustomerTransformed {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  acceptsMarketing: boolean;
  ordersCount: number;
  verifiedEmail: boolean;
  totalSpent: number;
  shopifyTags: string[];
  loyaltyStatus: string;
  pointsTally: number;
  creditsTally: string;
  dob: string | null;
  dobLastUpdatedAt: string | null;
  referralUrl: string;
  referralCode: string;
  vipTier: string | null;
  pointsExpireAt: string | null;
}

export interface RedemptionHistoryTransformed {
  id: number;
  pointsAmount: number;
  creditsAmount: string;
  appliedAt: string;
  name: string;
  code: string;
  expiresAt: string;
  usedAt: string | null;
  refundedAt: string | null;
  revokedAt: string | null;
  reward: {
    id: number;
    name: string;
    enabled: boolean;
    pointsAmount: number;
    pointsType: string;
    rewardType: string;
    source: string;
    prettyDisplayRewards: string;
    minOrderValueInCents: number | null;
    expiryMonths: number | null;
    rewardValue: number;
    redeemedCount: number;
    createdAt: string;
    updatedAt: string;
    termsOfService: {
      rewardType: string;
      appliesTo: string;
      expiryMonths: number | null;
      showTos: boolean;
    };
  };
  customer: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    acceptsMarketing: boolean;
    ordersCount: number;
    verifiedEmail: boolean;
    totalSpent: number;
    shopifyTags: string[];
    loyaltyStatus: string;
    pointsTally: number;
    creditsTally: string;
    dob: string | null;
    dobLastUpdatedAt: string | null;
    referralUrl: string;
    referralCode: string;
    vipTier: string | null;
    pointsExpireAt: string | null;
  };
}
export interface RedemptionHistoryTransformedResponse {
  links: PaginationLinks;
  redemptionHistory: RedemptionHistoryTransformed[];
}

interface PointsEventTransformed {
  id: number;
  pointsAmount: number;
  creditsAmount: string;
  pointsDiff: number;
  appliedAt: string;
  internalNote: string | null;
  externalNote: string;
  source: string;
  customer: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    acceptsMarketing: boolean;
    ordersCount: number;
    verifiedEmail: boolean;
    totalSpent: number;
    shopifyTags: string[];
    loyaltyStatus: string;
    pointsTally: number;
    creditsTally: string;
    dob: string | null;
    dobLastUpdatedAt: string | null;
    referralUrl: string;
    referralCode: string;
    vipTier: string | null;
    pointsExpireAt: string | null;
  };
}

interface SingleVipTier {
  id: number;
  name: string;
  threshold: number;
  perks: []
}

export interface PointsHistoryTransformedResponse {
  links: PaginationLinks;
  pointsHistory: PointsEventTransformed[];
}

export interface VipTierTransform{
  links: PaginationLinks;
  vipTiers: SingleVipTier
}